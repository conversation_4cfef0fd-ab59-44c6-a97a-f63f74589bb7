# 📁 两级文件夹结构设计文档

## 🎯 设计目标

实现支持**两级文件夹结构**和**拖拽排序**的PDF文件管理系统：

1. ✅ **文件夹层次管理** - 创建、删除、重命名文件夹
2. ✅ **文件分类存储** - 文件可以放在不同文件夹中
3. ✅ **拖拽排序支持** - 通过order字段实现自定义排序
4. ✅ **简洁易用设计** - 直观的API和数据结构

## 🏗️ 数据结构设计

### 扩展的 AttachmentDocType

```typescript
interface AttachmentDocType {
    id: string;              // 附件/文件夹ID
    wid: string;             // 工作区ID
    file_name: string;       // 文件名/文件夹名
    type: 'file' | 'folder'; // 类型：文件或文件夹
    parent_id: string;       // 父文件夹ID，根目录为'root'
    order: number;           // 排序顺序，数字越小越靠前
    size?: number;           // 文件大小（字节），仅文件类型
    mime_type?: string;      // MIME类型，仅文件类型
    file_path?: string;      // 文件路径，仅文件类型
    create_at: number;       // 创建时间
    update_at: number;       // 更新时间
}
```

### 关键字段说明

| 字段 | 用途 | 示例 |
|------|------|------|
| `type` | 区分文件和文件夹 | `'file'` 或 `'folder'` |
| `parent_id` | 建立层次关系 | `'root'`、`'folder_123'` |
| `order` | 拖拽排序 | `1`, `2`, `3` |
| `size` | 文件大小（仅文件） | `2048000` |
| `mime_type` | 文件类型（仅文件） | `'application/pdf'` |

### 数据示例

```typescript
// 根目录文件夹
{
    id: 'folder_001',
    wid: 'workspace_123',
    file_name: '我的文档',
    type: 'folder',
    parent_id: 'root',
    order: 1,
    create_at: 1640995200,
    update_at: 1640995200
}

// PDF文件
{
    id: 'file_001',
    wid: 'workspace_123', 
    file_name: '项目报告.pdf',
    type: 'file',
    parent_id: 'folder_001',
    order: 1,
    size: 2048000,
    mime_type: 'application/pdf',
    file_path: '/uploads/project-report.pdf',
    create_at: 1640995200,
    update_at: 1640995200
}
```

## 🔧 服务接口

### FolderService 主要方法

#### 创建文件夹
```typescript
await folderService.createFolder({
    wid: 'workspace_123',
    name: '新文件夹',
    parent_id: 'root' // 可选，默认为根目录
});
```

#### 创建文件记录
```typescript
await folderService.createFile({
    wid: 'workspace_123',
    file_name: 'document.pdf',
    parent_id: 'folder_001',
    size: 1024000,
    mime_type: 'application/pdf',
    file_path: '/uploads/document.pdf'
});
```

#### 获取文件列表
```typescript
// 获取指定文件夹下的内容
const items = await folderService.getList({
    wid: 'workspace_123',
    parent_id: 'folder_001', // 可选，默认根目录
    type: 'file' // 可选，筛选类型
});

// 获取整个工作区的树形数据
const treeData = await folderService.getTreeData('workspace_123');
```

#### 文件/文件夹操作
```typescript
// 重命名
await folderService.rename({
    id: 'folder_001',
    new_name: '重命名的文件夹'
});

// 移动到其他文件夹
await folderService.move({
    id: 'file_001',
    new_parent_id: 'folder_002',
    new_order: 3 // 可选
});

// 批量更新排序
await folderService.updateOrder({
    items: [
        { id: 'file_001', order: 1 },
        { id: 'file_002', order: 2 },
        { id: 'folder_001', order: 3 }
    ]
});

// 删除（文件夹会递归删除子项）
await folderService.delete({ id: 'folder_001' });
```

#### 辅助功能
```typescript
// 获取面包屑路径
const path = await folderService.getFolderPath('workspace_123', 'folder_001');
// 返回: [{ id: 'root', name: '根目录' }, { id: 'folder_001', name: '我的文档' }]

// 搜索文件/文件夹
const results = await folderService.search({
    wid: 'workspace_123',
    keyword: '报告',
    type: 'file' // 可选
});
```

## 🖼️ UI 组件集成

### 在 Menu.tsx 中使用

```typescript
import { folderService, FileTreeNode } from '@/local';

// 将数据转换为FileTreePanel格式
const fileTreeData = useMemo((): FileTreeNode[] => {
    // 从folderService获取数据并转换格式
    const items = await folderService.getTreeData(currentWorkspaceId);
    
    return items.map(item => ({
        id: item.id,
        title: item.file_name,
        isDirectory: item.type === 'folder',
        path: `/${item.file_name}`,
        size: item.size,
        lastModified: new Date(item.create_at * 1000),
        data: {
            isPdf: item.type === 'file',
            pdfInfo: item.type === 'file' ? item : undefined
        }
    }));
}, [currentWorkspaceId]);

// 处理文件夹创建
const handleFileCreate = useCallback(async (parentPath: string, type: 'file' | 'folder') => {
    if (type === 'folder') {
        await folderService.createFolder({
            wid: currentWorkspaceId,
            name: '新建文件夹',
            parent_id: extractParentIdFromPath(parentPath)
        });
    }
}, [currentWorkspaceId]);
```

## 📊 索引优化

新的数据结构包含优化的索引：

```typescript
indexes: [
    'wid',                    // 按工作区查询
    'type',                   // 按类型查询
    'parent_id',              // 按父文件夹查询
    ['wid', 'type'],          // 工作区+类型复合查询
    ['wid', 'parent_id'],     // 工作区+父文件夹复合查询
    ['wid', 'parent_id', 'order'], // 排序查询优化
    ['parent_id', 'order'],   // 文件夹内排序
    ['wid', 'type', 'parent_id'] // 三字段复合查询
]
```

## 🚀 使用示例

### 完整的文件管理流程

```typescript
import { folderService } from '@/local';

// 1. 创建文件夹结构
const projectFolder = await folderService.createFolder({
    wid: 'workspace_123',
    name: '项目文档',
    parent_id: 'root'
});

const subFolder = await folderService.createFolder({
    wid: 'workspace_123', 
    name: '设计稿',
    parent_id: projectFolder.id
});

// 2. 添加文件
await folderService.createFile({
    wid: 'workspace_123',
    file_name: '需求文档.pdf',
    parent_id: projectFolder.id,
    size: 1024000,
    mime_type: 'application/pdf'
});

// 3. 重新排序
await folderService.updateOrder({
    items: [
        { id: subFolder.id, order: 1 },      // 子文件夹排在前面
        { id: 'file_001', order: 2 }         // 文件排在后面
    ]
});

// 4. 获取最终结构
const structure = await folderService.getTreeData('workspace_123');
console.log('文件夹结构:', structure);
```

## ⚠️ 注意事项

1. **数据完整性** - 删除文件夹时会递归删除所有子项
2. **性能考虑** - 大量文件时建议分页查询
3. **事务安全** - 移动文件时要避免循环引用
4. **索引优化** - 复合索引支持高效的层次查询
5. **自动时间戳** - 创建和更新时间会自动生成

## 🎉 功能特性

- ✅ **真正的文件夹管理** - 支持无限层级的文件夹结构
- ✅ **直观的拖拽排序** - 用户可以自由调整文件顺序
- ✅ **简洁的API设计** - 易于理解和使用的接口
- ✅ **高性能查询** - 优化的索引结构支持快速检索
- ✅ **完整的CRUD操作** - 创建、读取、更新、删除全支持 
// 定义创建节点的请求参数类型
export type CreateNodeType = {
  wid: string;          // 工作区id
  content: string;      // 节点内容
  aid?: string;          // 附件id
  color: string;        // 节点颜色
  mark?: string;         // 划词坐标
  x: number;
  y: number;
  pids?: Array<string>;
  type?: "default" | "straight" | "step" | "smoothstep" | "simplebezier"       // 父节点ID
  mid?: string; // 高亮id (临时高亮id可能被对话上下文引用）
  style?: {
    strokeDasharray?: boolean;
    stroke?: string;
  }
}

// 定义关联节点的请求参数类型
export type CreateLinkType = {
  nid: string;       // 节点id
  pids: Array<string>;    // 父节点id数组
  type: "default" | "straight" | "step" | "smoothstep" | "simplebezier"
  label?: string;    // 边标签
};

//删除连线
export type DeleteLinkType = {
  wid: string;       // 工作区id
  eid: string;       // 连线id
}

// 更新节点请求参数类型
export type UpdateNodeType = {
  nid: string;       // 节点id
  title?: string;      // 节点标题
  content?: string;      // 节点内容
  color?: string;        // 节点颜色
  x?: number;
  y?: number;
  type: number;         // 1 标题 2 内容 3 颜色 4 标题+内容 5 坐标
}

export type UpdateNodesPositionType = {
  nid: string;       // 节点id
  x: number;
  y: number;
}

// 更新节点尺寸请求参数类型
export type UpdateNodeSizeType = {
  nid: string;       // 节点id
  width: number;     // 节点宽度
  height: number;    // 节点高度
}
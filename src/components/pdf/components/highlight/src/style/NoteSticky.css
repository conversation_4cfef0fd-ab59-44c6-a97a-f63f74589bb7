.NoteSticky {
    position: absolute;
    z-index: 1000;
}

.NoteSticky__part {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    overflow: auto;
}

.NoteSticky--scrolledTo .NoteSticky__part {
    border: 2px solid #ff6b6b;
}

.NoteSticky__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

.NoteSticky__content {
    max-height: 400px;
    overflow-y: auto;
}

.NoteSticky__content::-webkit-scrollbar {
    width: 6px;
}

.NoteSticky__content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.NoteSticky__content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.NoteSticky__content::-webkit-scrollbar-thumb:hover {
    background: #555;
} 
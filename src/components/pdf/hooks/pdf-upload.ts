import {useCallback} from "react";
import {getWsId} from "@/tools/params.ts";
import {usePdfStore} from "@/store/pdf-store.ts";
import {WorkspaceService} from "@/local/services/workspace-service";
import {FileTreeItem} from "./use-file-manager";

// 创建 WorkspaceService 实例
const workspaceService = new WorkspaceService();

export const usePdfUpload = () => {
    const {addPdfs} = usePdfStore((state) => ({
        addPdfs: state.addPdfs,
    }))
    const wid = getWsId()
    
    const uploadFiles = useCallback(async (files: FileList, targetFolderId?: string, addFiles?: (newFiles: FileTreeItem[]) => void) => {
        if (!wid) return { success: false, error: "未找到工作区ID" }
        
        try {
            // 使用 workspaceService.uploadFiles 替代直接操作
            const result = await workspaceService.uploadFiles({
                wid,
                files: Array.from(files),
                parent_id: targetFolderId || 'root'
            });
            
            // 将结果添加到 PDF Store 和文件列表
            if (result.list.length > 0) {
                // 1. 更新 PDF Store
                const pdfMap = new Map();
                result.list.forEach(item => {
                    pdfMap.set(item.aid, {
                        aid: item.aid,
                        filename: item.filename,
                        url: item.url
                    });
                });
                addPdfs(pdfMap);
                
                // 2. 更新文件列表
                if (addFiles) {
                    const now = Math.floor(Date.now() / 1000);
                    const newFiles: FileTreeItem[] = result.list.map(item => ({
                        id: item.aid,
                        file_name: item.filename,
                        type: 'file',
                        parent_id: targetFolderId || 'root',
                        mime_type: 'application/pdf',
                        file_path: item.url,
                        create_at: now,
                        update_at: now
                    }));
                    
                    // 增量更新文件列表
                    addFiles(newFiles);
                }
                
                return { 
                    success: true, 
                    files: result.list.map(item => ({
                        id: item.aid,
                        filename: item.filename,
                        url: item.url
                    }))
                };
            } else {
                throw new Error("没有文件上传成功");
            }
            
        } catch (e) {
            console.error("文件上传失败:", e);
            return { success: false, error: e instanceof Error ? e.message : "上传失败" };
        }
    }, [wid, addPdfs])
    
    return {
        uploadFiles
    }
}
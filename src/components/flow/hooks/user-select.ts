import { useEffect } from 'react';

/**
 * 自定义Hook：监听shift键状态，直接修改body的user-select样式
 * 按住shift键时禁用文本选择，松开时恢复
 */
export const useShiftUserSelect = () => {
  useEffect(() => {
    let isShiftPressed = false;

    const setUserSelectStyle = (value: string) => {
      const style = document.body.style as any;
      style.userSelect = value;
      style.webkitUserSelect = value; // Safari
      style.mozUserSelect = value; // Firefox
      style.msUserSelect = value; // IE/Edge
      style.webkitTouchCallout = value; // iOS Safari
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.shiftKey && !isShiftPressed) {
        isShiftPressed = true;
        setUserSelectStyle('none');
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (!event.shiftKey && isShiftPressed) {
        isShiftPressed = false;
        setUserSelectStyle('auto');
      }
    };

    // 处理窗口失去焦点的情况
    const handleWindowBlur = () => {
      if (isShiftPressed) {
        isShiftPressed = false;
        setUserSelectStyle('auto');
      }
    };

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    window.addEventListener('blur', handleWindowBlur);

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('blur', handleWindowBlur);
      
      // 确保组件卸载时恢复样式
      setUserSelectStyle('auto');
    };
  }, []);
}; 
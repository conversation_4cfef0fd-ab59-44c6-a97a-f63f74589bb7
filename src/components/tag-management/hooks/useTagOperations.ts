import { useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { useTagStore } from '../stores/useTagStore';

export interface TagOperations {
  // 分组操作
  createGroup: (title: string) => Promise<string>;
  editGroup: (id: string, title: string) => Promise<boolean>;
  deleteGroup: (id: string) => Promise<boolean>;
  
  // 标签操作
  createTag: (groupId: string, name: string) => Promise<string | null>;
  editTag: (groupId: string, tagId: string, name: string) => Promise<boolean>;
  deleteTag: (groupId: string, tagId: string) => Promise<boolean>;
  
  // 拖拽相关操作
  moveTagToGroup: (tagId: string, targetGroupId: string) => Promise<boolean>;
  
  // 批量操作
  deleteMultipleTags: (items: { groupId: string; tagId: string }[]) => Promise<number>;
  moveMultipleTags: (tagIds: string[], targetGroupId: string) => Promise<number>;
  
  // 验证操作
  validateGroupTitle: (title: string, excludeId?: string) => { isValid: boolean; message?: string };
  validateTagName: (groupId: string, name: string, excludeId?: string) => { isValid: boolean; message?: string };
  
  // 排序操作
  reorderGroups: (groupIds: string[]) => Promise<boolean>;
  reorderTags: (groupId: string, tagIds: string[]) => Promise<boolean>;
}

// 全局防重复调用机制（在模块级别，所有实例共享）
const globalMoveOperations = new Set<string>();

export const useTagOperations = (): TagOperations => {
  // 使用全局的防重复引用
  const moveOperationRef = useRef(globalMoveOperations);
  
  // 使用稳定的选择器直接获取函数，避免对象引用不稳定
  const addGroup = useTagStore(state => state.addGroup);
  const updateGroup = useTagStore(state => state.updateGroup);
  const deleteGroup = useTagStore(state => state.deleteGroup);
  const getGroup = useTagStore(state => state.getGroup);
  const addTag = useTagStore(state => state.addTag);
  const updateTag = useTagStore(state => state.updateTag);
  const deleteTagAction = useTagStore(state => state.deleteTag);
  const moveTag = useTagStore(state => state.moveTag);
  const findTagInGroups = useTagStore(state => state.findTagInGroups);
  const searchGroups = useTagStore(state => state.searchGroups);
  const reorderGroupsAction = useTagStore(state => state.reorderGroups);
  const reorderTagsAction = useTagStore(state => state.reorderTags);
  
  // 分组操作
  const createGroup = useCallback(async (title: string): Promise<string> => {
    try {
      const trimmedTitle = title.trim();
      
      // 验证输入
      const validation = validateGroupTitle(trimmedTitle);
      if (!validation.isValid) {
        toast.error(validation.message || '分组名称无效');
        throw new Error(validation.message);
      }
      
      const groupId = await addGroup({ title: trimmedTitle, tags: [] });
      toast.success('分组创建成功');
      return groupId;
    } catch (error) {
      console.error('创建分组失败:', error);
      toast.error('创建分组失败');
      throw error;
    }
  }, [addGroup]);
  
  const editGroup = useCallback(async (id: string, title: string): Promise<boolean> => {
    try {
      const trimmedTitle = title.trim();
      
      // 验证输入
      const validation = validateGroupTitle(trimmedTitle, id);
      if (!validation.isValid) {
        toast.error(validation.message || '分组名称无效');
        return false;
      }
      
      const success = await updateGroup(id, { title: trimmedTitle });
      if (success) {
        toast.success('分组更新成功');
      } else {
        toast.error('分组不存在');
      }
      return success;
    } catch (error) {
      console.error('更新分组失败:', error);
      toast.error('更新分组失败');
      return false;
    }
  }, [updateGroup]);
  
  const deleteGroupOp = useCallback(async (id: string): Promise<boolean> => {
    try {
      const group = getGroup(id);
      if (!group) {
        toast.error('分组不存在');
        return false;
      }
      
      const success = await deleteGroup(id);
      if (success) {
        const tagCount = group.tags.length;
        toast.success(
          tagCount > 0 
            ? `分组删除成功，同时删除了 ${tagCount} 个标签` 
            : '分组删除成功'
        );
      }
      return success;
    } catch (error) {
      console.error('删除分组失败:', error);
      toast.error('删除分组失败');
      return false;
    }
  }, [getGroup, deleteGroup]);
  
  // 标签操作
  const createTag = useCallback(async (groupId: string, name: string): Promise<string | null> => {
    try {
      const trimmedName = name.trim();
      
      // 验证输入
      const validation = validateTagName(groupId, trimmedName);
      if (!validation.isValid) {
        toast.error(validation.message || '标签名称无效');
        return null;
      }
      
      const tagId = await addTag(groupId, { name: trimmedName });
      if (tagId) {
        toast.success('标签创建成功');
      } else {
        toast.error('分组不存在');
      }
      return tagId;
    } catch (error) {
      console.error('创建标签失败:', error);
      toast.error('创建标签失败');
      return null;
    }
  }, [addTag]);
  
  const editTag = useCallback(async (groupId: string, tagId: string, name: string): Promise<boolean> => {
    try {
      const trimmedName = name.trim();
      
      // 验证输入
      const validation = validateTagName(groupId, trimmedName, tagId);
      if (!validation.isValid) {
        toast.error(validation.message || '标签名称无效');
        return false;
      }
      
      const success = await updateTag(groupId, tagId, { name: trimmedName });
      if (success) {
        toast.success('标签更新成功');
      } else {
        toast.error('标签不存在');
      }
      return success;
    } catch (error) {
      console.error('更新标签失败:', error);
      toast.error('更新标签失败');
      return false;
    }
  }, [updateTag]);
  
  const deleteTag = useCallback(async (groupId: string, tagId: string): Promise<boolean> => {
    try {
      const success = await deleteTagAction(groupId, tagId);
      if (success) {
        toast.success('标签删除成功');
      } else {
        toast.error('标签不存在');
      }
      return success;
    } catch (error) {
      console.error('删除标签失败:', error);
      toast.error('删除标签失败');
      return false;
    }
  }, [deleteTagAction]);
  
  // 拖拽相关操作
  const moveTagToGroup = useCallback(async (tagId: string, targetGroupId: string): Promise<boolean> => {
    const operationKey = `${tagId}-${targetGroupId}`;
    
    // 防止重复调用同样的移动操作
    if (moveOperationRef.current.has(operationKey)) {
      return true; // 操作正在进行中，直接返回成功
    }
    
    try {
      // 标记操作开始
      moveOperationRef.current.add(operationKey);
      
      // 查找标签当前所在的分组
      const result = findTagInGroups(tagId);
      if (!result) {
        toast.error('标签不存在');
        return false;
      }
      
      const { group: sourceGroup } = result;
      
      // 检查是否是移动到同一个分组
      if (sourceGroup.id === targetGroupId) {
        return true; // 没有实际移动，但不算错误
      }
      
      // 检查目标分组是否存在
      const targetGroup = getGroup(targetGroupId);
      if (!targetGroup) {
        toast.error('目标分组不存在');
        return false;
      }
      
      const success = await moveTag(tagId, sourceGroup.id, targetGroupId);
      if (success) {
        toast.success(`标签已移动到 "${targetGroup.title}"`);
      }
      return success;
    } catch (error) {
      console.error('移动标签失败:', error);
      toast.error('移动标签失败');
      return false;
    } finally {
      // 清理操作标记
      setTimeout(() => {
        moveOperationRef.current.delete(operationKey);
      }, 100); // 短暂延迟清理，防止快速重复调用
    }
  }, [findTagInGroups, getGroup, moveTag]);
  
  // 批量操作
  const deleteMultipleTags = useCallback(async (items: { groupId: string; tagId: string }[]): Promise<number> => {
    let successCount = 0;
    
    for (const item of items) {
      try {
        const success = await deleteTagAction(item.groupId, item.tagId);
        if (success) {
          successCount++;
        }
      } catch (error) {
        console.error(`删除标签 ${item.tagId} 失败:`, error);
      }
    }
    
    if (successCount > 0) {
      toast.success(`成功删除 ${successCount} 个标签`);
    }
    if (successCount < items.length) {
      toast.warning(`${items.length - successCount} 个标签删除失败`);
    }
    
    return successCount;
  }, [deleteTagAction]);
  
  const moveMultipleTags = useCallback(async (tagIds: string[], targetGroupId: string): Promise<number> => {
    let successCount = 0;
    
    // 检查目标分组是否存在
    const targetGroup = getGroup(targetGroupId);
    if (!targetGroup) {
      toast.error('目标分组不存在');
      return 0;
    }
    
    for (const tagId of tagIds) {
      try {
        const result = findTagInGroups(tagId);
        if (result && result.group.id !== targetGroupId) {
          const success = await moveTag(tagId, result.group.id, targetGroupId);
          if (success) {
            successCount++;
          }
        }
      } catch (error) {
        console.error(`移动标签 ${tagId} 失败:`, error);
      }
    }
    
    if (successCount > 0) {
      toast.success(`成功移动 ${successCount} 个标签到 "${targetGroup.title}"`);
    }
    if (successCount < tagIds.length) {
      toast.warning(`${tagIds.length - successCount} 个标签移动失败`);
    }
    
    return successCount;
  }, [getGroup, findTagInGroups, moveTag]);
  
  // 验证操作
  const validateGroupTitle = useCallback((title: string, excludeId?: string): { isValid: boolean; message?: string } => {
    if (!title || title.trim().length === 0) {
      return { isValid: false, message: '分组名称不能为空' };
    }
    
    if (title.trim().length > 50) {
      return { isValid: false, message: '分组名称不能超过50个字符' };
    }
    
    // 检查重名（如果提供了excludeId，则排除该ID）
    const groups = searchGroups(title.trim());
    const duplicate = groups.find(group => group.id !== excludeId && group.title === title.trim());
    if (duplicate) {
      return { isValid: false, message: '分组名称已存在' };
    }
    
    return { isValid: true };
  }, [searchGroups]);
  
  const validateTagName = useCallback((groupId: string, name: string, excludeId?: string): { isValid: boolean; message?: string } => {
    if (!name || name.trim().length === 0) {
      return { isValid: false, message: '标签名称不能为空' };
    }
    
    if (name.trim().length > 30) {
      return { isValid: false, message: '标签名称不能超过30个字符' };
    }
    
    // 检查分组是否存在
    const group = getGroup(groupId);
    if (!group) {
      return { isValid: false, message: '分组不存在' };
    }
    
    // 检查组内重名（如果提供了excludeId，则排除该ID）
    const duplicate = group.tags.find(tag => tag.id !== excludeId && tag.name === name.trim());
    if (duplicate) {
      return { isValid: false, message: '标签名称在该分组中已存在' };
    }
    
    return { isValid: true };
  }, [getGroup]);
  
  // 排序操作
  const reorderGroups = useCallback(async (groupIds: string[]): Promise<boolean> => {
    try {
      if (!groupIds.length) {
        toast.error('标签组列表不能为空');
        return false;
      }
      
      const success = await reorderGroupsAction(groupIds);
      if (success) {
        toast.success('标签组排序已更新');
      } else {
        toast.error('标签组排序更新失败');
      }
      return success;
    } catch (error) {
      console.error('重排序标签组失败:', error);
      toast.error('重排序标签组失败');
      return false;
    }
  }, [reorderGroupsAction]);
  
  const reorderTags = useCallback(async (groupId: string, tagIds: string[]): Promise<boolean> => {
    try {
      if (!groupId) {
        toast.error('标签组ID不能为空');
        return false;
      }
      
      if (!tagIds.length) {
        toast.error('标签列表不能为空');
        return false;
      }
      
      // 检查标签组是否存在
      const group = getGroup(groupId);
      if (!group) {
        toast.error('标签组不存在');
        return false;
      }
      
      const success = await reorderTagsAction(groupId, tagIds);
      if (success) {
        toast.success(`"${group.title}" 组内标签排序已更新`);
      } else {
        toast.error('标签排序更新失败');
      }
      return success;
    } catch (error) {
      console.error('重排序标签失败:', error);
      toast.error('重排序标签失败');
      return false;
    }
  }, [reorderTagsAction, getGroup]);
  
  return {
    createGroup,
    editGroup,
    deleteGroup: deleteGroupOp,
    createTag,
    editTag,
    deleteTag,
    moveTagToGroup,
    deleteMultipleTags,
    moveMultipleTags,
    validateGroupTitle,
    validateTagName,
    reorderGroups,
    reorderTags,
  };
};
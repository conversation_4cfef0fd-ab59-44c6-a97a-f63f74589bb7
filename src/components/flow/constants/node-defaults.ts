// 节点默认尺寸配置
export const NODE_DEFAULTS = {
    // 默认宽度
    WIDTH: 400,
    // 默认最小高度
    MIN_HEIGHT: 114,
    // 默认最大高度
    MAX_HEIGHT: 354,
    // 调整大小的最小限制
    RESIZE_MIN_WIDTH: 200,
    RESIZE_MIN_HEIGHT: 100,
} as const;

// 数据库默认值，与node-service.ts中的值保持一致
export const DB_DEFAULTS = {
    WIDTH: 400,
    HEIGHT: 114,
} as const;

// 获取节点默认尺寸
export const getDefaultNodeSize = () => ({
    width: NODE_DEFAULTS.WIDTH,
    height: NODE_DEFAULTS.MIN_HEIGHT,
});

// 验证节点尺寸是否有效
export const validateNodeSize = (width: number, height: number) => ({
    width: Math.max(width, NODE_DEFAULTS.RESIZE_MIN_WIDTH),
    height: Math.max(height, NODE_DEFAULTS.RESIZE_MIN_HEIGHT),
});

// 根据内容长度计算节点高度
export const calculateNodeHeight = (content: string) => {
    if (!content || content.trim().length === 0) {
        return NODE_DEFAULTS.MIN_HEIGHT;
    }

    // 基础计算参数
    const BASE_HEIGHT = 60; // 节点头部和边距的基础高度
    const LINE_HEIGHT = 20; // 每行文本的大概高度
    const CHARS_PER_LINE = 35; // 每行大概的字符数（考虑中英文混合）
    
    // 处理不同类型的内容
    const lines = content.split('\n');
    let totalHeight = BASE_HEIGHT;
    
    for (const line of lines) {
        if (line.trim().length === 0) {
            // 空行
            totalHeight += LINE_HEIGHT * 0.5;
        } else if (line.startsWith('![')) {
            // 图片内容，给予更多高度
            totalHeight += LINE_HEIGHT * 3;
        } else {
            // 普通文本，计算需要的行数
            const lineLength = line.length;
            const estimatedLines = Math.ceil(lineLength / CHARS_PER_LINE);
            totalHeight += estimatedLines * LINE_HEIGHT;
        }
    }
    
    // 确保高度在合理范围内
    return Math.max(
        NODE_DEFAULTS.MIN_HEIGHT,
        Math.min(totalHeight, NODE_DEFAULTS.MAX_HEIGHT)
    );
}; 
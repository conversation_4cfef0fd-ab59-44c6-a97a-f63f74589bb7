import type {SelectProps} from "antd";
import {Button, Form, Input, InputRef, message, Modal, Progress, Select, Upload,} from "antd";
import {memo, useEffect, useRef, useState} from "react";
import type {DefaultOptionType} from "antd/es/select";
import {DeleteOutlined, DownOutlined, PlusOutlined, RightOutlined,} from "@ant-design/icons";
import {UploadFile} from "antd/es/upload/interface";
import {createCategory} from "@/api/category";
import styled from "styled-components";
import PdfLargeSrc from "../../assets/images/uploadPdf.png";
import {ConfirmModalWrapper} from "@/components/pop-window/confirm-window";
import {CloseIcon} from "@/components/icons";
import {useHomeStore} from "@/store/home-store";
import {getRandomColor} from "@/tools";
import {useNavigate} from "react-router-dom";
import {createProject, type CreateProjectType} from "@/api/project";

const CustomModal = styled(Modal)`
  display: flex;
  justify-content: center;
  align-items: center;
  .ant-modal-content {
    width: 896.24px;
    height: 744px;
    background: #f1f2ff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .ant-modal-header {
    background: #f1f2ff;
    position: absolute;
    top: 8%;
    left: 50%;
    transform: translateX(-50%);
    .ant-modal-title {
      font-size: 24px;
      font-weight: 300;
      color: #3d56ba;
    }
  }

  .ant-modal-body {
    width: 75%;
    height: 75%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: -70px;
  }
`;
const CustomUpload = styled(Upload)`
  .ant-upload {
    width: 100% !important;
    height: 295px !important;
    background: #e0e2f4;
    border-radius: 8px;
    border-top: 2px dashed var(--border-color);
    border-bottom: 2px dashed var(--border-color);
    overflow: hidden;
    padding: 0;

    // 添加拖拽状态样式
    &.ant-upload-drag-hover {
      background: rgba(0, 0, 0, 0.49);
      border: 2px dashed var(--primary-color);
    }
  }
`;
const CustomSelect = styled(Select)`
  .ant-select-dropdown {
    background: var(--bg-category-tabs);
    border-radius: 8px;
    padding: 8px 0;
  }
`;
const PdfListContainer = styled.div`
  /* 默认情况下，第二个子元素隐藏 */
  & > div:nth-child(2) {
    display: none;
  }

  /* 当悬停在父容器上时，第二个子元素显示 */
  &:hover > div:nth-child(2) {
    display: block;
  }
`;
// 定义文件项的接口，继承自 UploadFile 类型
interface FileItem extends Omit<UploadFile, "status"> {
  uid: string;
  name: string;
  status: string;
  url: string;
  percent?: number;
}

// 组件属性类型定义
type Props = {
  visible: boolean; // 控制对话框显示状态
  onClose: () => void; // 关闭对话框的回调函数
};
type pdfInfo = {
  originalFileName: string;
  uid: string;
  content: Blob; // 添加content字段，用于存储PDF内容
}[];
export const CreateProjectModal = memo(
  ({ visible, onClose }: Props) => {
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState<FileItem[]>([]);
    const [openSelect, setOpenSelect] = useState<boolean>(false);
    const [createCategoryVisible, setCreateCategoryVisible] =
      useState<boolean>(false);
    const [options, setOptions] = useState<SelectProps["options"]>([]);
    const {
      categories,
      categorySelectList,
      currentCategory,
      selectCategoryId,
      setSearching,
      addCategory,
      modifyCategory,
      addProject,
      setSelectCategoryId,
    } = useHomeStore();
    const inputRef = useRef<InputRef>(null);
    const categoryInputRef = useRef<InputRef>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [pdfListInfo, setPdfListInfo] = useState<pdfInfo>([]);
    const [newCategoryName, setNewCategoryName] = useState<string>("");
    const navigate = useNavigate();
    // 初始化选项和默认值
    useEffect(() => {
      if (visible) {
        // 过滤出有效的分类列表
        const validCategories = (categorySelectList || []).filter(
          (item): item is DefaultOptionType =>
            item !== null &&
            typeof item === "object" &&
            "label" in item &&
            "value" in item &&
            item.value !== "-1"
        );

        // 添加"新建分类"选项
        const newOptions = [
          ...validCategories,
          {
            label: "新建分类",
            value: "create_category",
          },
        ];

        setOptions(newOptions);
      }
    }, [visible, categorySelectList]);

    // 重置表单
    useEffect(() => {
      if (!visible) {
        form.resetFields();
      }
    }, [visible]);
    // 聚焦输入框
    useEffect(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, []);
    // 创建并更新本地tag颜色
    const handleCreateCategory = async (value: string) => {
      try {
        const res = (await createCategory({
          cname: value,
        })) as any;
        if (res.code === 0) {
          message.success("新建成功");
          const newCid = res.data.cid as string | number;
          addCategory({
            cid: newCid,
            cname: value,
            wnum: 0,
          });
          setCreateCategoryVisible(false);
          setSelectCategoryId(newCid);
          setNewCategoryName("");
          const colors =
            localStorage.getItem("category_color_list") !== null
              ? JSON.parse(
                  localStorage.getItem("category_color_list") as string
                )
              : [];
          colors.push({
            cid: newCid,
            color: getRandomColor(),
          });
          localStorage.setItem("category_color_list", JSON.stringify(colors));
        } else {
          message.error(res.msg);
        }
      } catch (error) {
        message.error((error as any).msg);
      }
    };

    // 删除上传文件
    const handleDelete = (uid: string) => {
      setFileList((prev) => prev.filter((file) => file.uid !== uid));
      setPdfListInfo((prev) => prev.filter((item) => item.uid !== uid));
    };

    // 更新文件上传进度
    const updateFileProgress = (uid: string, percent: number) => {
      setFileList((prev) =>
        prev.map((file) =>
          file.uid === uid
            ? {
                ...file,
                percent,
                status: percent === 100 ? "done" : "uploading",
              }
            : file
        )
      );
    };
    useEffect(() => {
      const allDone = fileList.every((file) => file.status === "done");
      if (allDone) {
        setLoading(false);
      }
    }, [fileList]);
    useEffect(() => {
      form.setFieldValue(
        "cid",
        selectCategoryId === "-1" ? "0" : selectCategoryId
      );
    }, [currentCategory?.cid, selectCategoryId]);
    useEffect(() => {
      if (fileList.length > 0) {
        form.setFields([
          {
            name: "pdfs",
            errors: [],
          },
        ]);
      }
    }, [fileList]);

    // 添加全局Enter键监听
    useEffect(() => {
      // 只有在弹窗可见时才添加监听器
      if (!visible) return;

      const handleKeyDown = (e: KeyboardEvent) => {
        // 如果是Enter键提交，检查PDF是否为空
        if (e.key === "Enter") {
          if (e.isComposing) {
            return;
          }
          if (fileList.length === 0) {
            form.setFields([
              {
                name: "pdfs",
                validated: false,
                errors: ["pdf不能为空"],
              },
            ]);
            setTimeout(() => {
              form.setFields([
                {
                  name: "pdfs",
                  errors: [],
                },
              ]);
            }, 1000);
          } else {
            // PDF不为空时才正常提交
            form.submit();
          }
        }
      };

      // 添加全局键盘事件监听器
      document.addEventListener("keydown", handleKeyDown);

      // 组件卸载或visible变为false时移除监听器
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }, [visible, form, fileList]);
    return (
      <>
        <CustomModal
          title={<div className="text-[var(--text-primary)]">NEW Project</div>}
          closeIcon={
            <div onClick={onClose}>
              <CloseIcon color="#000" size={24} />
            </div>
          }
          open={visible}
          footer={null}
          onCancel={onClose}
        >
          <Form
            form={form}
            className="w-full"
            wrapperCol={{span: 24}}
            labelCol={{ span: 4 }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                // 如果是Enter键提交，检查PDF是否为空
                if (fileList.length === 0) {
                  form.setFields([
                    {
                      name: "pdfs",
                      validated: false,
                      errors: ["pdf不能为空"],
                    },
                  ]);
                  setTimeout(() => {
                    form.setFields([
                      {
                        name: "pdfs",
                        errors: [],
                      },
                    ]);
                  }, 1000);
                } else {
                  // PDF不为空时才正常提交
                  form.submit();
                }
              }
            }}
            onFinish={async (values) => {
              // 移除PDF文件检查
              // 即使没有PDF文件也允许创建工作区
              
              const body: CreateProjectType = {
                cid: selectCategoryId === "-1" ? "0" : selectCategoryId,
                title: values.title,
                attachs: pdfListInfo.map((item) => ({
                  fname: item.originalFileName,
                  content: item.content // 添加content字段，传递PDF内容
                })),
              };
              
              console.log("提交数据:", {
                ...body,
                attachs: body.attachs
              });
              
              setLoading(true);
              try {
                const data = (await createProject(body)) as unknown as {
                  code: number;
                  data: any;
                  msg: string;
                };
                if (data.code === 0) {
                  message.success(`${values.title}工作区创建成功`);
                  addProject({
                    wid: data.data.wid,
                    cid: selectCategoryId === "-1" ? "0" : selectCategoryId,
                    del_at: 0,
                    last_at: new Date().getTime() / 1000,
                    title: values.title,
                    preview:
                      "https://b0.bdstatic.com/30e1068eb5baee85ff39977076276655.jpg@h_1280",
                    type: 0,
                  });
                  setSearching(false);
                  setLoading(false);
                  const categoryItem = categories.find((category) => {
                    if (selectCategoryId === "-1") {
                      return category.cid === "0";
                    } else {
                      return category.cid === selectCategoryId;
                    }
                  });
                  const allItem = categories.find(
                    (category) => category.cid === "-1"
                  );
                  if (categoryItem && allItem) {
                    modifyCategory({
                      ...categoryItem,
                      wnum: categoryItem.wnum + 1,
                    });
                    modifyCategory({
                      ...allItem,
                      wnum: allItem.wnum + 1,
                    });
                  }
                  onClose();
                  if (pdfListInfo.length !== 0) {
                    navigate(`/workerspace?wid=${data.data.wid}`);
                    setPdfListInfo([]);
                  }
                } else {
                  // 通用错误提示
                  form.setFields([
                    {
                      name: "title",
                      errors: [data.msg],
                    },
                  ]);
                  setLoading(false);
                }
              } catch (error) {
                // 捕获API异常，动态设置表单字段错误
                console.error("创建工作区失败:", error);
                setLoading(false);
                if ((error as any).code === 4100) {
                  form.setFields([
                    {
                      name: "title",
                      errors: [(error as any).msg],
                    },
                  ]);
                  setTimeout(() => {
                    form.setFields([
                      {
                        name: "title",
                        errors: [],
                      },
                    ]);
                  }, 1000);
                } else {
                  message.error((error as any).msg || "创建工作区失败，请重试");
                }
              }
            }}
          >
            <Form.Item
              label={
                <div className="text-[var(--text-form-label)] text-right w-full">
                  项目名称
                </div>
              }
              name={"title"}
              rules={[{ message: "请输入项目名称", required: true }]}
            >
              <div
                  className="flex items-center"
                style={{ width: "530px" }}
              >
                <Input
                  ref={inputRef}
                  className="flex-1 h-[40px]"
                  placeholder="请输入项目名称"
                />
              </div>
            </Form.Item>
            <Form.Item
              label={
                <div className="text-[var(--text-form-label)] text-right w-full">
                  上传pdf
                </div>
              }
              className="mt-12"
              name={"pdfs"}
            >
              <div
                  className="flex flex-col gap-4"
                style={{ width: "530px" }}
              >
                <CustomUpload
                  className="w-full h-[295px] cursor-pointer flex justify-center items-center border-2 border-dashed border-[var(--border-color)] rounded-lg"
                  multiple
                  showUploadList={false}
                  accept=".pdf"
                  listType="picture"
                  beforeUpload={async (file, files) => {
                    if (!file.type.includes("pdf")) {
                      form.setFields([
                        {
                          name: "pdfs",
                          errors: ["只能上传pdf文件"],
                        },
                      ]);
                      setTimeout(() => {
                        form.setFields([
                          {
                            name: "pdfs",
                            errors: [],
                          },
                        ]);
                      }, 1000);
                      return Upload.LIST_IGNORE;
                    }
                    try {
                      const tempfiles = fileList;
                      // 立即更新文件列表，不等待customRequest

                      if (tempfiles.length < 10) {
                        tempfiles.push({
                          uid: file.uid,
                          name: file.name,
                          status: "uploading",
                          url: URL.createObjectURL(file),
                          percent: 0,
                        });
                        setFileList([...tempfiles]);
                        return true; // 继续上传
                      } else {
                        form.setFields([
                          {
                            name: "pdfs",
                            errors: ["只能上传10个pdf文件"],
                          },
                        ]);
                        setTimeout(() => {
                          form.setFields([
                            {
                              name: "pdfs",
                              errors: [],
                            },
                          ]);
                        }, 1000);
                        return Upload.LIST_IGNORE;
                      }
                    } catch (error) {
                      console.error("准备上传失败:", error);
                      message.error("准备上传失败，请重试");
                      return Upload.LIST_IGNORE;
                    }
                  }}
                  onChange={(info) => {
                    // 移除onChange中的fileList设置，因为我们在beforeUpload中已经处理
                    if (info.file.status === "removed") {
                      setFileList((prev) =>
                        prev.filter((item) => item.uid !== info.file.uid)
                      );
                    }
                  }}
                  customRequest={async ({ file }) => {
                    // 上传前检查当前文件数量，如果已达到10个则直接返回
                    if (pdfListInfo.length >= 10) {
                      form.setFields([
                        {
                          name: "pdfs",
                          errors: ["只能上传10个pdf文件"],
                        },
                      ]);
                      setTimeout(() => {
                        form.setFields([
                          {
                            name: "pdfs",
                            errors: [],
                          },
                        ]);
                      }, 1000);
                      return;
                    }

                    try {
                      
                      // 模拟上传进度
                      let percent = 0;
                      const interval = setInterval(() => {
                        percent += 10;
                        updateFileProgress((file as any).uid, percent);
                        
                        if (percent >= 100) {
                          clearInterval(interval);
                          // 添加到pdfListInfo
                          setPdfListInfo((prev) => [
                            ...prev,
                            {
                              originalFileName: (file as File).name,
                              uid: (file as any).uid,
                              content: file as Blob, // 强制转换为Blob类型
                            },
                          ]);
                        }
                      }, 100);
                    } catch (error) {
                      console.error("读取文件内容失败:", error);
                      message.error("读取文件内容失败，请重试");
                      updateFileProgress((file as any).uid, 0);
                      setFileList((prev) => 
                        prev.map(item => 
                          item.uid === (file as any).uid 
                            ? {...item, status: 'error'} 
                            : item
                        )
                      );
                    }
                  }}
                >
                  {fileList.length === 0 ? (
                    <div
                      className="w-full h-full flex flex-col justify-center items-center gap-2"
                      onClick={() => {
                        // 不再需要获取OSS签名
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.add("bg-black/30");
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.remove("bg-black/30");
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.remove("bg-black/30");
                        // 继续上传操作会通过Upload组件的属性自动处理
                      }}
                    >
                      <img
                        src={PdfLargeSrc}
                        alt="pdf-large"
                        className="w-24 h-24"
                      />
                      <div className="text-[#97a3da] flex flex-col items-center">
                        <div>点击或拖拽多个PDF到此处</div>
                        <div
                          className="text-[12px]"
                          style={{
                            fontStyle: "italic",
                          }}
                        >
                          最多上传10个PDF
                        </div>
                      </div>
                    </div>
                  ) : (
                    <PdfListContainer
                      className="w-full h-full scrollbar-custom pl-[18px] pr-2.5 p-[30px] relative"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.add("bg-black/30");
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.remove("bg-black/30");
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.remove("bg-black/30");
                        // 继续上传操作会通过Upload组件的属性自动处理
                      }}
                    >
                      <div className="w-full h-full pl-[18px] pr-2.5 p-[30px] scrollbar-custom">
                        {fileList.map((file) => (
                          <div
                            key={file.uid}
                            className="w-full h-[46px] flex justify-between items-center gap-10 rounded-xl hover:bg-[var(--bg-upload)]"
                          >
                            <div className="flex-1 flex justify-around items-center gap-2.5">
                              <img
                                src={PdfLargeSrc}
                                alt="pdf"
                                className="w-8 inline-block"
                              />
                              <div className="w-[200px] text-sm mb-1 text-[var(--text-primary)] truncate">
                                {file.name}12321323213213213213213
                              </div>
                            </div>
                            <div className="flex-1  flex items-center justify-around  gap-4 w-full">
                              <Progress
                                className="w-full"
                                percent={file.percent || 0}
                                size="small"
                                showInfo={false}
                                status={
                                  file.status === "error"
                                    ? "exception"
                                    : file.status === "done"
                                    ? "success"
                                    : "active"
                                }
                              />
                              <div className="text-xs text-red mt-1 text-center">
                                {file.status === "error"
                                  ? "上传失败"
                                  : file.status === "done"
                                  ? "100%"
                                  : `${Math.round(file.percent || 0)}%`}
                              </div>
                              <div>
                                <DeleteOutlined
                                  className="text-[var(--text-primary)] cursor-pointer hover:text-[var(--primary-color)]"
                                  onClick={() => {
                                    handleDelete(file.uid);
                                    //
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="absolute bottom-2.5 left-1/2 -translate-x-1/2">
                        <Button
                          type="primary"
                          onClick={async (e) => {
                            e.stopPropagation();

                            // 检查文件数量限制
                            if (fileList.length >= 10) {
                              message.error("最多只能上传10个PDF文件");
                              return;
                            }

                            // 创建一个完全独立的文件选择器
                            const input = document.createElement("input");
                            input.type = "file";
                            input.multiple = true;
                            input.accept = ".pdf";
                            input.style.display = "none";

                            input.onchange = async (event) => {
                              const target = event.target as HTMLInputElement;
                              if (target.files && target.files.length > 0) {
                                // 计算可以上传的文件数量
                                const remainingSlots = 10 - fileList.length;
                                const filesToProcess = Array.from(
                                  target.files
                                ).slice(0, remainingSlots);

                                // 如果选择的文件过多，显示提示
                                if (target.files.length > remainingSlots) {
                                  message.warning(
                                    `已达到上传上限，只会上传前${remainingSlots}个文件`
                                  );
                                }

                                // 处理每个选中的文件
                                for (const file of filesToProcess) {
                                  if (!file.type.includes("pdf")) {
                                    message.error("只能上传PDF文件！");
                                    continue;
                                  }

                                  // 生成唯一ID
                                  const uid = crypto.randomUUID();

                                  // 更新文件列表UI
                                  setFileList((prev) => [
                                    ...prev,
                                    {
                                      uid,
                                      name: file.name,
                                      status: "uploading",
                                      url: URL.createObjectURL(file),
                                      percent: 0,
                                    },
                                  ]);

                                  try {
                                    // 模拟上传进度
                                    let percent = 0;
                                    const interval = setInterval(() => {
                                      percent += 10;
                                      updateFileProgress(uid, percent);
                                      
                                      if (percent >= 100) {
                                        clearInterval(interval);
                                        
                                        // 更新PDF信息列表
                                        setPdfListInfo((prev) => [
                                          ...prev,
                                          {
                                            originalFileName: file.name,
                                            uid,
                                            content: file as Blob, // 强制转换为Blob类型
                                          },
                                        ]);
                                      }
                                    }, 100);
                                  } catch (error) {
                                    console.error("读取文件内容失败:", error);
                                    message.error(`读取文件 ${file.name} 内容失败，请重试`);
                                    updateFileProgress(uid, 0);
                                    setFileList((prev) => 
                                      prev.map(item => 
                                        item.uid === uid 
                                          ? {...item, status: 'error'} 
                                          : item
                                      )
                                    );
                                  }
                                }
                              }

                              // 清理：从DOM中移除临时输入元素
                              document.body.removeChild(input);
                            };

                            // 添加到DOM并触发点击
                            document.body.appendChild(input);
                            input.click();
                          }}
                        >
                          继续上传
                        </Button>
                      </div>
                    </PdfListContainer>
                  )}
                </CustomUpload>
              </div>
            </Form.Item>
            <Form.Item
              label={
                <div className="text-[var(--text-form-label)] text-right w-full">
                  选择分类
                </div>
              }
              name={"cid"}
              className="mt-12"
            >
              <div style={{width: "530px"}}>
                <CustomSelect
                  className="w-full h-[40px]"
                  options={options}
                  dropdownStyle={{
                    padding: 0,
                    background: "var(--bg-category-tabs)",
                    borderRadius: "8px",
                  }}
                  suffixIcon={openSelect ? <DownOutlined /> : <RightOutlined />}
                  defaultValue={
                    selectCategoryId === "-1" ? "0" : selectCategoryId
                  }
                  value={selectCategoryId === "-1" ? "0" : selectCategoryId}
                  onChange={(value) => {
                    if (value === "create_category") {
                      return;
                    }
                    setSelectCategoryId(value as string | number);
                  }}
                  onDropdownVisibleChange={(visible) => {
                    setOpenSelect(visible);
                  }}
                  optionRender={(option) => {
                    if (option.value === "create_category") {
                      return (
                        <div
                          className="flex items-center gap-2 text-[var(--primary-color)] py-2 px-3"
                          onMouseDown={(e) => {
                            e.preventDefault();
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            categoryInputRef.current?.focus();
                            setCreateCategoryVisible(true);
                          }}
                        >
                          <div>
                            <PlusOutlined />
                            <span>新建分类</span>
                          </div>
                        </div>
                      );
                    }
                    return (
                      <div className="py-2 px-3 text-[var(--text-primary)]">
                        {option.label}
                      </div>
                    );
                  }}
                  onSelect={(value) => {
                    if (value === "create_category") {
                      return;
                    }
                  }}
                />
              </div>
            </Form.Item>
            <div className="absolute bottom-14 left-1/2 -translate-x-1/2">
              <Button
                className="w-[150px] h-[46px] bg-[var(--text-primary)]"
                type="primary"
                htmlType="submit"
                disabled={loading}
                loading={loading}
              >
                新建
              </Button>
            </div>
          </Form>
        </CustomModal>
        <ConfirmModalWrapper
          confirmOpen={createCategoryVisible}
          onClose={() => setCreateCategoryVisible(false)}
          onConfirmHandle={() => {
            handleCreateCategory(newCategoryName);
          }}
          handleType="Edit"
          title={<div>新建分类</div>}
          component={
            <Input
              className="w-[300px] h-[40px] bg-[var(--bg-primary)] border-[1px] border-[var(--border-color)] rounded-[4px] pl-4"
              ref={categoryInputRef}
              placeholder="请输入分类名称"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  if (e.nativeEvent.isComposing) {
                    return;
                  } else {
                    handleCreateCategory(newCategoryName);
                  }
                }
              }}
            />
          }
        />
      </>
    );
  },
  (prevProps, nextProps) => prevProps.visible === nextProps.visible
);

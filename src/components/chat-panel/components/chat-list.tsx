import { memo, useEffect, useRef, useState } from "react";
import { Spin, Tooltip } from "antd";
import { Markdown } from "@/components/markdown";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatInput } from "./chat-input";
import { throttle } from "lodash";
import { getSessionDetail } from "@/api/chat";
import { useChatPad } from "@/components/chat-panel/hooks/use-chat-pad";
import { ChatToNodeIcon, NotionIcon } from "@/components/icons";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { processContent } from '@/tools/parseMarkdown'
import styled from "styled-components";
import { useNodeAdd } from "@/components/flow/hooks/node-add";
import { useNoteStore } from "@/store/note-store";
/**
 * 聊天列表组件
 * 用于显示聊天历史记录和当前正在生成的回答
 * 使用memo优化性能，避免不必要的重渲染
 */

export const ChatList: React.FC = memo(() => {
  // 聊天列表容器的引用，用于控制滚动
  const chatListRef = useRef<HTMLDivElement>(null);
  // 预处理内容


  const {
    currentQuestionInStream,
    userInput,
    sessionId,
    chatList,
    responseStream,
    isFetchStream,
    session_detail_list,
    addChat,
    updateSessionDetail,
  } = useChatStore();
  const { notePanelOpen, toggleNotePanel } = usePanelOpenStore();
  const [scrollLoading, setScrollLoading] = useState(false);
  const { handleAddNode } = useNodeAdd()
  const { setInsertContent } = useNoteStore();
  
  // 监听 responseStream 的变化，保持滚动到底部
  useEffect(() => {
    const scrollToBottom = () => {
      if (!chatListRef.current) return;
      const element = chatListRef.current;
      // 检查是否接近底部
      const isNearBottom =
        element.scrollHeight - element.scrollTop - element.clientHeight < 200;

      // 自动滚动逻辑
      if ((isFetchStream && isNearBottom) || !isFetchStream) {
        // 使用渐进式滚动而非原生smooth效果
        const targetScrollTop = element.scrollHeight;
        const startScrollTop = element.scrollTop;
        const distance = targetScrollTop - startScrollTop;

        // 如果距离很小，直接滚动到底部
        if (distance < 50) {
          element.scrollTop = targetScrollTop;
          return;
        }

        // 否则使用自定义动画
        let start: number | null = null;
        const duration = 300; // 滚动持续时间，单位毫秒

        const step = (timestamp: number) => {
          if (!start) start = timestamp;
          const elapsed = timestamp - start;
          const progress = Math.min(elapsed / duration, 1);

          // 使用easeOutQuad缓动函数，开始快结束慢
          const easeProgress = 1 - (1 - progress) * (1 - progress);

          // 重新获取目标位置，以防在动画过程中内容高度变化
          const updatedTargetScrollTop = element.scrollHeight;
          const updatedDistance = updatedTargetScrollTop - startScrollTop;

          element.scrollTop = startScrollTop + updatedDistance * easeProgress;

          if (progress < 1) {
            window.requestAnimationFrame(step);
          }
        };

        window.requestAnimationFrame(step);
      }
    };

    // 立即执行
    scrollToBottom();

    // 添加一个延迟执行，确保在内容渲染后再次滚动
    const timer = setTimeout(scrollToBottom, 50);

    return () => {
      clearTimeout(timer);
    };
  }, [responseStream, isFetchStream]);

  // 初次加载时滚动到底部
  // useEffect(() => {
  //   return
  //   if (!chatListRef.current || !chatList?.length) return;

  //   // 只在初次加载或聊天列表变短时（非向上加载更多）滚动到底部
  //   if (chatList.length <= 10) {
  //     const element = chatListRef.current;
  //     element.scrollTop = element.scrollHeight;
  //   }
  // }, [chatList]);
  // 滚动加载更多聊天记录
  useEffect(() => {
    if (!chatListRef.current || !chatList?.length) return;
    const onScrollLoad = throttle(async (e) => {
      if (scrollLoading) {
        return; // 如果正在加载，直接返回，不执行后续代码
      }

      if (e.target.scrollTop < 100) {
        if (chatList.length % 10 !== 0) {
          return; // 如果聊天记录数量不是 10 的倍数，不执行后续代码
        } else {
          setScrollLoading(true); // 设置加载状态为 true

          // 这里可以调用 getSessionDetail 加载更多历史记录
          const rid = chatList[0].rid;
          try {
            const res = await getSessionDetail({
              sid: sessionId as string,
              rid: Number(rid),
              page_size: 10,
            });
            if ((res as any).code === 0) {
              if (res.data.chat && res.data.chat.length > 0) {
                addChat(res.data.chat);
                const currentSesstionDetail = session_detail_list.find(
                  (item) => item.sid === sessionId
                );
                if (currentSesstionDetail) {
                  currentSesstionDetail.chatList = [
                    ...currentSesstionDetail.chatList,
                    ...res.data.chat,
                  ];
                  updateSessionDetail(currentSesstionDetail);
                }
              }
            }
          } catch (error) {
            console.log(error);
          } finally {
            // 添加延迟效果，让加载动画显示足够长的时间
            setTimeout(() => {
              // 无论请求成功还是失败，都将加载状态设置回 false
              setScrollLoading(false);
            }, 500); // 500毫秒的延迟，可以根据需要调整
          }
        }
      }
    }, 1000);

    const currentRef = chatListRef.current;
    currentRef.addEventListener("scroll", onScrollLoad);

    // 清理函数，组件卸载时移除事件监听器
    return () => {
      currentRef.removeEventListener("scroll", onScrollLoad);
      // 取消节流函数中可能的待处理调用
      onScrollLoad.cancel?.();
    };
  }, [chatList, scrollLoading, sessionId]); // 添加 scrollLoading 和 sessionId 作为依赖项

  if (sessionId === 0) {
    return <EmptyWords />
  }

  return (
    <div
      ref={chatListRef}
      className="flex-1 justify-end overflow-x-hidden overflow-y-auto max-h-full pb-10"
    >
      {scrollLoading && (
        <div className="flex flex-col justify-center items-center w-full h-10 overflow-x-hidden">
          <Spin spinning={scrollLoading} />
          <span className="text-gray-500 text-center text-[12px]">
            数据加载中，请稍后
          </span>
        </div>
      )}
      {chatList.length > 0 &&
        chatList.map((chat, index) => {
          const currentRefs = chat.refs
            ? chat.refs.map((ref) => ({
              type: ref.type,
              id: ref.id,
              title: ref.title,
              content: ref.content,
            }))
            : [];

          return (
            <div
              key={chat?.rid + `${chat?.question}`}
              className="border border-gray-200 mb-2.5  rounded-md "
            >
              {/* 聊天输入组件，用于显示问题和允许编辑 */}
              <ChatInput
                rid={chat?.rid}
                question={chat?.question}
                currentRefs={currentRefs}
                context={chat}
              />
              {/* 显示AI回答 */}
              <div className="mt-2.5">
                {/* 使用Markdown组件渲染回答内容 */}
                <Markdown content={chat.answer} />
                {/* 操作按钮区域 */}
                <div className="h-10 flex justify-between items-center mt-4 p-2">
                  <div />
                  <div className="h-full text-[#A99CF5] flex items-center gap-2.5 justify-around cursor-pointer">
                    {/* 转到记事本按钮 */}
                    <Tooltip title="转到记事本">
                      <div
                        className="flex items-center"
                        onClick={() => {
                          // 调用importChatToNotepad函数，将聊天内容追加到记事本
                          if (!notePanelOpen) {
                            toggleNotePanel(true);
                          }
                          // chat.answer && importChatToNotepad(processContent(chat.answer));
                          chat.answer && setInsertContent(`${chat.answer}\n\n (来源: 引用chat) `);
                          
                        }}
                      >
                        <NotionIcon size={30} color="#A99CF5" />{" "}
                        {/* <span></span> */}
                        {/* ToNotePad */}
                      </div>
                    </Tooltip>
                    <div className="w-[2px] h-[60%] bg-[#A99CF5] cursor-pointer rounded-full" />
                    {/* 创建节点按钮 */}
                    <Tooltip title="创建节点">
                      <div
                        className="flex items-center"
                        onClick={() => {
                          handleAddNode({
                            setCenter: true,
                            content: chat.answer,
                          })
                        }}

                      >
                        <ChatToNodeIcon size={30} color="#A99CF5" />{" "}
                        <div></div>
                        {/* ToNode */}
                      </div>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </div>
          );
        })}


      {isFetchStream && <>
        <div className="border border-gray-200 mb-2.5  rounded-md py-2.5">
          <ChatInput
            inStream
            rid={currentQuestionInStream?.rid as string}
            question={currentQuestionInStream?.question}
            currentRefs={currentQuestionInStream?.Refs}
          >
            <Markdown content={responseStream} />
          </ChatInput>
      </div>
      <Spin spinning={isFetchStream} />
      </>
      }
    </div>
  );
});

const EmptyWords = () => {
  return <EmptyWrapper
    className="flex-1 justify-end overflow-y-auto max-h-full"
  >
    <p className='text-center text-lg font-semibold' >
      欢迎开启知识管理新体验！
    </p>
    <p className="text-sm">
      <p>我是知识管理生态的 AI 智能助手，依托资源库、节点等模块联动,</p>
      <p>支持跨形态问答、智能关联等，为你高效梳理知识、辅助创作。</p>
    </p>
    <p className="text-sm"></p>
    <p className="text-sm">你可以跨文档找答案、借助节点生成思路、做笔记智能提炼….</p>
    <p className='text-center text-lg font-semibold'>
      把知识“用活”，让创作、沉淀更轻松
    </p>
  </EmptyWrapper>
}

const EmptyWrapper = styled.div`
  > p {
    text-align: center;
    padding: 3px;
    box-sizing: border-box;
  }
`

// {/* 最新的问答对（如果存在响应流） */}
// {isFetchStream && (
//   <div className="animate-fade-slide-up">
//     {/* 最新的问题 */}
//     <div className="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 hover:border-gray-200 transition-all animate-fade-in">
//       <div className="flex items-center gap-2 mb-2">
//         <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
//           <span className="text-blue-600 text-sm">U</span>
//         </div>
//         <div className="font-medium text-gray-700">User</div>
//       </div>
//       <div className="mt-2 text-gray-800 pl-10">{userInput}</div>
//     </div>
//     {/* 正在生成的回答 */}
//     <div className="p-4 animate-fade-in-delay">
//       <div className="flex items-center gap-2 mb-2">
//         <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
//           <span className="text-purple-600 text-sm">A</span>
//         </div>
//         <div className="font-medium text-gray-700">Assistant</div>
//       </div>
//       {/* 使用Markdown组件渲染流式响应内容 */}
//       <div>
//         <Markdown content={responseStream} />
//         {/* 加载中指示器 */}
//         <div className="text-center mt-2">
//           <Spin size="small" />
//         </div>
//       </div>
//     </div>
//   </div>
// )}
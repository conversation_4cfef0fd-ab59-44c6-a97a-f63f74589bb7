import {nodeData} from "@/store/flow-store.ts";
import {Input, InputRef} from "antd";
import {useEffect, useRef, useState} from "react";
import useNodeEdit from "@/components/flow/hooks/node-edit.ts";

const NodeTitle = ({nid, data, editMode, setEditMode}: {
    nid: string,
    data: nodeData,
    editMode: number,
    setEditMode: (editMode: number) => void
}) => {
    const {onNodeEdit} = useNodeEdit()
    const [title, setTitle] = useState(data.title)
    const inputRef = useRef<InputRef>(null);
    useEffect(() => {
        inputRef.current?.focus({
            cursor: "end",
        });
    }, [editMode]);
    useEffect(() => {
        setTitle(data.title)
    }, [data.title]);
    if (editMode === 1) {
        return (
            <Input
                ref={inputRef}
                className="h-[30px] px-4"
                value={title}
                placeholder="请输入标题"
                onChange={(e) => setTitle(e.target.value)}
                onBlur={() => {
                    onNodeEdit({id: nid, title})
                }}
                onPressEnter={() => {
                    onNodeEdit({id: nid, title})
                }}
            />
        )
    } else {
        return (
            <div className="h-[30px] px-4 flex items-center" style={{backgroundColor: data.color}}
                 onDoubleClick={() => setEditMode(1)}>{title}</div>
        )
    }
}

export default NodeTitle
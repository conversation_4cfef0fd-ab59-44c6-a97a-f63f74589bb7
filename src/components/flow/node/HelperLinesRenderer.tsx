import React from 'react';
import { HelperLine } from '../hooks/use-helper-lines';
import { useStore } from '@xyflow/react';

interface HelperLinesRendererProps {
  helperLines: HelperLine[];
}

export const HelperLinesRenderer: React.FC<HelperLinesRendererProps> = ({ helperLines }) => {
  const transform = useStore((state) => state.transform);
  const [x, y, zoom] = transform;

  return (
    <svg className="react-flow__helper-lines">
      {helperLines.map((line) => {
        if (line.orientation === 'vertical') {
          return (
            <line
              key={line.id}
              className="react-flow__helper-line"
              x1={line.position * zoom + x}
              y1={0}
              x2={line.position * zoom + x}
              y2="100%"
            />
          );
        } else {
          return (
            <line
              key={line.id}
              className="react-flow__helper-line"
              x1={0}
              y1={line.position * zoom + y}
              x2="100%"
              y2={line.position * zoom + y}
            />
          );
        }
      })}
    </svg>
  );
}; 
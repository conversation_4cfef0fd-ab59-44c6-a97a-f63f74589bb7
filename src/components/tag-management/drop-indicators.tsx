import React from 'react';
import { ChevronDown, Target } from 'lucide-react';

/**
 * 拖放指示器组件 - 提供清晰的视觉反馈
 */

interface DropIndicatorProps {
  isActive: boolean;
  position: 'top' | 'bottom' | 'center';
  type: 'tag' | 'group';
  className?: string;
}

/**
 * 通用拖放指示器
 */
export const DropIndicator: React.FC<DropIndicatorProps> = ({ 
  isActive, 
  position, 
  type, 
  className = '' 
}) => {
  if (!isActive) return null;

  const baseClasses = `
    absolute z-50 pointer-events-none transition-all duration-200 ease-out
    ${type === 'tag' ? 'h-0.5' : 'h-1'}
  `;

  const colorClasses = type === 'tag' 
    ? 'bg-blue-400 shadow-lg shadow-blue-200' 
    : 'bg-green-400 shadow-lg shadow-green-200';

  const positionClasses = {
    top: '-top-1 left-0 right-0',
    bottom: '-bottom-1 left-0 right-0', 
    center: 'top-1/2 left-0 right-0 -translate-y-1/2'
  }[position];

  return (
    <div className={`${baseClasses} ${colorClasses} ${positionClasses} ${className}`}>
      {/* 发光效果 */}
      <div className={`absolute inset-0 ${colorClasses} animate-pulse opacity-60`} />
      
      {/* 插入点指示器 */}
      {position !== 'center' && (
        <div className={`
          absolute ${position === 'top' ? '-top-1' : '-bottom-1'} 
          left-2 w-2 h-2 rounded-full 
          ${colorClasses} border-2 border-white
          animate-bounce
        `} />
      )}
    </div>
  );
};

/**
 * 标签插入指示器
 */
interface TagInsertIndicatorProps {
  isVisible: boolean;
  position: 'before' | 'after';
  targetRect?: DOMRect;
}

export const TagInsertIndicator: React.FC<TagInsertIndicatorProps> = ({
  isVisible,
  position,
  targetRect
}) => {
  if (!isVisible || !targetRect) return null;

  return (
    <div 
      className="fixed z-50 pointer-events-none"
      style={{
        left: position === 'before' ? targetRect.left - 4 : targetRect.right + 4,
        top: targetRect.top,
        height: targetRect.height,
        width: 2
      }}
    >
      <div className="w-full h-full bg-blue-500 rounded-full shadow-lg animate-pulse">
        <div className="absolute -top-1 -left-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white animate-bounce" />
        <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white animate-bounce" />
      </div>
    </div>
  );
};

/**
 * 分组拖放区域指示器
 */
interface GroupDropZoneIndicatorProps {
  isActive: boolean;
  isEmpty: boolean;
  groupTitle: string;
}

export const GroupDropZoneIndicator: React.FC<GroupDropZoneIndicatorProps> = ({
  isActive,
  isEmpty,
  groupTitle
}) => {
  if (!isActive) return null;

  return (
    <div className={`
      absolute inset-0 z-10 pointer-events-none
      border-2 border-dashed border-green-400 bg-green-50/80 rounded-lg
      transition-all duration-300 ease-out
      ${isActive ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
    `}>
      {/* 中心指示器 */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="flex flex-col items-center text-green-600 animate-pulse">
          <Target className="w-8 h-8 mb-2" />
          <div className="text-sm font-medium">
            {isEmpty ? `添加到 "${groupTitle}"` : `合并到 "${groupTitle}"`}
          </div>
          <ChevronDown className="w-4 h-4 mt-1 animate-bounce" />
        </div>
      </div>

      {/* 角落指示器 */}
      {[
        'top-2 left-2',
        'top-2 right-2', 
        'bottom-2 left-2',
        'bottom-2 right-2'
      ].map((position, index) => (
        <div 
          key={index}
          className={`
            absolute ${position} w-3 h-3 bg-green-400 rounded-full
            animate-ping opacity-75
          `}
          style={{ animationDelay: `${index * 0.2}s` }}
        />
      ))}
    </div>
  );
};

/**
 * 分组排序指示器
 */
interface GroupSortIndicatorProps {
  isActive: boolean;
  position: 'above' | 'below';
  groupTitle: string;
}

export const GroupSortIndicator: React.FC<GroupSortIndicatorProps> = ({
  isActive,
  position,
  groupTitle
}) => {
  if (!isActive) return null;

  const isAbove = position === 'above';

  return (
    <div className={`
      absolute z-20 left-0 right-0 pointer-events-none
      ${isAbove ? '-top-3' : '-bottom-3'}
      flex items-center justify-center
    `}>
      {/* 插入线 */}
      <div className="flex-1 h-1 bg-green-500 rounded-full shadow-lg shadow-green-200 animate-pulse" />
      
      {/* 文字提示 */}
      <div className="mx-4 px-3 py-1 bg-green-500 text-white text-xs rounded-full shadow-lg animate-bounce">
        移动到{isAbove ? '上方' : '下方'}
      </div>
      
      {/* 插入线 */}
      <div className="flex-1 h-1 bg-green-500 rounded-full shadow-lg shadow-green-200 animate-pulse" />
    </div>
  );
};

/**
 * 拖拽状态浮层
 */
interface DragOverlayEnhancementProps {
  isDragging: boolean;
  dragType: 'tag' | 'group';
  itemName: string;
  targetInfo?: string;
}

export const DragOverlayEnhancement: React.FC<DragOverlayEnhancementProps> = ({
  isDragging,
  dragType,
  itemName,
  targetInfo
}) => {
  if (!isDragging) return null;

  return (
    <div className="fixed top-4 right-4 z-[9999] pointer-events-none">
      <div className={`
        px-4 py-3 rounded-lg shadow-xl border-2 transition-all duration-200
        ${dragType === 'tag' 
          ? 'bg-blue-50 border-blue-200 text-blue-800' 
          : 'bg-green-50 border-green-200 text-green-800'
        }
      `}>
        <div className="flex items-center gap-2">
          <div className={`
            w-3 h-3 rounded-full animate-pulse
            ${dragType === 'tag' ? 'bg-blue-400' : 'bg-green-400'}
          `} />
          <div className="text-sm font-medium">
            正在拖拽 {dragType === 'tag' ? '标签' : '分组'}: {itemName}
          </div>
        </div>
        {targetInfo && (
          <div className="text-xs text-gray-600 mt-1">
            {targetInfo}
          </div>
        )}
      </div>
    </div>
  );
};
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { Search, X, ChevronDown, ChevronRight, Check } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  useTagStore,
  useTagGroups,
  useTagQueries,
} from "@/components/tag-management/stores/useTagStore";
import { tagService } from "@/local";

interface NodeTagModalProps {
  visible: boolean;
  nodeId?: string;
  node?: Record<any, any>;
  onCancel: () => void;
  onOk: (selectedTags: string[]) => void;
  onUpdateNodeTags?: (nodeId: string, tags: string[]) => void;
}

export const NodeTagModal: React.FC<NodeTagModalProps> = ({
  visible,
  nodeId,
  node,
  onCancel,
  onOk,
  onUpdateNodeTags,
}) => {
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 使用tag-management的stores
  const tagGroups = useTagGroups();
  const { loadTagGroups } = useTagStore();

  // 搜索防抖
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchText]);

  // 获取所有标签的扁平化列表
  const allTags = useMemo(() => {
    return tagGroups.flatMap((group) =>
      group.tags.map((tag) => ({
        ...tag,
        groupId: group.id,
        groupTitle: group.title,
      }))
    );
  }, [tagGroups]);

  // 过滤标签列表（使用防抖搜索文本）
  const filteredTags = useMemo(() => {
    if (!debouncedSearchText.trim()) {
      return allTags;
    }
    return allTags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
        tag.groupTitle.toLowerCase().includes(debouncedSearchText.toLowerCase())
    );
  }, [allTags, debouncedSearchText]);

  // 按分组组织过滤后的标签
  const groupedFilteredTags = useMemo(() => {
    const groups: Record<string, typeof filteredTags> = {};
    filteredTags.forEach((tag) => {
      if (!groups[tag.groupId]) {
        groups[tag.groupId] = [];
      }
      groups[tag.groupId].push(tag);
    });
    return groups;
  }, [filteredTags]);

  // 切换组展开/折叠状态
  const toggleGroupExpanded = useCallback((groupId: string) => {
    setExpandedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  }, []);

  // 搜索时自动展开包含匹配标签的组
  useEffect(() => {
    if (debouncedSearchText.trim()) {
      const groupsToExpand = new Set<string>();
      Object.keys(groupedFilteredTags).forEach((groupId) => {
        groupsToExpand.add(groupId);
      });
      setExpandedGroups(groupsToExpand);
    }
  }, [debouncedSearchText, groupedFilteredTags]);

  // 加载标签数据
  useEffect(() => {
    if (visible) {
      setLoading(true);
      loadTagGroups().finally(() => setLoading(false));
    }
  }, [visible, loadTagGroups]);

  // 初始化时展开所有组
  useEffect(() => {
    if (visible && tagGroups.length > 0) {
      const allGroupIds = new Set(tagGroups.map((g) => g.id));
      setExpandedGroups(allGroupIds);
    }
  }, [visible, tagGroups]);

  // 获取节点当前标签
  useEffect(() => {
    if (visible && nodeId) {
      // 从node数据中获取当前标签，如果没有则从数据库获取
      const nodeTags = node?.data?.tags || [];
      setSelectedTags(nodeTags);

      // 如果node数据中没有标签，尝试从数据库获取
      if (nodeTags.length === 0) {
        tagService
          .getNodeTags({ nodeId })
          .then((tags) => {
            const tagIds = tags.map((t) => t.id);
            setSelectedTags(tagIds);
          })
          .catch((error) => {
            console.error("获取节点标签失败:", error);
          });
      }
    }
  }, [visible, nodeId, node]);

  // 切换标签选择状态并立即保存
  const toggleTag = useCallback(
    async (tagId: string) => {
      if (!nodeId) {
        console.error("节点ID不存在");
        return;
      }

      const newSelectedTags = selectedTags.includes(tagId)
        ? selectedTags.filter((id) => id !== tagId)
        : [...selectedTags, tagId];

      setSelectedTags(newSelectedTags);

      try {
        // 立即保存到数据库
        await tagService.updateNodeTags({ nodeId, tagIds: newSelectedTags });

        // 同步更新FlowStore中的节点数据
        if (onUpdateNodeTags) {
          onUpdateNodeTags(nodeId, newSelectedTags);
        }
      } catch (error) {
        console.error("更新节点标签失败:", error);
        // 如果保存失败，回滚状态
        setSelectedTags(selectedTags);
      }
    },
    [nodeId, selectedTags, onUpdateNodeTags]
  );

  // 关闭模态框
  const handleClose = useCallback(() => {
    setSearchText("");
    setDebouncedSearchText("");
    onCancel();
  }, [onCancel]);

  // 键盘快捷键处理
  useEffect(() => {
    if (!visible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape 关闭
      if (e.key === "Escape") {
        e.preventDefault();
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [visible, handleClose]);

  // 获取标签信息
  const getTagInfo = (tagId: string) => {
    return allTags.find((t) => t.id === tagId);
  };

  return (
    <Dialog open={visible} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-6xl w-[600px] p-0 gap-0 overflow-hidden rounded-2xl">
        <div className="flex flex-col h-full">
          {/* 顶部搜索区域 */}
          <div className="p-6 border-b border-gray-200">
            {/* 搜索框 */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索标签或分组..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="pl-10 pr-10"
              />
            </div>

            {/* 已选择的标签 */}
            {selectedTags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {selectedTags.map((tagId) => {
                  const tag = getTagInfo(tagId);
                  return tag ? (
                    <Badge
                      key={tagId}
                      variant="default"
                      className="bg-blue-500 hover:bg-blue-600 cursor-pointer text-white"
                      onClick={() => toggleTag(tagId)}
                    >
                      #{tag.name}
                      <X className="w-3 h-3 ml-1" />
                    </Badge>
                  ) : null;
                })}
              </div>
            )}
          </div>

          {/* 标签分组列表区域 */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="text-center py-8 text-muted-foreground">
                加载中...
              </div>
            ) : Object.keys(groupedFilteredTags).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchText ? "没有找到匹配的标签" : "暂无标签"}
              </div>
            ) : (
              <div className="space-y-3">
                {Object.entries(groupedFilteredTags).map(([groupId, tags]) => {
                  const group = tagGroups.find((g) => g.id === groupId);
                  const isExpanded = expandedGroups.has(groupId);

                  return (
                    <div
                      key={groupId}
                      className="overflow-hidden"
                    >
                      {/* 分组头部 */}
                      <div
                        className="flex items-center justify-between p-2 cursor-pointer bg-gray-50  hover:bg-gray-100 transition-colors"
                        onClick={() => toggleGroupExpanded(groupId)}
                      >
                        <div className="flex items-center gap-2">
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-500" />
                          )}
                          <h3 className="text-sm font-medium text-gray-700">
                            {group?.title}
                          </h3>
                          <span className="text-xs text-gray-500">
                            ({tags.length})
                          </span>
                        </div>
                      </div>

                      {/* 分组内容 */}
                      {isExpanded && (
                        <div className="p-4 pb-4">
                          {tags.length === 0 ? (
                            <p className="text-sm text-gray-500">No items.</p>
                          ) : (
                            <div className="flex flex-wrap gap-2">
                              {tags.map((tag) => (
                                <Badge
                                  key={tag.id}
                                  variant={
                                    selectedTags.includes(tag.id)
                                      ? "default"
                                      : "secondary"
                                  }
                                  className={`cursor-pointer transition-all hover:scale-105 ${
                                    selectedTags.includes(tag.id)
                                      ? "bg-blue-500 hover:bg-blue-600 text-white"
                                      : "hover:bg-gray-200"
                                  }`}
                                  onClick={() => toggleTag(tag.id)}
                                >
                                  #{tag.name}
                                  {selectedTags.includes(tag.id) && (
                                    <Check className="w-3 h-3 ml-1" />
                                  )}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

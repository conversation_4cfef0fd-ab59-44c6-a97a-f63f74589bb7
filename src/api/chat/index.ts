import { httpRequest } from '../index'
import { SessionListType, SessionDetailType, UpdateSessionType, DeleteSessionType, ConversationType, CreateSessionType } from './types.d'
// 会话列表
export const getSessionList = (data: SessionListType) => httpRequest.get('/sess', {
  params: data
})

// 会话详情
export const getSessionDetail = (data: SessionDetailType) => httpRequest.get('/sess/detail', {
  params: data
})

// 更新会话
export const updateSession = (data: UpdateSessionType) => httpRequest.patch('/sess', data)

// 删除会话
export const deleteSession = (data: DeleteSessionType) => httpRequest.delete('/sess', { data })


// 新增会话
export const createSession = (data: CreateSessionType) => httpRequest.post('/sess', data)

export * from './types.d'
import { useWorkerSpaceStore } from '@/store/workerspace-store/store'
import { useChatStore } from '@/store/workerspace-store/chat-store'

// OpenAI API配置
// 请在.env文件中设置VITE_OPENAI_API_KEY，或者直接在这里替换为您的API密钥
const OPENAI_API_KEY =  'sk-pWXcatwwLVmLvw1Kzb5V7f3v2gohFdSaTa9yeOwLtioHFrzM'
const OPENAI_API_URL = 'https://api.chatanywhere.tech'

// AI SDK请求消息格式
interface AISdkMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  data?: any
}

// AI SDK请求体格式  
interface AISdkChatRequest {
  messages: AISdkMessage[]
  data?: {
    refs?: any[]
    pdfRefs?: any[]
    nodeRefs?: any[]
    sessionId?: string
  }
}

// OpenAI API消息格式
interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

// OpenAI API请求格式
interface OpenAIChatRequest {
  model: string
  messages: OpenAIMessage[]
  stream: boolean
  temperature?: number
  max_tokens?: number
}

// 构建引用上下文
const buildReferenceContext = (refs: any[]): string => {
  if (!refs || refs.length === 0) return ''
  
  let context = '\n\n📚 参考资料：\n'
  refs.forEach((ref, index) => {
    context += `${index + 1}. **${ref.title}**`
    if (ref.page) context += ` (第${ref.page}页)`
    if (ref.content) context += `\n   ${ref.content}`
    context += '\n'
  })
  
  return context + '\n💡 请基于以上参考资料来回答问题，并在回答中标明信息来源。'
}

// AI SDK聊天API适配器
export const aiSdkChatAdapter = async (request: Request): Promise<Response> => {
  try {

    const body = await request.json() as AISdkChatRequest
    const { messages, data } = body
    
    // 获取当前状态中的引用
    const { footerRefs, pdfRefs, nodeRefs } = useChatStore.getState()
    
    // 合并所有引用
    const allRefs = [
      ...(data?.refs || []),
      ...(footerRefs || []),
      ...(pdfRefs?.map(pdf => ({ 
        ...pdf, 
        type: 1, 
        url: pdf.url,
        title: pdf.fileName 
      })) || []),
      ...(nodeRefs?.map(node => ({ 
        ...node, 
        type: 2 
      })) || [])
    ]
    
    // 转换为OpenAI格式的消息
    const openaiMessages: OpenAIMessage[] = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
    
    // 如果有引用，在最后一条用户消息中添加引用上下文
    if (allRefs.length > 0 && openaiMessages.length > 0) {
      const lastMessage = openaiMessages[openaiMessages.length - 1]
      if (lastMessage.role === 'user') {
        lastMessage.content += buildReferenceContext(allRefs)
      }
    }
    
    // 构建OpenAI API请求
    const openaiRequest: OpenAIChatRequest = {
      model: 'gpt-3.5-turbo', // 使用较便宜的模型
      messages: openaiMessages,
      stream: true,
      temperature: 0.7,
      max_tokens: 2000
    }
    
    console.log('🚀 发送请求到OpenAI API...', {
      model: openaiRequest.model,
      messageCount: openaiMessages.length,
      hasRefs: allRefs.length > 0,
      refCount: allRefs.length
    })
    
    // 发送请求到OpenAI API
    const openaiResponse = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify(openaiRequest)
    })
    
    if (!openaiResponse.ok) {
      const errorData = await openaiResponse.text()
      console.error('❌ OpenAI API Error:', errorData)
      
      // 提供更友好的错误信息
      let friendlyError = 'OpenAI API调用失败'
      if (openaiResponse.status === 401) {
        friendlyError = 'API密钥无效，请检查您的OpenAI API密钥是否正确'
      } else if (openaiResponse.status === 429) {
        friendlyError = 'API调用次数超限，请稍后再试或检查您的账户余额'
      } else if (openaiResponse.status === 402) {
        friendlyError = '账户余额不足，请为您的OpenAI账户充值'
      }
      
      throw new Error(`${friendlyError} (${openaiResponse.status})`)
    }
    
    console.log('✅ OpenAI API响应成功，开始流式传输...')
    
    // 直接返回OpenAI的流式响应
    // OpenAI的流式响应格式已经与AI SDK兼容
    return new Response(openaiResponse.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    })
    
  } catch (error) {
    console.error('❌ AI SDK Adapter Error:', error)
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Unknown error',
        type: 'ai_sdk_adapter_error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// 自定义fetch函数，用于AI SDK
export const createAISdkFetch = (): typeof fetch => {
  return async (input: RequestInfo | URL, init?: RequestInit) => {
    // 转换input为字符串URL
    const url = typeof input === 'string' ? input : 
                input instanceof URL ? input.toString() : 
                input instanceof Request ? input.url : ''
    
    // 如果是聊天API，使用适配器
    if (url.includes('/api/sess/chat')) {
      // 创建Request对象
      const request = input instanceof Request ? input : new Request(input, init)
      return aiSdkChatAdapter(request)
    }
    
    // 其他API直接转发
    return fetch(input, init)
  }
} 
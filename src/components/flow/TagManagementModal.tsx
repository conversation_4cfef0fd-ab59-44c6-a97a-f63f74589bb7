import React from "react";
import { X } from "lucide-react";
import { Dialog, FullscreenDialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { TagManagementPage } from "@/components/tag-management";

interface TagManagementModalProps {
  open: boolean;
  onClose: () => void;
}

export const TagManagementModal: React.FC<TagManagementModalProps> = ({
  open,
  onClose,
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <div className=" fixed z-30 top-0 left-0 bottom-0 right-0">
        {/* 头部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b bg-white sticky top-0 z-10">
          <h2 className="text-xl font-semibold text-gray-900">标签管理</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            关闭
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto">
          <TagManagementPage onClose={onClose} />
        </div>
      </div>
    </Dialog>
  );
};

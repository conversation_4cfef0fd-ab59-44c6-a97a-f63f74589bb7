import {CSSProperties, MouseEvent, useState} from "react";
import {getPageFromElement} from "../lib/pdfjs-dom";
import {Rnd} from "react-rnd";
import type {LTWHP, ViewportHighlight} from "../types";
import {Markdown} from "@/components/markdown";

/**
 * 便签组件的属性类型
 */
export interface NoteStickyProps {
    /**
     * 要渲染的高亮
     */
    highlight: ViewportHighlight;

    /**
     * 便签内容
     */
    content: string;

    /**
     * 当便签位置改变时的回调
     */
    onChange?(rect: LTWHP): void;

    /**
     * 是否已滚动到视图中
     */
    isScrolledTo?: boolean;

    /**
     * 便签的边界限制
     */
    bounds?: string | Element;

    /**
     * 右键菜单回调
     */
    onContextMenu?(event: MouseEvent<HTMLDivElement>): void;

    /**
     * 开始编辑时的回调
     */
    onEditStart?(): void;

    /**
     * 自定义样式
     */
    style?: CSSProperties;
}

/**
 * 渲染一个可移动的便签，用于展示模型输出的markdown内容
 */
export const NoteSticky = ({
                               highlight,
                               content,
                               onChange,
                               isScrolledTo,
                               bounds,
                               onContextMenu,
                               onEditStart,
                               style,
                           }: NoteStickyProps) => {
    const [expanded, setExpanded] = useState(true);
    const highlightClass = isScrolledTo ? "NoteSticky--scrolledTo" : "";

    // 根据位置生成key，当位置变化时强制重新挂载
    const key = `${highlight.position.boundingRect.width}${highlight.position.boundingRect.height}${highlight.position.boundingRect.left}${highlight.position.boundingRect.top}`;

    return (
        <div
            className={`NoteSticky ${highlightClass}`}
            onContextMenu={onContextMenu}
        >
            <Rnd
                className="NoteSticky__part"
                onDragStop={(_, data) => {
                    const boundingRect: LTWHP = {
                        ...highlight.position.boundingRect,
                        top: data.y,
                        left: data.x,
                    };

                    onChange && onChange(boundingRect);
                }}
                onResizeStop={(_mouseEvent, _direction, ref, _delta, position) => {
                    const boundingRect: LTWHP = {
                        top: position.y,
                        left: position.x,
                        width: ref.offsetWidth,
                        height: ref.offsetHeight,
                        pageNumber: getPageFromElement(ref)?.number || -1,
                    };

                    onChange && onChange(boundingRect);
                }}
                onDragStart={onEditStart}
                onResizeStart={onEditStart}
                default={{
                    x: highlight.position.boundingRect.left,
                    y: highlight.position.boundingRect.top,
                    width: highlight.position.boundingRect.width || 300,
                    height: highlight.position.boundingRect.height || 200,
                }}
                key={key}
                bounds={bounds}
                style={{
                    ...style,
                    backgroundColor: "white",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                    padding: "8px",
                    overflow: "auto",
                    zIndex: 1000,
                }}
                minWidth={200}
                minHeight={100}
                // 防止点击事件冒泡
                onClick={(event: MouseEvent) => {
                    event.stopPropagation();
                    event.preventDefault();
                }}
            >
                <div className="NoteSticky__header" style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "8px",
                    borderBottom: "1px solid #eee",
                    paddingBottom: "4px"
                }}>
                    <div style={{fontWeight: "bold"}}>模型摘要</div>
                    <div style={{cursor: "pointer"}} onClick={() => setExpanded(!expanded)}>
                        {expanded ? "收起" : "展开"}
                    </div>
                </div>
                {expanded && (
                    <div className="NoteSticky__content">
                        <Markdown content={content}/>
                    </div>
                )}
            </Rnd>
        </div>
    );
}; 
import React, { useCallback, useEffect, useState } from 'react';
import { Edge } from '@xyflow/react';
import { useFlowStore } from '@/store/flow-store';
import { useUpdateEdge } from './use-edge-update';
import { useEdgeConnect } from '../hooks/edge-connect';
import './index.less'

interface EdgePopupProps {
  edge: Edge;
  position: { left: number; top: number };
  onClose: () => void;
}

const iconBtn =
  "w-9 h-9 flex items-center justify-center rounded-lg hover:bg-[#f1f3fa] transition-colors text-[#6b6f76] hover:text-[#3b82f6] text-xl shadow-sm";

const colorList = [
  "#ff6a00", "#ff9500", "#ffc100", "#5fd200", "#00c97b",
  "#00c2c7", "#00a2ff", "#6a6cff", "#b966ff", "#ff5fc0", "#6b6f76"
];

const strokeWidths = [1, 2, 3, 4, 5, 6, 8, 10];

const EdgePopup: React.FC<EdgePopupProps> = ({ edge, position, onClose }) => {

  const { setEditingEdgeId, edges, setEdges } = useFlowStore.getState();
  const [showWidthSelector, setShowWidthSelector] = useState(false);
  const [showColorSelector, setShowColorSelector] = useState(false);

  const targetEdge = edges.find(e => e.id === edge.id)

  const isStraight = !!(targetEdge?.style as any)?.straight
  const dashArray = !!(targetEdge?.style as any)?.strokeDasharray
  const animation = !!(targetEdge?.data as any)?.animation

  const { updateEdge } = useUpdateEdge()

  // 删除边
  const handleDelete = () => {
    setEdges(edges.filter(e => e.id !== edge.id));
    onClose();
  };

  // 编辑标签
  const handleEditLabel = () => {
    setEditingEdgeId(edge.id);
  };

  // 线宽选择器
  const handleWidthIconClick = () => {
    setShowWidthSelector(v => !v);
    setShowColorSelector(false);
  };

  const handleWidthSelect = (w: number) => {
    const updateEdgeData = {
      strokeWidth: w
    }

    updateEdge(edge.id, updateEdgeData)

    setShowWidthSelector(false);
  };

  // 颜色选择器
  const handleColorIconClick = () => {
    setShowColorSelector(v => !v);
    setShowWidthSelector(false);
  };

  const handleColorSelect = (color: string) => {

    const updateEdgeData = {
      stroke: color,
    }

    updateEdge(edge.id, updateEdgeData)

    setShowColorSelector(false);
  };

  // 切换直线/曲线
  const handleToggleType = useCallback(() => {
    const targetEdge = edges.find(e => e.id === edge.id)
    const straight = !(targetEdge?.style as any)?.straight
    const updateEdgeData = {
      straight
    }
    updateEdge(edge.id, updateEdgeData)
  }, [edges])

  return (
    <div
      className="edge-popup fixed z-[9999] bg-white border border-[#e5e7ef] rounded-2xl shadow-xl flex items-center px-4 py-3 gap-2"
      style={{
        left: position.left,
        top: position.top,
        transform: 'translate(-50%, -100%)',
      }}
    >
      {/* 颜色盘 */}
      <div className="relative">
        <button className={iconBtn} title="颜色" onClick={handleColorIconClick}>
          <span role="img" aria-label="palette">🎨</span>
        </button>
        {showColorSelector && (
          <div className="absolute top-12 left-1/2 -translate-x-1/2 bg-white border border-[#e5e7ef] rounded-xl shadow-lg flex px-3 py-2 z-50">
            {colorList.map(color => (
              <button
                key={color}
                className="mx-1 w-6 h-6 rounded-full border-2 flex items-center justify-center"
                style={{
                  background: color,
                  borderColor: edge.style?.stroke === color ? "#3b82f6" : "#e5e7ef",
                  boxShadow: edge.style?.stroke === color ? "0 0 0 2px #3b82f6" : undefined,
                  outline: "none",
                  transition: "box-shadow 0.15s, border-color 0.15s"
                }}
                onClick={() => handleColorSelect(color)}
                title={color}
              >
                {edge.style?.stroke === color && (
                  <svg width="14" height="14" fill="none">
                    <circle cx="7" cy="7" r="5" stroke="#fff" strokeWidth="2" />
                  </svg>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
      {/* 线宽 */}
      <div className="relative">
        <button className={iconBtn} title="线宽" onClick={handleWidthIconClick}>
          <svg width="22" height="22" fill="none">
            <line x1="5" y1="11" x2="17" y2="11" stroke={edge.style?.stroke || "#3b82f6"} strokeWidth={edge.style?.strokeWidth || 2} strokeLinecap="round" />
          </svg>
        </button>
        {showWidthSelector && (
          <div className="absolute top-12 left-1/2 -translate-x-1/2 bg-white border border-[#e5e7ef] rounded-xl shadow-lg flex px-3 py-2 z-50">
            {strokeWidths.map(w => (
              <button
                key={w}
                className={`mx-1 flex flex-col items-center group ${edge.style?.strokeWidth === w ? 'text-[#3b82f6]' : 'text-[#6b6f76]'}`}
                onClick={() => handleWidthSelect(w)}
                title={`${w}px`}
                style={{ transition: 'color 0.15s' }}
              >
                <svg width="28" height="16">
                  <line x1="4" y1="8" x2="24" y2="8" stroke={edge.style?.stroke || "#3b82f6"} strokeWidth={w} strokeLinecap="round" />
                </svg>
                <span className="text-[11px] mt-0.5">{w}</span>
              </button>
            ))}
          </div>
        )}
      </div>
      {/* 曲线/直线切换 */}
      <button className={iconBtn} title={isStraight ? "切换为直线" : "切换为曲线"} onClick={handleToggleType}>
        {isStraight ? (
          <svg width="22" height="22" fill="none"><path d="M5 17C5 7 17 7 17 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" /></svg>
        ) : (
          <svg width="22" height="22" fill="none"><line x1="5" y1="11" x2="17" y2="11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" /></svg>
        )}
      </button>
      {/* 虚实线切换 */}
      <button
        className={iconBtn}
        title={dashArray ? "切换为实线" : "切换为虚线"}
        onClick={() => {
          const updateInfo = {
            strokeDasharray: !dashArray
          }
          updateEdge(edge.id, updateInfo);
        }}
      >
        {!dashArray ? (
          // 虚线图标
          <svg width="22" height="22" fill="none">
            <line x1="5" y1="11" x2="17" y2="11" stroke="currentColor" strokeWidth="2" strokeDasharray="6 4" strokeLinecap="round" />
          </svg>
        ) : (
          // 实线图标
          <svg width="22" height="22" fill="none">
            <line x1="5" y1="11" x2="17" y2="11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          </svg>
        )}
      </button>
      {/* T 文本 */}
      <button className={iconBtn} title="编辑标签" onClick={handleEditLabel}>
        <span className="font-bold text-base">T</span>
      </button>
      {/* 动画控制 */}
      <button
        className={iconBtn}
        title={animation ? "隐藏动画" : "显示动画"}
        onClick={() => {
          const state = !animation
          updateEdge(edge.id, {
            animation: state
          });
        }}
      >
        {!animation ? (
          // 动画开启图标
          <svg width="22" height="22" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" fill="none" />
            <circle cx="11" cy="11" r="3" fill="currentColor" />
          </svg>
        ) : (
          // 动画关闭图标
          <svg width="22" height="22" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" fill="none" />
          </svg>
        )}
      </button>

      {/* 删除 */}
      <button className={iconBtn + " hover:bg-[#ffeaea] hover:text-[#ef4444]"} title="删除" onClick={handleDelete}>
        <svg width="22" height="22" fill="none"><rect x="6" y="9" width="10" height="8" rx="2" stroke="currentColor" strokeWidth="2" /><path d="M9 9V7a2 2 0 1 1 4 0v2" stroke="currentColor" strokeWidth="2" /><path d="M4 9h14" stroke="currentColor" strokeWidth="2" /></svg>
      </button>
    </div>
  );
};

export default EdgePopup; 
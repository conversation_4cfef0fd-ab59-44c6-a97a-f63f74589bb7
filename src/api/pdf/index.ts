import { httpRequest } from '../index'
import {UploadPdfType, DeletePdftType, PdfHighlightType} from './types.d'
import { SSEClient } from '@/tools/sse-client';

// 获取PDF列表
export const getPdfList = async (wid: string | number) => {
  try {
    // 参数验证
    if (!wid) {
      console.error('获取PDF列表失败: 工作区ID为空');
      return {
        code: 4001,
        msg: '工作区ID不能为空',
        data: { list: [] }
      };
    }
        
    // 使用params对象传递参数
    const response = await httpRequest.get('/ws/attach-list', {
      params: { wid }
    });
        
    return response;
  } catch (error) {
    console.error('获取PDF列表失败:', error);
    return {
      code: 3,
      msg: error instanceof Error ? error.message : 'Cannot read properties of undefined (reading \'find\')',
      data: { list: [] }
    };
  }
};

// 上传PDF
export const uploadPdf = async (data: UploadPdfType) => {
  try {
    // 这个方法现在应该通过本地服务调用workspace-service的attach方法
    // 直接调用httpRequest，本地服务会处理
    return httpRequest.post('/ws/attach', data);
  } catch (error) {
    console.error('上传PDF失败:', error);
    return {
      code: 1,
      msg: '上传PDF失败'
    };
  }
};

// 删除PDF
export const deletePdf = (data: DeletePdftType) => {
  // 直接调用httpRequest，本地服务会处理
  return httpRequest.delete(`/ws/attach`, {
    data
  });
};

// 删除本地PDF
export const deleteLocalPdf = async (aid: string) => {
  try {
    // 调用PDF服务删除内容
    await httpRequest.delete('/pdf/delete', {
      data: { aid }
    });
    
    return {
      code: 0,
      msg: '删除成功'
    };
  } catch (error) {
    console.error('删除PDF失败:', error);
    return {
      code: 1,
      msg: '删除PDF失败'
    };
  }
};

// 更新高亮
export const updateHighlight = (data: PdfHighlightType) => {
  // 直接调用httpRequest，本地服务会处理
  return httpRequest.patch('/ws/mark', data);
};

// 高亮数据类型
interface HighlightData {
  id: string;
  aid: string;
  wid: string;
  mark: string;
  color: string;
}

// 更新本地高亮
export const updateLocalHighlight = async (data: PdfHighlightType) => {
  try {
    // 调用PDF服务保存高亮
    await httpRequest.post('/pdf/highlight', data);
    
    return {
      code: 0,
      msg: '更新高亮成功'
    };
  } catch (error) {
    console.error('更新高亮失败:', error);
    return {
      code: 1,
      msg: '更新高亮失败'
    };
  }
};

// 获取高亮
export const getHighlights = async (aid: string) => {
  try {
    // 调用PDF服务获取高亮
    return httpRequest.get(`/pdf/highlight?aid=${aid}`);
  } catch (error) {
    console.error('获取高亮失败:', error);
    return {
      code: 1,
      msg: '获取高亮失败',
      data: { list: [] }
    };
  }
};

// 导出PDF到文件系统
export const savePdfToLocal = async ({
  aid,
  customFileName,
}: {
  aid: string;
  customFileName?: string;
}) => {
  try {
    // 调用PDF服务导出PDF
    const response = await httpRequest.post('/pdf/export', {
      aid,
      customFileName
    });
    
    return {
      success: true,
      fileName: response.data.fileName
    };
  } catch (error) {
    console.error('导出PDF失败:', error);
    return {
      success: false,
      error
    };
  }
};

// 文本插入类型
export interface TextInsertType {
  aid: string;
  wid: string;
  text: string;
  mark: string; // JSON字符串，包含位置、类型和内容信息
  color?: string;
}

// 插入文本，不创建节点
export const insertText = async (data: TextInsertType) => {
  try {
    // 调用PDF服务插入文本
    const response = await httpRequest.post('/pdf/insert-text', data);

    // 确保返回一致的数据结构
    return {
      code: response.data?.code || 0,
      msg: response.data?.msg || '插入文本成功',
      data: response?.data || {
        mid: Date.now().toString(), // 临时ID，实际应该由后端返回
      }
    };
  } catch (error) {
    console.error('插入文本失败:', error);
    return {
      code: 1,
      msg: '插入文本失败',
      data: null
    };
  }
};

// 生成摘要类型
export interface GenerateSummaryType {
  question: string;  // 问题文本
  Refs: Array<any>;  // 参考资料数组
  sid?: string;      // 可选，会话ID
  rid?: number;      // 可选，请求ID
}

// 生成摘要API
export const generateSummary = async (data: GenerateSummaryType) => {
  try {
    // 统一使用外部 OpenAI API
    const aiChatUrl = 'https://api.chatanywhere.tech/v1/chat/completions';
    const sseClient = new SSEClient(aiChatUrl);
    
    // 返回一个包含SSE客户端的对象，让调用方处理流式数据
    return {
      code: 0,
      msg: '请求成功',
      data: {
        sseClient, // 返回SSE客户端实例
        requestData: data // 返回请求数据，方便调用方使用
      }
    };
  } catch (error) {
    console.error('生成摘要失败:', error);
    return {
      code: 1,
      msg: '生成摘要失败',
      data: {
        summary: '',
        sseClient: null
      }
    };
  }
};

export * from './types.d'
import { Remark } from "react-remark";
import "vditor/dist/index.css";
import { useEffect, useState } from "react";
import Vditor from "vditor";
import useNodeEdit from "@/components/flow/hooks/node-edit.ts";
import { NodeEditor } from "@/components/lexicalEditor/App";

const NodeContent = ({ nid, content, editMode, setEditMode }: {
    nid: string,
    content: string
    editMode: number,
    setEditMode: (editMode: number) => void,
}) => {
    const { onNodeEdit } = useNodeEdit();
    // const [vd, setVd] = useState<Vditor>();

    // useEffect(() => {
    //     if (editMode === 1) {
    //         const vditor = new Vditor("node-vditor", {
    //             height: '100%',
    //             toolbar,
    //             after: () => {
    //                 vditor.setValue(content);
    //                 vditor.focus();
    //                 setVd(vditor);
    //             },
    //             blur: async () => {
    //                 const newContent = vditor.getValue();
    //                 await onNodeEdit({ id: nid, content: newContent });
    //             },
    //             mode: "wysiwyg"
    //         });
    //     } else {
    //         if (vd) {
    //             vd.destroy();
    //             setVd(undefined);
    //         }
    //     }

    //     return () => {
    //         if (vd) {
    //             vd.destroy();
    //             setVd(undefined);
    //         }
    //     };
    // }, [editMode]);

    return editMode === 1 ? (
        // <div id="node-vditor"  ></div>
        <div className="vditor nodrag w-full max-h-full h-full overflow-y-scroll hide-scrollbar">
            <NodeEditor onChange={(value: string | undefined) => {
                onNodeEdit({ id: nid, content: value || '' })
            }} content={content}/>
        </div>
    ) : (
        <div
            className="px-4 py-2 vditor-reset h-full overflow-y-scroll"
            onDoubleClick={() => setEditMode(1)}
        >
            <Remark>{content}</Remark>
        </div>
    );
}

export default NodeContent

const toolbar = [
    // 始终显示的核心工具
    "emoji", "headings", "bold", "italic", "strike", "|", "line", "quote", "list", "ordered-list", "check", "outdent", "indent", "code", "inline-code",
    // 更多工具放在子菜单中
    {
        name: 'more',
        toolbar: [
            "insert-after", "insert-before", "undo", "redo", "upload", "link", "table", "record", "edit-mode", "both", "preview", "fullscreen", "outline", "code-theme", "content-theme", "export", "devtools", "info", "help", "br"
        ]
    }
];
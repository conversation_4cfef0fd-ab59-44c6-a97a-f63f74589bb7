import { Node } from '@xyflow/react';

interface PositionConfig {
  width: number;
  height: number;
  padding: number;
}

export const calculateNodePosition = (nodes: Node[], config: PositionConfig) => {
  if (nodes.length === 0) {
    return { x: 100, y: 100 };
  }

  // 找到最右边的节点
  const rightmostNode = nodes.reduce((max, node) => {
    return node.position.x > max.position.x ? node : max;
  }, nodes[0]);

  // 找到最右边一列的所有节点
  const rightColumn = nodes.filter(
    (node) => Math.abs(node.position.x - rightmostNode.position.x) < config.width / 2
  );

  // 找到该列最下方的节点
  const bottomNode = rightColumn.reduce((max, node) => {
    return node.position.y > max.position.y ? node : max;
  }, rightColumn[0]);


  // 计算每列最大节点数（考虑屏幕高度）
  const maxNodesInColumn = Math.floor(
    (window.innerHeight - config.padding) / (config.height + config.padding)
  );
  // 判断是否需要开始新的一列
  const shouldStartNewColumn = rightColumn.length >= maxNodesInColumn;

  if (shouldStartNewColumn) {
    return {
      x: rightmostNode.position.x + config.width + config.padding,
      y: config.padding,
    };
  }

  return {
    x: rightmostNode.position.x,
    y: bottomNode.position.y + config.height + config.padding,
  };
};

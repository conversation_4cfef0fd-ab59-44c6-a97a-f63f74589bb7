import { useCallback, useEffect } from 'react';
import { FileTreeNode } from '@/components/file-tree';
import { folderService, getDatabase } from '@/local';
import { usePdfStore } from '@/store/pdf-store.ts';
import { useMenu } from './menu.tsx';
import { WorkspaceService } from '@/local/services/workspace-service';
import { deletePdf } from '@/api/pdf';
import { FileTreeItem } from './use-file-manager';

// 创建 WorkspaceService 实例
const workspaceService = new WorkspaceService();

// PDF数据类型
interface PdfData {
  aid: string;
  filename: string;
  url: string;
}

// 文件夹创建请求事件接口
interface FolderCreateRequest {
  path: string;
  name: string;
  parentId?: string; // 可选的父文件夹ID
}



export function useFileOperations(
  currentWorkspaceId: string,
  loadData: () => Promise<void>,
  setTargetFolderId?: (id: string) => void,
  updateFileTree?: () => void,
  addFolder?: (folder: FileTreeItem) => void,
  removeFolder?: (folderId: string) => void,
  removeFile?: (fileId: string) => void,
  folders: FileTreeItem[] = [] // 添加folders参数，默认为空数组
) {
  const { onMenuClick } = useMenu();
  const { addPdfs, removePdfs, setTabItems } = usePdfStore(state => ({
    addPdfs: state.addPdfs,
    removePdfs: state.removePdfs,
    setTabItems: state.setTabItems
  }));

  // 处理文件选择
  const handleFileSelect = useCallback(async (node: FileTreeNode) => {
    if (node.isDirectory) {
      // 如果选择的是文件夹，设置当前目标文件夹ID
      if (setTargetFolderId) {
        const folderId = node.id === 'folder-all' ? 'root' : node.id;
        setTargetFolderId(folderId);
        console.log(`已选择文件夹: ${node.title}，ID: ${folderId}`);
      }
      return;
    }
    
    // 处理文件选择
    const aid = node.data?.pdfInfo?.aid || node.data?.fileInfo?.id || node.id;
    
    if (!aid) {
      console.error("无法获取文件ID");
      return;
    }
    
    try {
      // 确保文件在 PDF store 中
      const pdfStore = usePdfStore.getState();
      if (!pdfStore.pdfs.has(aid)) {
        // 使用 workspaceService 获取文件内容
        try {
          const url = await workspaceService.getFileContent(aid);
          
          // 获取文件信息
          const db = await getDatabase();
          const fileDoc = await db.attachments.findOne({
            selector: { id: aid }
          }).exec();
          
          if (!fileDoc) {
            alert("文件记录不存在，请重新上传文件");
            return;
          }
          
          // 添加到 PDF store
          const pdfData = new Map();
          pdfData.set(aid, {
            aid: aid,
            filename: fileDoc.file_name,
            url: url
          });
          addPdfs(pdfData);
        } catch (error) {
          console.error("获取文件内容失败:", error);
          alert("无法加载文件，请重新上传");
          return;
        }
      }
      
      // 打开文件
      onMenuClick(aid);
    } catch (error) {
      console.error("打开文件失败:", error);
      alert("打开文件失败，请重试");
    }
  }, [onMenuClick, addPdfs, setTargetFolderId]);

  // 处理新建文件/文件夹
  const handleFileCreate = useCallback(async (parentPath: string, type: 'file' | 'folder') => {
    if (!currentWorkspaceId) {
      alert('未找到工作区ID，无法创建文件/文件夹');
      return;
    }
    
    // 从路径中提取父文件夹ID
    let parentId = 'root';
    
    // 尝试从路径中提取父文件夹ID
    if (parentPath && parentPath !== '/') {
      const pathParts = parentPath.split('/').filter(Boolean);
      const parentFolderName = pathParts[pathParts.length - 1];
      
      if (parentFolderName === '所有文档') {
        parentId = 'root';
              } else {
          // 查询数据库获取文件夹ID
          try {
            const db = await getDatabase();
            const folder = await db.attachments.findOne({
              selector: {
                wid: currentWorkspaceId,
                file_name: parentFolderName,
                type: 'folder'
              }
            }).exec();
            
            if (folder) {
              parentId = folder.id;
            } else {
              console.warn(`未找到文件夹: ${parentFolderName}，将创建在根目录`);
              parentId = 'root';
            }
          } catch (error) {
            console.error('获取父文件夹ID失败:', error);
            parentId = 'root';
          }
        }
    }
    
    if (type === 'file') {
      // 文件创建由文件上传按钮处理
      // 但我们可以设置当前目标文件夹ID
      if (setTargetFolderId) {
        setTargetFolderId(parentId);
      }
      return;
    } else {
      // 检查路径中是否已经包含文件夹名称
      // 格式可能是 /parent/newFolderName
      let folderName = '';
      
      if (parentPath.includes('/')) {
        const lastSegment = parentPath.split('/').pop();
        // 如果最后一段不是父文件夹名称，可能是新文件夹名称
        if (lastSegment && lastSegment !== '所有文档') {
          // 检查这个名称是否已经存在于folders中
          const exists = folders.some((f: FileTreeItem) => f.file_name === lastSegment);
          
          if (!exists) {
            // 这可能是从HeadlessFileTreePanel传来的新文件夹名称
            folderName = lastSegment;
          }
        }
      }
      
      // 如果没有从路径中提取到名称，则弹出prompt
      if (!folderName) {
        const promptResult = prompt('请输入文件夹名称:');
        if (promptResult !== null) {
          folderName = promptResult;
        } else {
          // 用户取消了prompt
          return;
        }
      }
      
      // 调用内部方法创建文件夹
      await createFolder(folderName, parentId);
    }
  }, [currentWorkspaceId, loadData, setTargetFolderId, addFolder, updateFileTree, folders]);

  // 内部方法：创建文件夹
  const createFolder = useCallback(async (folderName: string, parentId: string = 'root') => {
    if (!folderName || !folderName.trim() || !currentWorkspaceId) {
      return { success: false, message: '无效的文件夹名称或工作区ID' };
    }
    
    try {
      // 创建文件夹
      const result = await folderService.createFolder({
        wid: currentWorkspaceId,
        name: folderName.trim(),
        parent_id: parentId
      });
      
      // 如果创建成功并返回了ID，设置为当前目标文件夹
      if (result && result.id && setTargetFolderId) {
        setTargetFolderId(result.id);
      }
      
      // 增量更新：添加新文件夹到状态
      if (addFolder) {
        const now = Math.floor(Date.now() / 1000);
        const newFolder: FileTreeItem = {
          id: result.id,
          file_name: folderName.trim(),
          type: 'folder',
          parent_id: parentId,
          create_at: now,
          update_at: now
        };
        
        addFolder(newFolder);
        
        // 强制更新文件树
        if (updateFileTree) {
          updateFileTree();
        }
        
        console.log('文件夹已添加到状态:', newFolder);
        
        return { success: true, message: `文件夹 "${folderName}" 创建成功！`, folderId: result.id };
      } else {
        // 如果没有提供增量更新方法，则回退到全量更新
        await loadData();
        return { success: true, message: `文件夹 "${folderName}" 创建成功！`, folderId: result.id };
      }
    } catch (error) {
      return { 
        success: false, 
        message: `创建文件夹失败: ${error instanceof Error ? error.message : '未知错误'}` 
      };
    }
  }, [currentWorkspaceId, loadData, setTargetFolderId, addFolder, updateFileTree]);

  // 监听文件夹创建请求事件
  useEffect(() => {
    const handleFolderCreateRequest = async (e: Event) => {
      const customEvent = e as CustomEvent<FolderCreateRequest>;
      const { path, name, parentId: pathParentId } = customEvent.detail;
      
      // 优先使用传递的parentId，如果没有则从路径中提取
      let parentId = pathParentId || 'root';
      
      // 如果没有传递parentId，尝试从路径中提取
      if (!pathParentId && path && path !== '/') {
        const pathParts = path.split('/').filter(Boolean);
        const parentFolderName = pathParts[pathParts.length - 1];
        
        if (parentFolderName === '所有文档') {
          parentId = 'root';
        } else {
          // 查询数据库获取文件夹ID
          try {
            const db = await getDatabase();
            const folder = await db.attachments.findOne({
              selector: {
                wid: currentWorkspaceId,
                file_name: parentFolderName,
                type: 'folder'
              }
            }).exec();
            
            if (folder) {
              parentId = folder.id;
            } else {
              console.warn(`未找到文件夹: ${parentFolderName}，将创建在根目录`);
              parentId = 'root';
            }
          } catch (error) {
            console.error('获取父文件夹ID失败:', error);
            parentId = 'root';
          }
        }
      }
      
      // 创建文件夹
      const result = await createFolder(name, parentId);
      
      // 发送创建结果事件
      document.dispatchEvent(new CustomEvent('ht-folder-create-result', {
        detail: { 
          success: result.success, 
          message: result.message 
        }
      }));
    };
    
    // 添加事件监听
    document.addEventListener('ht-folder-create-request', handleFolderCreateRequest);
    
    // 清理函数
    return () => {
      document.removeEventListener('ht-folder-create-request', handleFolderCreateRequest);
    };
  }, [createFolder]);

  // 处理文件删除
  const handleFileDelete = useCallback(async (path: string, folders: any[], pdfs: Map<string, PdfData>) => {
    const fileName = path.split('/').pop();
    
    const folder = folders.find(f => f.file_name === fileName);
    if (folder) {
      const confirmed = confirm(`确定要删除文件夹 "${folder.file_name}" 吗？\n此操作将删除文件夹及其所有内容。`);
      if (confirmed) {
        try {
          // 获取该文件夹下的所有文件
          const db = await getDatabase();
          const filesInFolder = await db.attachments.find({
            selector: {
              parent_id: folder.id,
              type: 'file'
            }
          }).exec();
          
          // 从PDF store中移除该文件夹下的所有文件
          filesInFolder.forEach((file: any) => {
            if (pdfs.has(file.id)) {
              removePdfs(file.id);
              
              // 从标签页中移除
              const state = usePdfStore.getState();
              const tabItems = state.tabItems || [];
              const newTabItems = tabItems.filter((item) => item.key !== file.id);
              setTabItems(newTabItems);
              
              // 增量更新：从文件列表中移除
              if (removeFile) {
                removeFile(file.id);
              }
            }
          });
          
          // 删除文件夹
          await folderService.delete({ id: folder.id });
          
          // 如果删除的是当前选中的文件夹，重置为root
          if (setTargetFolderId) {
            setTargetFolderId('root');
          }
          
          // 增量更新：从文件夹列表中移除
          if (removeFolder) {
            removeFolder(folder.id);
            
            // 强制更新文件树
            if (updateFileTree) {
              updateFileTree();
            }
            
            setTimeout(() => {
              alert(`✅ 文件夹 "${folder.file_name}" 删除成功！`);
            }, 100);
          } else {
            // 如果没有提供增量更新方法，则回退到全量更新
            await loadData();
            setTimeout(() => {
              alert(`✅ 文件夹 "${folder.file_name}" 删除成功！`);
            }, 100);
          }
          
        } catch (error) {
          console.error('删除文件夹失败:', error);
          alert(`❌ 删除文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      return;
    }
    
    const pdf = Array.from(pdfs.values()).find(p => p.filename === fileName);
    if (pdf) {
      // 删除PDF文件
      const confirmed = confirm(`确定要删除文件 "${pdf.filename}" 吗？`);
      if (confirmed) {
        try {
          // 先从PDF store中移除文件，确保UI立即更新
          removePdfs(pdf.aid);
          
          // 从标签页中移除
          const state = usePdfStore.getState();
          const tabItems = state.tabItems || [];
          const newTabItems = tabItems.filter((item) => item.key !== pdf.aid);
          setTabItems(newTabItems);
          
          // 增量更新：从文件列表中移除
          if (removeFile) {
            removeFile(pdf.aid);
          }
          
          // 直接调用API删除文件
          await deletePdf({
            aid: pdf.aid,
            type: 1
          });
          
          // 强制更新文件树
          if (updateFileTree) {
            updateFileTree();
          } else {
            // 如果没有提供更新方法，则回退到全量更新
            await loadData();
          }
          
          // 使用setTimeout延迟显示成功提示，避免UI阻塞
          setTimeout(() => {
            alert(`✅ 文件 "${pdf.filename}" 删除成功！`);
          }, 100);
        } catch (error) {
          console.error('删除文件失败:', error);
          // 如果删除失败，恢复PDF到store
          const fileToRestore = Array.from(pdfs.values()).find(p => p.aid === pdf.aid);
          if (fileToRestore) {
            const pdfData = new Map();
            pdfData.set(fileToRestore.aid, fileToRestore);
            addPdfs(pdfData);
          }
          
          alert(`❌ 删除文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
    }
  }, [loadData, setTargetFolderId, removePdfs, addPdfs, setTabItems, removeFile, removeFolder, updateFileTree]);

  // 处理文件重命名
  const handleFileRename = useCallback(async (oldPath: string, newPath: string, folders: any[]) => {
    const oldFileName = oldPath.split('/').pop();
    const newFileName = newPath.split('/').pop();
    
    if (!oldFileName || !newFileName || oldFileName === newFileName) {
      return;
    }
    
    const folder = folders.find(f => f.file_name === oldFileName);
    if (folder) {
      try {
        // 调用API重命名文件夹
        await folderService.rename({
          id: folder.id,
          new_name: newFileName
        });
        
        // 这里我们需要全量更新，因为重命名可能会影响文件树结构
        // 如果后续需要优化，可以考虑实现更精细的增量更新
        await loadData();
        
      } catch (error) {
        alert(`❌ 重命名文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
  }, [loadData]);

  return {
    handleFileSelect,
    handleFileCreate,
    handleFileDelete,
    handleFileRename
  };
} 
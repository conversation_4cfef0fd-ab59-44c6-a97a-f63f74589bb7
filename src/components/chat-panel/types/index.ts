import React from "react";

export type SessionType = {
  sid: string;
  title: string;
  datetime: number;
};

export type ChatRef = {
  id?: string;
  type: number | "pdf" | "node";
  path: string;
  title: string;
  content: string;
  nid?: string;
};

export type ChatDetail = {
  question: string;
  answer: string;
  refs: ChatRef[];
};

export type ChatInputProps = {
  isFooter?: boolean;
  rid: string | number;
  question?: string;
  currentRefs?: ChatResponseRefs;
  footerRefs?: TagRefType[];
  context?: ChatResponseType;
  inStream?: boolean;
  children?: React.ReactNode
}

// 其他类型定义也可以放在这里
export type ConversationResponse = {
  code: number;
  data: {
    sid: string;
  };
  msg?: string;
};

export type ModelType = "gpt-3.5-turbo" | "gpt-4" | "custom-model";

export interface ChatModel {
  value: ModelType;
  label: string;
}

export interface ChatListProps {
  footerHeight: number;
  responseContainerRef: React.RefObject<HTMLDivElement | null>;
}

export type ChatResponseRefs = {
  type: number,
  id: string,
  title: string,
  content: string
  url?: string
  page?: number
  refInfo?: PdfRef | NodeRef
}[] | null
// 从 response-type.ts 整合的类型
export type ChatResponseType = {
  rid: number
  question: string;
  answer: string;
  refs: ChatResponseRefs
};


export type TagRefType = {
  id: string
  title: string
  content?: string
  url?: string
  type: number
  page?: number;
  refInfo?: PdfRef | NodeRef
}
export interface PdfRef {
  id: number | string;
  fileName: string;
  url: string;
  page?: number;
  content?: string;
  title?: string;
}
export interface NodeRef {
  id: number | string;
  title?: string;
  content: string;
}
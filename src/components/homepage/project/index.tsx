import {useEffect, useRef, useState} from "react";
import {Input, message} from "antd";
import {InputRef} from "antd/es/input";
import styled from "styled-components";
import RecycleBin from "../../../assets/images/recycle-bin.png";
import MediumPlus from "../../../assets/images/plus-medium.png";
import MiniPlus from "../../../assets/images/plus-mini.png";
import {ProjectItemGrid} from "./project-item-grid";
import {ProjectItemList} from "./project-item-list";
import {ProjectType} from "../response-type";
import {useHomeStore} from "@/store/home-store";
import {ProjectRecycleBinDialog} from "../recycle/recycle-modal";
import {CreateProjectModal} from "@/components/pop-window/create-project-window";
import {ProjectHeader} from "./project-header";
import {getProjectList, updateProject} from "@/api/project";
import {ConfirmModalWrapper} from "@/components/pop-window/confirm-window";

const Container = styled.div`
  height: calc(100vh - 88px);
  flex: 1;
  padding: 0 60px;
  overflow-x: hidden; /* 防止水平滚动条 */
  
  @media (max-width: 768px) {
    padding: 0 20px;
  }
`;

const ProjectGridLayout = styled.div`
  height: calc(100vh - 206px);
  width: 100%;
  overflow-y: auto;
  scrollbar-color: transparent transparent;
  
  /* 使用CSS Grid实现水平排列的瀑布流 */
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-gap: 30px;
  grid-auto-rows: auto;
  grid-auto-flow: row; /* 确保按行填充 */
  
  /* 添加平滑过渡效果 */
  transition: all 0.3s ease;
  
  /* 响应式布局 - 根据屏幕宽度调整列数 */
  @media (max-width: 1920px) {
    grid-template-columns: repeat(5, 1fr);
    grid-gap: 28px;
  }
  
  @media (max-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 25px;
  }
  
  @media (max-width: 1366px) {
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 22px;
  }
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 15px;
  }
`;

const ProjectListLayout = styled.div`
  height: calc(100vh - 206px);
  gap: 10px;
  scrollbar-color: transparent transparent;
  display: flex;
  flex-direction: column;
`;

export const Project = () => {
  const {
    categories,
    projects,
    layoutType,
    searchList,
    selectCategoryId,
    createProjectModalVisible,
    isRestore,
    openReCyclcModal,
    modifyProject,
    setProjects,
    setSearchList,
    setCreateProjectModalVisible,
    openRecycleBinModal,
  } = useHomeStore();
  const modifyProjectTitleInput = useRef<InputRef>(null);
  const [colorList, setColorList] = useState<{ cid: string; color: string }[]>(
    []
  );
  const [createCategoryVisible, setCreateCategoryVisible] =
    useState<boolean>(false);
  const [newProjectTitle, setNewProjectTitle] = useState<string>("");
  const [currentProject, setCurrentProject] = useState<ProjectType | null>();
  // 初始化
  const onFetchData = async () => {
    const body = { cid: "-1", page: 1, page_size: 100, keyword: "", from: 0 };
    const { code, data: res } = (await getProjectList(body)) as any;
    if (code === 0) {
      setProjects(res.list ? res.list : []);
    }
  };
  // 更新数据
  useEffect(() => {
    onFetchData();
  }, [isRestore]);

  useEffect(() => {
    const colors =
      localStorage.getItem("category_color_list") !== null
        ? JSON.parse(localStorage.getItem("category_color_list") as string)
        : [];
    setColorList(colors);
  }, [categories, projects]);
  useEffect(() => {
    if (projects.length > 0) {
      // 先按更新时间排序（降序）
      const sortedProjects = [...projects].sort((a, b) => b.last_at - a.last_at);
      
      setSearchList(
        sortedProjects.filter((item) => {
          if (selectCategoryId === "-1") {
            return true;
          } else {
            return item.cid === selectCategoryId;
          }
        })
      );
    } else {
      setSearchList([]);
    }
  }, [projects, selectCategoryId]);
  useEffect(() => {
    if (modifyProjectTitleInput.current) {
      modifyProjectTitleInput.current.focus();
    }
  }, [modifyProjectTitleInput.current, createCategoryVisible]);

  // 修改项目名称
  const handleProjectTitleChange = async () => {
    if (newProjectTitle.trim() !== "") {
      if (currentProject) {
        try {
          const res = await updateProject({
            wid: currentProject.wid,
            title: newProjectTitle,
            cid: currentProject.cid,
          });
          if ((res as any).code === 0) {
            modifyProject({
              ...currentProject,
              title: newProjectTitle,
              last_at: new Date().getTime() / 1000,
            });
            setCreateCategoryVisible(false);
          }
        } catch (error) {
          // 安全地处理未知类型的错误
          const errorMessage = error && typeof error === 'object' && 'msg' in error 
            ? (error as any).msg 
            : '更新项目名称失败';
          message.error(errorMessage);
        }
      }
    }
  };

  return (
    <Container>
      <ProjectHeader />
      {layoutType === "grid" ? (
        <ProjectGridLayout className="scrollbar-custom">
          {searchList.map((item: ProjectType, index: number) => {
            return (
              <ProjectItemGrid
                project={item}
                tagColors={colorList}
                getCurrentProject={(currentProject: ProjectType) => {
                  setCurrentProject(currentProject);
                  setNewProjectTitle(currentProject.title);
                  setCreateCategoryVisible(true);
                }}
                key={index}
              />
            );
          })}
          {/* 新增project按钮 */}
          <div className="w-full mb-4" style={{width: "300px"}}>
            <div
              className="w-full rounded-[10px] bg-[#EAEBF7] shadow-[0px_4px_16px_rgba(61,86,186,0.4)] flex justify-center items-center cursor-pointer transition-all duration-300 hover:shadow-lg hover:transform hover:scale-[1.02]"
              style={{ paddingBottom: '75%', position: 'relative' }}
              onClick={() => {
                setCreateProjectModalVisible(true);
              }}
            >
              <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
                <img src={MediumPlus} alt="" />
              </div>
            </div>
            <div className="text-[#3D56BA] text-[18px] mt-2">New Project</div>
          </div>
        </ProjectGridLayout>
      ) : (
        <ProjectListLayout className="scrollbar-custom">
          {searchList.map((item: ProjectType, index: number) => {
            return (
              <ProjectItemList
                project={item}
                tagColors={colorList}
                getCurrentProject={(currentProject: ProjectType) => {
                  setCurrentProject(currentProject);
                  setNewProjectTitle(currentProject.title);
                  setCreateCategoryVisible(true);
                }}
                key={index}
              />
            );
          })}
          {/* 新增project按钮 */}
          <div
            className="w-[calc(100%-20px)] h-[65px] flex justify-between bg-[#E0E2F4] cursor-pointer rounded-md px-2.5 hover:w-[calc(100%-10px)] hover:border-2 hover:border-[var(--border-color)] transition-all ease-in-out"
            onClick={() => {
              setCreateProjectModalVisible(true);
            }}
          >
            <div className="flex  justify-center items-center gap-5 ">
              <div
                className="w-[70px] h-[44px] border-[1px] border-[#3D56BA] rounded-[10px] bg-[#EAEBF7] flex justify-center items-center"
                onClick={() => {
                  setCreateProjectModalVisible(true);
                }}
              >
                <div>
                  <img src={MiniPlus} alt="" />
                </div>
              </div>
              <div className="text-[#3D56BA] text-[18px]">New Project</div>
            </div>
            <div />
          </div>
        </ProjectListLayout>
      )}
      <div
        className="fixed bottom-10 right-10 bg-[#DCDEF0] hover:bg-[#C3CCEA] p-2 rounded-full  cursor-pointer"
        onClick={() => {
          openRecycleBinModal(true);
        }}
      >
        <img src={RecycleBin} alt="" />
      </div>
      {openReCyclcModal && (
        <ProjectRecycleBinDialog
          open={openReCyclcModal}
          onClose={() => openRecycleBinModal(false)}
        />
      )}
      {createProjectModalVisible && (
        <CreateProjectModal
          visible={createProjectModalVisible}
          onClose={() => {
            setCreateProjectModalVisible(false);
          }}
        />
      )}
      {createCategoryVisible && (
        <ConfirmModalWrapper
          title="重命名"
          handleType="edit"
          confirmOpen={createCategoryVisible}
          onClose={() => {
            setCreateCategoryVisible(false);
          }}
          onConfirmHandle={handleProjectTitleChange}
          component={
            <Input
              className={`w-[300px] h-[40px] bg-[var(--bg-primary)] border-[1px] border-[var(--border-color)] rounded-[4px] pl-4`}
              ref={modifyProjectTitleInput}
              value={newProjectTitle}
              onChange={(e) => setNewProjectTitle(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleProjectTitleChange();
                }
              }}
            />
          }
        />
      )}
    </Container>
  );
};

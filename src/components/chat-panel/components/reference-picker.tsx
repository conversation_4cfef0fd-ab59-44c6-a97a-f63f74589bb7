import { useState } from "react";
import { LinkOutlined } from "@ant-design/icons";
import { Dropdown, Tag, Tooltip } from "antd";
import styled from "styled-components";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { CopyableContent } from "@/components/ui/copyable-content";
import PdfLarge from "@/assets/images/uploadPdf.png";
import { Reference } from "./reference";
import type { ChatInputProps, TagRefType } from "../types";
import { Remark } from "react-remark";
import { getNodeHighlightWithPdf } from "@/store/pdf-store";

// type: 1. pdf  4. 图片  2 flow节点 

enum PreviewType {
  pdf = 1,
  img = 4,
  flow = 2,
  pdfHightLight = 3,
}

/**
 * 引用标签容器样式
 * 用于水平排列多个引用标签
 */
const CharBar = styled.div`
  display: flex;
  gap: 2px;
  padding: 2px 3px;
  flex-wrap: wrap;
`;

const CustomTag = styled(Tag) <{ $selected: boolean }>`
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 24px;
  position: relative;
  cursor: pointer;
  border: none;
  border: ${(props) => (props.$selected ? "1px solid blue" : "none")};

  /* 关闭图标样式 */
  .ant-tag-close-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 4px;
    background-color: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
  }

  /* 标签文本样式 */
  .ant-tag-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
  }
`;

/**
 * 引用选择器组件
 * 用于显示、选择和管理聊天中的引用资源
 *
 * @param currentRefs - 当前聊天的引用列表
 * @param footerRefs - 页脚引用列表
 * @param rid - 聊天记录ID
 * @param isFooter - 是否为页脚输入框
 */
export const ReferencePicker = ({
  currentRefs,
  // footerRefs,
  rid,
  isFooter,
  active,
}: Pick<ChatInputProps, "currentRefs"> & {
  rid: string | number;
  isFooter?: boolean;
  footerRefs?: TagRefType[];
  active?: boolean;
}) => {
  // 当前选中的引用索引
  const [selectedRefIndex, setSelectedRefIndex] = useState<number>(0);
  const { currentChatRid } = useChatStore()
  // 从全局状态获取引用相关数据和方法
  const {
    footerRefs,
    // sessionId,
    // currentChatRid,
    currentChat,
    setCurrentChat,
    updateCurrentChat,
    updateChatList,
    deleteFooterRef,
  } = useChatStore();

  const renderCurrentView = () => {
    if (currentChatRid === rid) {
      let curNodeInfo = currentRefs?.[selectedRefIndex]

      if (isFooter) {
        curNodeInfo = footerRefs?.[selectedRefIndex] as any
      }


      if (!curNodeInfo) return null

      if (curNodeInfo?.type === PreviewType.pdf) {
        return <PdfView content={curNodeInfo?.title as string} />
      }

      if (curNodeInfo?.type === PreviewType.flow) {
        //需要展示, 引用来源(如果有), 
        const content = curNodeInfo?.content
        if (curNodeInfo?.id) {
          const pdfInfo = getNodeHighlightWithPdf(curNodeInfo?.id)
          return <FlowView content={content} refTitle={pdfInfo?.filename} />
        }

        return <FlowView content={content} refTitle={''} />
      }

      // 其他,如PDF高亮过来的内容,仅展示内容即可
      if (curNodeInfo?.type === PreviewType.pdfHightLight) {
        // pdf高亮直接展示即可
        const content = curNodeInfo?.content
        return <Other content={content} />
      }

      return <> 未配置的类型: {curNodeInfo?.type}</>
    }
    return null
  }

  return (
    <>
      {/* 引用标签栏 */}
      <CharBar>
        {
          (active || isFooter) && <Tooltip title="添加引用">
            <div>
              <Dropdown
                trigger={["click"]}
                placement="topRight"
                overlayStyle={{ minWidth: "200px" }}
                dropdownRender={(menu) => (
                  <div onClick={(e) => e.stopPropagation()}>
                    <Reference
                    // handleClick={(source, ref) => {}}
                    />
                  </div>
                )}
              >
                <LinkOutlined
                  size={20}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                  }}
                />
              </Dropdown>
            </div>
          </Tooltip>
        }

        {currentRefs?.map((tagref, index) => (
          <CustomTag
            $selected={(!!active) && selectedRefIndex === index}
            // $selected={selectedRefIndex === index && currentChatRid === rid}
            // key={index}
            key={`${tagref?.id + index}`}
            closeIcon={active || isFooter}
            onClose={() => {
              // 删除当前聊天引用
              if (currentChat) {
                // 过滤掉要删除的引用
                const refs = currentChat.refs
                  ? currentChat.refs.filter((ref) => ref.id !== tagref.id)
                  : [];
                const newCurrentChat = {
                  ...currentChat,
                  refs: refs,
                };
                updateCurrentChat(newCurrentChat);
                updateChatList(newCurrentChat);
              }
            }}
            onClick={() => {
              setSelectedRefIndex(index)
            }}
          >
            <Tooltip title={tagref.title + (tagref.page ? `(第${tagref.page}页)` : '')}>
              {tagref.title ? tagref.title + (tagref.page ? `(第${tagref.page}页)` : '') : "未命名.node"}
            </Tooltip>
          </CustomTag>
        ))}

        {/* 页脚引用标签 - 仅在页脚输入框时显示 */}
        {isFooter &&
          footerRefs?.map((tagref, index) => (
            <CustomTag
              $selected={selectedRefIndex === index && currentChatRid === rid}
              key={tagref?.id + '_' + index}
              closeIcon={active || isFooter}
              onClose={() => {
                deleteFooterRef(tagref.id);
              }}
              onClick={() => {
                console.log('onClick', index)
                setSelectedRefIndex(index);
              }}
            >
              <Tooltip title={tagref.title + (tagref.page ? `(第${tagref.page}页)` : '')}>
                {tagref.title ? tagref.title + (tagref.page ? `(第${tagref.page}页)` : '') : "未命名.node"}
              </Tooltip>
            </CustomTag>
          ))}
      </CharBar>
      <div>
        {
          renderCurrentView()
        }
      </div>

    </>
  );
};


const PdfView = ({ content = '' }: { content?: string, refTitle?: '' }) => {
  return <div
    className="flex flex-col gap-2.5 justify-center items-center border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom">
    <img src={PdfLarge} alt="" width={50} />
    <div>{
      content
    }</div>
  </div>
}

const FlowView = ({ content = '', refTitle = '' }: { content?: string, refTitle?: string }) => {
  return <CopyableContent
    content={content}
    iconPosition="right-center"
    className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom vditor-reset"
  >
    <div className="line-clamp-4">
      <Remark>{content}</Remark>
    </div>

    {!!refTitle && <>
      <div className="text-sm text-blue-600">节点绑定的文档</div>
      <div className="indent-2">
        《{refTitle}》
      </div>
    </>
    }
  </CopyableContent>
}

const Other = ({ content = '' }: { content?: string }) => {
  return <CopyableContent
    content={content}
    iconPosition="right-center"
    className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom vditor-reset"
  >
    <div className="line-clamp-4">
      <Remark>{content}</Remark>
    </div>
  </CopyableContent>
}

{/* 当前聊天引用标签 */ }
{/* {currentRefs?.map((tagref, index) => (
          <CustomTag
            $selected={selectedRefIndex === index && currentChatRid === rid}
            key={index}
            closeIcon={currentChatRid === rid || isFooter}
            onClose={() => {
              // 删除当前聊天引用
              if (currentChat) {
                // 过滤掉要删除的引用
                const refs = currentChat.refs
                  ? currentChat.refs.filter((ref) => ref.id !== tagref.id)
                  : [];
                const newCurrentChat = {
                  ...currentChat,
                  refs: refs,
                };
                updateCurrentChat(newCurrentChat);
                updateChatList(newCurrentChat);
              }
            }}
            onClick={() => setSelectedRefIndex(index)}
          >
            <Tooltip title={tagref.title + (tagref.page ? `(第${tagref.page}页)` : '')}>
              {tagref.title ? tagref.title + (tagref.page ? `(第${tagref.page}页)` : '') : "未命名.node"}
            </Tooltip>
          </CustomTag>
        ))} */}

// {/* 页脚引用内容预览 - 仅在页脚输入框且有引用时显示 */}
//       {footerRefs &&
//         isFooter &&
//         footerRefs.length !== 0 &&
//         // TODO: 有时报错
//         footerRefs[selectedRefIndex] &&
//         // sessionId !== 0 &&
//         (footerRefs[selectedRefIndex].type === 1 ? (
//           <div className="flex flex-col gap-2.5 justify-center items-center border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom">
//             {footerRefs[selectedRefIndex] && (
//               <>
// <img src={PdfLarge} alt="" width={50} />
{/* <div>{footerRefs[selectedRefIndex]?.title as string}</div>
              </>
            )} */}
//           </div>
//         ) : footerRefs[selectedRefIndex].type === 4 ? (
//           // 图片类型引用预览
//           <CopyableContent
//             content={footerRefs[selectedRefIndex]?.content as string}
//             iconPosition="right-center"
//             className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom"
//           >
//             <div className="flex flex-col items-center">
//               <img
//                 src={footerRefs[selectedRefIndex].content}
//                 className="max-w-full max-h-[350px] object-contain"
//               />
//               <div></div>
//             </div>
//           </CopyableContent>
//         ) : (
//           // 节点类型引用预览 - 使用可复制内容组件
// <CopyableContent
//   content={footerRefs[selectedRefIndex]?.content as string}
//   iconPosition="right-center"
//   className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom vditor-reset"
// >
//   <div>
//     <Remark>{footerRefs[selectedRefIndex]?.content as string}</Remark>
//   </div>
// </CopyableContent>
//         ))}


// {/* 当前聊天引用内容预览 - 仅在当前聊天且有引用时显示 */}
//       {currentRefs &&
//         currentChatRid === rid &&
//         currentRefs.length !== 0 &&
//         sessionId !== 0 &&
//         (currentRefs && currentRefs[selectedRefIndex].type === 1 ? (
//           <div className="flex flex-col gap-2.5 justify-center items-center border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom">
//             {currentRefs[selectedRefIndex] && (
//               <>
//                 <img src={PdfLarge} alt="" width={50} />
//                 <div>{currentRefs[selectedRefIndex]?.title as string}</div>
//               </>
//             )}
//           </div>
//         ) : currentRefs[selectedRefIndex].type === 4 ? (
//             // 图片类型引用预览
//             <CopyableContent
//                 content={currentRefs[selectedRefIndex]?.content as string}
//                 iconPosition="bottom-right"
//                 className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom"
//             >
//               <div className="flex flex-col items-center">
//                 <img
//                     src={currentRefs[selectedRefIndex].content}
//                     className="max-w-full max-h-[350px] object-contain"
//                 />
//                 <div></div>
//               </div>
//             </CopyableContent>
//         ) : (
//             // 节点类型引用预览 - 使用可复制内容组件
// <CopyableContent
//     content={currentRefs[selectedRefIndex]?.content as string}
//     iconPosition="bottom-right"
//     className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom"
// />
//         ))}
import { useState, useCallback, useEffect } from 'react';

interface UseTreeStateProps {
  currentWorkspaceId: string;
}

export const useTreeState = ({ currentWorkspaceId }: UseTreeStateProps) => {
  // 使用 localStorage 存储展开状态
  const getStorageKey = useCallback(() => {
    return `file-tree-expanded-${currentWorkspaceId}`;
  }, [currentWorkspaceId]);

  // 获取保存的展开状态
  const getSavedExpandedItems = useCallback((): string[] => {
    if (!currentWorkspaceId) return [];
    
    try {
      const saved = localStorage.getItem(getStorageKey());
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.warn('Failed to load tree state:', error);
      return [];
    }
  }, [currentWorkspaceId, getStorageKey]);

  // 保存展开状态
  const saveExpandedItems = useCallback((expandedItems: string[]) => {
    if (!currentWorkspaceId) return;
    
    try {
      localStorage.setItem(getStorageKey(), JSON.stringify(expandedItems));
    } catch (error) {
      console.warn('Failed to save tree state:', error);
    }
  }, [currentWorkspaceId, getStorageKey]);

  // 获取选中的项目
  const getSavedSelectedItems = useCallback((): string[] => {
    if (!currentWorkspaceId) return [];
    
    try {
      const saved = localStorage.getItem(`${getStorageKey()}-selected`);
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.warn('Failed to load selected items:', error);
      return [];
    }
  }, [currentWorkspaceId, getStorageKey]);

  // 保存选中的项目
  const saveSelectedItems = useCallback((selectedItems: string[]) => {
    if (!currentWorkspaceId) return;
    
    try {
      localStorage.setItem(`${getStorageKey()}-selected`, JSON.stringify(selectedItems));
    } catch (error) {
      console.warn('Failed to save selected items:', error);
    }
  }, [currentWorkspaceId, getStorageKey]);

  // 清理过期的状态
  const cleanupExpiredState = useCallback(() => {
    if (!currentWorkspaceId) return;
    
    try {
      const keys = Object.keys(localStorage);
      const prefix = 'file-tree-expanded-';
      const currentKey = getStorageKey();
      
      keys.forEach(key => {
        if (key.startsWith(prefix) && key !== currentKey) {
          // 删除其他工作区的状态
          localStorage.removeItem(key);
          localStorage.removeItem(`${key}-selected`);
        }
      });
    } catch (error) {
      console.warn('Failed to cleanup expired state:', error);
    }
  }, [currentWorkspaceId, getStorageKey]);

  // 当工作区ID变化时清理过期状态
  useEffect(() => {
    cleanupExpiredState();
  }, [currentWorkspaceId, cleanupExpiredState]);

  return {
    getSavedExpandedItems,
    saveExpandedItems,
    getSavedSelectedItems,
    saveSelectedItems,
    cleanupExpiredState
  };
}; 
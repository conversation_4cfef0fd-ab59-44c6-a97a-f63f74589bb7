import {useState} from "react";
import {useNavigate} from "react-router-dom";
import {Dropdown, message} from "antd";

import {DeleteOutlined, EditOutlined, EllipsisOutlined,} from "@ant-design/icons";
import {CollapseIcon, ExpandIcon, HomeIcon, TagIcon,} from "@/components/icons";
import {ProjectType} from "../response-type";
import {useHomeStore} from "@/store/home-store";
import {deleteProject, updateProject} from "@/api/project";

type Props = {
  project: ProjectType;
  tagColors: { cid: string; color: string }[];
  getCurrentProject: (project: ProjectType) => void;
};
export const ProjectItemGrid = ({
  project,
  tagColors,
  getCurrentProject,
}: Props) => {
  const navigate = useNavigate();
  const [isExpand, setIsExpand] = useState(false);
  const {
    categories,
    categorySelectList,
    currentCategory,
    setCurrentCategory,
    modifyProject,
    removeProjectByWId,
    toggleCategory,
    removeProjectSyncCategory,
  } = useHomeStore();

  return (
    <div
      key={`workspace-${project.wid}-${crypto.randomUUID()}`}
      className="w-full mb-4"
      style={{width: "300px"}}
    >
      <div
          className="relative w-full border-0 rounded-md flex items-center justify-center cursor-pointer overflow-hidden transition-all duration-300"
          style={{
            boxShadow: "0 8px 8px rgba(0, 0, 0, 0.2)",
            paddingBottom: "75%", /* 固定宽高比例 4:3 */
              borderWidth: "3px",
              borderColor: "transparent",
          }}
          onClick={() => {
            navigate(`/workerspace?wid=${project.wid}`);
          }}
          onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = "#EC692D";
          }}
          onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = "transparent"; // 将边框颜色改回透明
          }}
      >
        <div
            className="absolute inset-0 w-full h-full transition-all duration-500 ease-in-out bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `${
                  project.preview
                      ? `url(${project.preview})`
                      : "url(https://b0.bdstatic.com/30e1068eb5baee85ff39977076276655.jpg@h_1280)"
              }`,
              backgroundSize: "contain",
              transform: "scale(1)",
              transformOrigin: "center",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = "scale(1.5)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = "scale(1)";
            }}
        />
        <Dropdown
          menu={{
            style: {
              fontSize: "12px",
              backgroundColor: "var(--bg-category-tabs)",
            },
          }}
          placement="bottomLeft"
          dropdownRender={() => {
            return (
              <div
                className={`relative bg-[var(--bg-category-tabs)] flex flex-col scrollbar-custom overflow-hidden rounded-md px-2.5 py-2.5 ${
                  isExpand
                    ? "w-[287px] max-h-[556px]"
                    : "w-[104px] max-h-[182px]"
                }`}
                onClick={(e) => e.stopPropagation()}
              >
                {categorySelectList.filter((item: any) => {
                  return item.key !== "-1"
                }).map((DropDownitem) => {
                  return (
                    <div
                      key={DropDownitem?.key}
                      title={(DropDownitem as any).label}
                      className={`flex items-center gap-2 mb-2 cursor-pointer  hover:bg-[var(--bg-category-tabs-hover)] rounded-md px-1 py-1 ${
                        DropDownitem?.key === project.cid
                          ? "bg-[var(--bg-category-tabs-hover)] cursor-default"
                          : ""
                      }`}
                      onClick={async (e) => {
                        if (
                          DropDownitem?.key === project.cid ||
                          DropDownitem?.key === "-1"
                        ) {
                          return;
                        }

                        const body = {
                          wid: project.wid,
                          cid:
                            DropDownitem?.key === "-1"
                              ? "0"
                              : DropDownitem?.key,
                          title: project.title,
                        };
                        try {
                          const res = await updateProject(body);
                          if ((res as any)?.code === 0) {
                            modifyProject({
                              ...project,
                              cid: DropDownitem?.key,
                            });
                            toggleCategory(project.cid, DropDownitem?.key);
                            message.success("更新成功");
                            setIsExpand(false);
                          }
                        } catch (error) {
                          message.error((error as any)?.msg);
                        }
                      }}
                    >
                      {DropDownitem?.key === "-1" ? (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <HomeIcon color="#3856ce" size={12} />
                        </div>
                      ) : DropDownitem?.key === "0" ? (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                            background: "#8e9bd6",
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <TagIcon color="#ffffff" className="w-5 h-5" />
                        </div>
                      ) : (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                            background: tagColors.find(
                              (colorItem) => colorItem.cid === DropDownitem?.key
                            )?.color,
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <TagIcon color="#ffffff" className="w-5 h-5" />
                        </div>
                      )}
                      <div className="truncate">
                        {(DropDownitem as any).label}
                      </div>
                    </div>
                  );
                })}
                <div
                  className="absolute bottom-[2px] right-[2px]"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsExpand(!isExpand);
                  }}
                >
                  {isExpand ? (
                    <ExpandIcon color="var(--text-primary)" />
                  ) : (
                    <CollapseIcon color="var(--text-primary)" />
                  )}
                </div>
              </div>
            );
          }}
        >
          <div
            className="absolute top-2.5 right-2.5 p-1 rounded-md backdrop-blur-sm"
            style={{
              backgroundColor:
                project.cid === "0"
                  ? "#8e9bd6"
                  : tagColors.find(
                      (colorItem) => colorItem.cid === project?.cid
                    )?.color,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <TagIcon color="#ffffff" className="w-5 h-5" />
          </div>
        </Dropdown>
      </div>
      <div className="w-full flex items-center justify-between mt-[10px]">
        <div className="truncate text-[18px] tracking-[0.9px]">
          {project.title}
        </div>
        <Dropdown
          placement="bottom"
          dropdownRender={() => {
            const MenuItem = [
              {
                key: "rename",
                label: "重命名",
                icon: <EditOutlined />,
                onClick: () => {
                  getCurrentProject(project);
                },
              },
              {
                key: "delete",
                label: "删除",
                icon: <DeleteOutlined />,
                onClick: async () => {
                  try {
                    const res = await deleteProject({
                      wid: project.wid,
                    });
                    if ((res as any).code === 0) {
                      message.success("删除成功");
                      removeProjectByWId(project.wid);
                      const categoryItem = categories.find(
                        (category) => category.cid === project.cid
                      );
                      const allItem = categories.find(
                        (item) => item.cid === "-1"
                      );
                      if (categoryItem && allItem) {
                        removeProjectSyncCategory(
                          project.cid === "-1" ? "0" : project.cid
                        );
                        if (currentCategory) {
                          setCurrentCategory({
                            ...currentCategory,
                            wnum: currentCategory.wnum - 1,
                          });
                        }
                      }
                    }
                  } catch (error) {
                    message.error((error as any).msg);
                  }
                },
              },
            ];
            return (
              <div className="bg-white rounded-md shadow-lg py-1">
                {MenuItem.map((menuItem) => (
                  <div
                    key={menuItem.key}
                    className="px-4 py-2 hover:bg-[var(--bg-category-tabs-hover)] flex items-center gap-2 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      menuItem.onClick?.();
                    }}
                  >
                    {menuItem.icon}
                    <span>{menuItem.label}</span>
                  </div>
                ))}
              </div>
            );
          }}
        >
            <EllipsisOutlined className="hover:bg-[var(--bg-category-tabs-hover)]"/>
        </Dropdown>
      </div>
      <div className="text-[12px] text-[#a0a0a1] tracking-[0.6px] mt-[2px]">
        {new Date(project.last_at * 1000).toLocaleString()} 更新
      </div>
    </div>
  );
};

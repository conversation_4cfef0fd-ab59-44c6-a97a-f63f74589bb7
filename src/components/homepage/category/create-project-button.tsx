import styled from "styled-components";
import { PlusMini } from "@/components/icons";
import { useHomeStore } from "@/store/home-store";
import { CreateProjectModal } from "@/components/pop-window/create-project-window";
const CreateProjectWrapper = styled.div`
  z-index: 30;
  width: 100%;
  height: 118px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const CreateProject = styled.div`
  position: relative;
  background: linear-gradient(to right, #cdd2e9, transparent);
  border: 1px solid transparent;
  border-radius: 24px;
  padding: 10px;
  width: 240px;
  height: 68px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 11px;
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 24px;
    padding: 1px;
    background: linear-gradient(90deg, #3d56ba, transparent);
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }

  // 确保内容在渐变层之上
  > * {
    position: relative;
    z-index: 1;
  }
`;

export const CreateProjectButton = () => {
  const { createProjectModalVisible, setCreateProjectModalVisible } =
    useHomeStore();
  return (
    <CreateProjectWrapper>
      <CreateProject
        className="cursor-pointer flex items-center gap-[17px]"
        onClick={() => setCreateProjectModalVisible(true)}
      >
        <PlusMini color="#3D56BA" size={15} />
        <div className="text-[#3D56BA] text-[18px]">New Project</div>
      </CreateProject>
      {createProjectModalVisible && (
        <CreateProjectModal
          visible={createProjectModalVisible}
          onClose={() => {
            setCreateProjectModalVisible(false);
          }}
        />
      )}
    </CreateProjectWrapper>
  );
};

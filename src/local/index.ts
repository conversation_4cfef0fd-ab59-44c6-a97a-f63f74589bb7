import {getDatabase} from './db/index';
// 导入服务类
import {WorkspaceService} from './services/workspace-service';
import {NodeService} from './services/node-service';
import {SessionService} from './services/session-service';
import {CategoryService} from './services/category-service';
import {TagService} from './services/tag-service';
import {FolderService} from './services/folder-service';
import {generateId, generateIds} from './utils/id-generator';

// 导出数据库函数
export {getDatabase};

// 导出ID生成工具
export {generateId, generateIds};

// 导出服务类
export {WorkspaceService} from './services/workspace-service';
export {NodeService} from './services/node-service';
export {SessionService} from './services/session-service';
export {CategoryService} from './services/category-service';
export {TagService} from './services/tag-service';
export {FolderService} from './services/folder-service';

// 导出服务实例
export const workspaceService = new WorkspaceService();
export const nodeService = new NodeService();
export const sessionService = new SessionService();
export const categoryService = new CategoryService();
export const tagService = new TagService();
export const folderService = new FolderService();

// 导出数据库模型类型
export type {WorkspaceDocType} from './db/schemas/workspace';
export type {CategoryDocType} from './db/schemas/category';
export type {NodeDocType} from './db/schemas/node';
export type {EdgeDocType} from './db/schemas/edge';
export type {RoundDocType} from './db/schemas/round';
export type {AttachmentDocType} from './db/schemas/attachment';
export type {NoteDocType} from './db/schemas/note';
export type {TagGroupDocType} from './db/schemas/tagGroup';
export type {TagDocType} from './db/schemas/tag';
export type {NodeTagDocType} from './db/schemas/nodeTag';

// 默认导出
export default {
  workspaceService,
  nodeService,
  sessionService,
  categoryService,
  tagService,
  folderService,
}; 
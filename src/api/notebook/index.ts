import { httpRequest } from "@/api";
import { SaveNotebookType } from './types.d'

// 获取笔记列表
// export const getNotebook = (wid: string) => httpRequest.get(`/ws/note${wid ? `?wid=${wid}` : ""}`);
export const getNotebook = (wid: string) => httpRequest.get(`/ws/note`, { params: { wid } });
// 保存笔记本
export const saveNotebook = (data: SaveNotebookType) => httpRequest.post<SaveNotebookType>(`/ws/note`, data);

export * from './types.d'
import {Flex, Toolt<PERSON>} from "antd";
import {CloseOutlined, MenuFoldOutlined, MenuUnfoldOutlined} from "@ant-design/icons";
import {IconFontPdf} from "@/common/constant.ts";
import {usePdfStore} from "@/store/pdf-store.ts";
import "../../style/pdf.css";
import {usePanelOpenStore} from "@/store/panel-open-store.tsx";

export const Header = () => {
    const {mode, showMenu, setMode, setShowMenu} = usePdfStore((state) => ({
        mode: state.mode,
        showMenu: state.showMenu,
        setMode: state.setMode,
        setShowMenu: state.setShowMenu,
    }))
    const {togglePdfPanel} = usePanelOpenStore((state) => ({
        togglePdfPanel: state.togglePdfPanel
    }))
    return (
        <Flex justify="space-between" align="center"
              className="w-full h-12 px-8 drag-handle cursor-pointer bg-[#E0E2F4]">
            <div onClick={() => setShowMenu(!showMenu)}>
                <Tooltip title={showMenu ? "折叠目录" : "展示目录"} className="cursor-pointer">
                    {showMenu ? <MenuFoldOutlined/> : <MenuUnfoldOutlined/>}
                </Tooltip>
            </div>
            <Flex justify="space-between" align="center"
                  className="w-32 h-10 px-6 rounded-[10px] bg-[#CDD2E9] opacity-[0.54]">
                <Tooltip title="鼠标模式" placement="bottom">
                    <div
                        className={`w-8 h-8 leading-8 text-center cursor-pointer rounded-[8px] ${mode === 1 ? "mode-active" : "mode-hover"}`}
                        onClick={() => setMode(1)}>
                        <IconFontPdf type="icon-Hand"/>
                    </div>
                </Tooltip>
                <Tooltip title="笔记模式" placement="bottom" className="cursor-pointer ">
                    <div
                        className={`w-8 h-8 leading-8 text-center cursor-pointer rounded-[8px] ${mode === 2 ? "mode-active" : "mode-hover"}`}
                        onClick={() => setMode(2)}>
                        <IconFontPdf type="icon-Select-"/>
                    </div>
                </Tooltip>
                <Tooltip title="添加文字" placement="bottom">
                   <div 
                   className={`w-8 h-8 leading-8 text-center cursor-pointer rounded-[8px] ${mode===3?"mode-active":"mode-hover"}`}
                   onClick={()=>setMode(3)}>
                       <IconFontPdf type="icon-icon_charuwenben" />
                   </div>
                </Tooltip>
            </Flex>
            <CloseOutlined className="cursor-pointer" onClick={() => togglePdfPanel(false)}/>
        </Flex>
    )
};

import React, {useCallback, useRef} from 'react';
import {usePdfStore} from '@/store/pdf-store';
import {DroppableTabContainer} from './DroppableTabContainer';
import {DragItem, DropResult, TabItem} from './types';
import './styles.css';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";

interface DraggableTabSystemProps {
    children?: React.ReactNode;
}

export const DraggableTabSystem: React.FC<DraggableTabSystemProps> = ({children}) => {
    const {
        tabItems,
        activeAid,
        windows,
        setActiveAid,
        setTabItems,
        createWindow,
        addTabToWindow,
        closeWindow,
        removeTabFromWindow,
    } = usePdfStore((state) => ({
        tabItems: state.tabItems,
        activeAid: state.activeAid,
        windows: state.windows,
        setActiveAid: state.setActiveAid,
        setTabItems: state.setTabItems,
        createWindow: state.createWindow,
        addTabToWindow: state.addTabToWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
    }));
    const {dragEnd} = useDragTab()
    const containerRef = useRef<HTMLDivElement>(null);

    // 主标签页点击
    const handleMainTabClick = useCallback((key: string) => {
        setActiveAid(key);
    }, [setActiveAid]);

    // 主标签页关闭
    const handleMainTabClose = useCallback((key: string) => {
        const state = usePdfStore.getState();
        const currentTabItems = state.tabItems || [];
        const newTabItems = currentTabItems.filter((item) => item?.key !== key);
        state.setTabItems(newTabItems);

        if (state.activeAid === key) {
            const index = currentTabItems.findIndex((item) => item?.key === key);
            if (newTabItems.length > 0) {
                const newIndex = index === currentTabItems.length - 1 ? index - 1 : index;
                setActiveAid(newTabItems[newIndex]?.key || '');
            } else {
                setActiveAid('');
            }
        }
    }, [setActiveAid]);

    // 主标签页重排序
    const handleMainTabsReorder = useCallback((tabs: TabItem[]) => {
        const newTabItems = tabs.map(tab => ({
            key: tab.key,
            label: tab.label,
            children: tab.children,
            closable: tab.closable,
        }));
        setTabItems(newTabItems);
    }, [setTabItems]);

    // 主标签页拖拽结束
    const handleMainTabDragEnd = useCallback((item: DragItem, result: DropResult | null, monitor: any) => {
        dragEnd(item, result)
    }, [dragEnd]);

    return (
        <div ref={containerRef} className="h-full flex flex-col flex-1 min-w-0 pdf-main relative">
            {/* 主标签容器 */}
            <DroppableTabContainer
                tabs={tabItems}
                windowId={"main"}
                activeTabKey={activeAid}
                isDraggingTab={false}
                onTabClick={handleMainTabClick}
                onTabClose={handleMainTabClose}
                onTabsReorder={handleMainTabsReorder}
                onTabDragEnd={handleMainTabDragEnd}
                className="flex-shrink-0"
            />

            {/* 主内容区域 */}
            <div className="flex-1 overflow-hidden">
                {children}
            </div>
        </div>
    );
};

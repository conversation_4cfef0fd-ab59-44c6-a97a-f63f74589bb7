import {usePanelOpenStore} from "@/store/panel-open-store.tsx";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {useNoteStore} from "@/store/note-store.ts";
import {useCallback} from "react";

export const useHighlightNote = () => {
    const {toggleNotePanel} = usePanelOpenStore((state) => ({
        toggleNotePanel: state.toggleNotePanel,
    }));
    const {setInsertContent} = useNoteStore((state) => ({
        setInsertContent: state.setInsertContent,
    }))
    const handleCopyToNotebook = useCallback((highlight: CustomHighlight) => {
        if (!usePanelOpenStore.getState().notePanelOpen) {
            toggleNotePanel(true)
        }
        let content = highlight.content?.text || "";
        if (highlight.type == "area") {
            content = `![pdf截图-${new Date().toLocaleString()}](${highlight.content?.image})`
        }
        const aid = usePdfStore.getState().activeAid
        const pdf = usePdfStore.getState().pdfs.get(aid)
        const page = highlight.position.boundingRect?.pageNumber
        if (pdf && page) {
            content = `${content}\n\n（来源：《${pdf.filename}》 第${page}页 ）`
        } else if (pdf) {
            content = `${content}\n\n（来源：《${pdf.filename}》）`
        }
        setInsertContent(content)
    }, [toggleNotePanel, setInsertContent])
    return {
        handleCopyToNotebook
    }
}
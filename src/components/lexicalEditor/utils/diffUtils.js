import DiffMatchPatch from 'diff-match-patch';

// 创建 DMP 实例
const dmp = new DiffMatchPatch();

// 设置 DMP 配置
dmp.Diff_Timeout = 1.0; // 1秒超时
dmp.Diff_EditCost = 4; // 编辑成本

/**
 * 计算中文字符级别的差异
 * @param {string} text1 - 原始文本
 * @param {string} text2 - 修改后的文本
 * @returns {Array} 字符级差异数组
 */
export function computeChineseDiff(text1, text2) {
  // 对中文文本使用更精细的字符级对比
  const diffs = dmp.diff_main(text1, text2);
  dmp.diff_cleanupSemantic(diffs);
  
  return diffs.map((diff, index) => {
    const [operation, text] = diff;
    
    switch (operation) {
      case DiffMatchPatch.DIFF_DELETE:
        return {
          id: `chinese-diff-${index}`,
          type: 'removed',
          text: text,
          operation: operation
        };
      case DiffMatchPatch.DIFF_INSERT:
        return {
          id: `chinese-diff-${index}`,
          type: 'added',
          text: text,
          operation: operation
        };
      case DiffMatchPatch.DIFF_EQUAL:
        return {
          id: `chinese-diff-${index}`,
          type: 'unchanged',
          text: text,
          operation: operation
        };
      default:
        return {
          id: `chinese-diff-${index}`,
          type: 'unchanged',
          text: text,
          operation: operation
        };
    }
  }).filter(diff => diff.text.length > 0);
}

/**
 * 创建补丁
 * @param {string} text1 - 原始文本
 * @param {string} text2 - 修改后的文本
 * @returns {Array} 补丁数组
 */
export function makePatches(text1, text2) {
  return dmp.patch_make(text1, text2);
}

/**
 * 应用补丁到文本
 * @param {string} text - 原始文本
 * @param {Array} patches - 补丁数组
 * @returns {string} 应用补丁后的文本
 */
export function applyPatches(text, patches) {
  const results = dmp.patch_apply(patches, text);
  return results[0]; // 返回应用补丁后的文本
}

/**
 * 获取差异的统计信息
 * @param {Array} diffs - 差异数组
 * @returns {Object} 统计信息
 */
export function getDiffStats(diffs) {
  let additions = 0;
  let deletions = 0;
  let unchanged = 0;
  
  diffs.forEach(diff => {
    switch (diff.type) {
      case 'added':
        additions += diff.text.length;
        break;
      case 'removed':
        deletions += diff.text.length;
        break;
      case 'unchanged':
        unchanged += diff.text.length;
        break;
    }
  });
  
  return {
    additions,
    deletions,
    unchanged,
    total: additions + deletions + unchanged,
    changes: additions + deletions
  };
} 
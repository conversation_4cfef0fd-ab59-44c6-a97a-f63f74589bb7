import {getDocument, GlobalWorkerOptions} from 'pdfjs-dist'

interface pdf {
    id: string;
    url: Blob | string;
}

GlobalWorkerOptions.workerSrc = "https://unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs";
/**
 * 异步获取所有PDF文本内容
 * @returns 返回一个Promise，解析为包含PDF文本内容的数组
 */
export const fetchAllPdfContent = async (pdfs: pdf[]): Promise<Map<string, string>> => {
    const map = new Map();
    try {
        if (pdfs.length === 0) {
            return map
        }
        // 异步获取每个PDF的文本内容
        const contentPromises = pdfs.map(async (pdf) => {
            try {
                // 获取PDF的文本内容
                const textContent = await extractTextFromPdf(pdf.url);
                map.set(pdf.id, textContent)
            } catch (error) {
                console.error(`获取PDF ${pdf.id} ${pdf.url} 内容失败:`, error);
            }
        });

        // 等待所有PDF内容获取完成
        await Promise.all(contentPromises);
        return map
    } catch (error) {
        console.error('获取PDF内容失败:', error);
        return map
    }
};

/**
 * 从PDF Blob中提取文本内容
 * @param pdfUrlOrBlob PDF文件的Blob对象或路径
 * @returns 返回提取的文本内容
 */
async function extractTextFromPdf(pdfUrlOrBlob: Blob | string): Promise<string> {
    try {
        let arrayBuffer: ArrayBuffer;

        if (pdfUrlOrBlob instanceof Blob) {
            // 如果是Blob对象，直接调用arrayBuffer方法
            arrayBuffer = await pdfUrlOrBlob.arrayBuffer();
        } else if (typeof pdfUrlOrBlob === 'string') {
            // 如果是URL字符串，通过fetch获取ArrayBuffer
            const response = await fetch(pdfUrlOrBlob);
            if (!response.ok) {
                throw new Error(`无法加载PDF文件: ${pdfUrlOrBlob}`);
            }
            arrayBuffer = await response.arrayBuffer();
        } else {
            throw new TypeError('pdfUrlOrBlob 必须是Blob对象或字符串');
        }

        // 加载PDF文档
        const loadingTask = getDocument(arrayBuffer);
        const pdf = await loadingTask.promise;

        let textContent = '';

        // 遍历所有页面并提取文本
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const content = await page.getTextContent();

            // 将文本内容连接起来
            const pageText = content.items
                .map((item: any) => item.str)
                .join(' ');

            textContent += pageText + '\n';
        }

        return textContent;
    } catch (error) {
        console.error(`提取PDF ${pdfUrlOrBlob} 文本失败:`, error);
        return '';
    }
} 
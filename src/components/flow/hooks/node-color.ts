import {useCallback} from "react";
import {updateNode} from "@/api/canvas";
import {useFlowStore} from "@/store/flow-store.ts";
import {getNodeHighlight, usePdfStore} from "@/store/pdf-store.ts";

const useNodeColor = () => {
    // flow
    const {updateNodes} = useFlowStore((state) => ({
        updateNodes: state.updateNodes
    }))
    // pdf
    const {updateHighlights} = usePdfStore((state) => ({
        updateHighlights: state.updateHighlights
    }))
    // 选择颜色
    const handleColorChange = useCallback(async (id: string, color: string) => {
        // 接口
        try {
            await updateNode({nid: id, color: color.slice(1), type: 3})
        } catch (e) {
            console.log(e)
            return
        }
        // 更改节点
        updateNodes([{id: id, data: {color: color}}])
        // 更改高亮
        const highlights = getNodeHighlight([id])
        if (highlights.length === 0) {
            return
        }
        const highlight = highlights[0]
        updateHighlights(highlight.aid, {id: highlight.id, color: color})
    }, [updateNodes, updateHighlights])

    return {
        handleColorChange
    }
}

export {useNodeColor}
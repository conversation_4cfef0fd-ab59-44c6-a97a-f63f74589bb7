// 主要组件
export { TagManagementPageRefactored as TagManagementPage } from './TagManagementPageRefactored';
export { TagGroup, type Tag } from './TagGroup';
export { TagItem, type TagItemProps } from './TagItem';

// 数据类型
export { type TagGroupData } from './mock-data';

// 状态管理
export { 
  useTagStore, 
  useTagGroups, 
  useTagActions, 
  useTagQueries,
  type TagStore 
} from './stores/useTagStore';

// 业务逻辑 Hooks
export { useTagOperations, type TagOperations } from './hooks/useTagOperations';
export { 
  useDragAndDrop, 
  useDragStyles, 
  useDragStatus,
  type DragItem,
  type DragState,
  type DragConfig 
} from './hooks/useDragAndDrop';
export { 
  useModalManager,
  type ModalType,
  type ModalMode,
  type ModalState,
  type FormData 
} from './hooks/useModalManager';
export {TagPanel} from './TagPanel'

// 数据
export { mockTagGroups } from './mock-data';
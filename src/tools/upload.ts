import {uploadImageToOss} from "@/api/image";

export const uploadBase64 = async (base64String: string): Promise<string> => {
    // 将 base64 转换为 Blob
    const parts = base64String.split(';base64,');
    const contentType = parts[0].split(':')[1];
    const raw = window.atob(parts[1]);
    const uInt8Array = new Uint8Array(raw.length);

    for (let i = 0; i < raw.length; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
    }

    const blob = new Blob([uInt8Array], {type: contentType});
    const file = new File([blob], 'screenshot.png', {type: contentType});
    // 获取签名
    const policyString = localStorage.getItem("image_oss_signature")
    const policyJson = JSON.parse(policyString || "");
    const filename = `${policyJson.dir}/${crypto.randomUUID()}.png`
    // 上传图片
    const res = await uploadImageToOss({
        fileName: filename,
        file,
        ossInfo: policyJson
    });
    if (res != 200) {
        return Promise.reject("图片上传失败");
    }
    return Promise.resolve(filename)
}
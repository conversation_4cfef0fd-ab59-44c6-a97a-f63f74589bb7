import {RxJsonSchema} from 'rxdb';

export interface WorkspaceDocType {
    id: string; // 工作区ID
    title: string; // 标题
    cate_id: string; // 分类ID
    last_at: number; // 最后更新时间
    del_id: string; // 删除ID，0表示未删除，否则为删除时的工作区ID
    create_at: number; // 创建时间
    update_at: number; // 更新时间
    del_at: number; // 删除时间，0表示未删除
}

export const workspaceSchema: RxJsonSchema<WorkspaceDocType> = {
    title: 'workspace schema',
    version: 0,
    description: '工作区模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        title: {
            type: 'string',
            maxLength: 100
        },
        cate_id: {
            type: 'string',
            maxLength: 100
        },
        last_at: {
            type: 'number',
            minimum: 0
        },
        del_id: {
            type: 'string',
            maxLength: 100
        },
        del_at: {
            type: 'number',
            minimum: 0
        },
        create_at: {
            type: 'number',
            minimum: 0
        },
        update_at: {
            type: 'number',
            minimum: 0
        }
    },
    attachments: {},
    required: ['id', 'title', 'cate_id', 'last_at', 'del_id', 'del_at'],
    indexes: [
        // 单字段索引
        'cate_id',
        'last_at',
        'del_id',
        
        // 复合索引 - 按分类和删除状态查询
        ['cate_id', 'del_id'],
        
        // 复合索引 - 按删除状态和最后更新时间排序
        ['del_id', 'last_at'],
        
        // 复合索引 - 标题和删除状态
        // 这个索引可以用来确保在相同的del_id下，title是唯一的
        ['title', 'del_id']
    ]
}; 
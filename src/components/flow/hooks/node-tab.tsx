import { useEffect } from "react";
import { useFlowStore } from "@/store/flow-store";
import { useNodeAdd } from "@/components/flow/hooks/node-add";

export const useTabAddNode = () => {
    const { handleAddNode } = useNodeAdd();

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (
                e.key === "Tab" &&
                !e.defaultPrevented &&
                document.activeElement?.tagName !== "INPUT" &&
                document.activeElement?.tagName !== "TEXTAREA"
            ) {
                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                if (selectedNodes.length > 0) {
                    e.preventDefault();
                    selectedNodes.forEach((node, idx) => {
                        handleAddNode({ pids: [node.id], offsetIndex: idx });
                    });
                }
            }
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [handleAddNode]);
};
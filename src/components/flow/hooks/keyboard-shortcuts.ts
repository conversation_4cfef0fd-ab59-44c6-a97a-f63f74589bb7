import { useEffect, useCallback } from 'react';

interface UseKeyboardShortcutsProps {
  onCopy: () => void;
  onPaste: () => Promise<boolean> | boolean;
  onCut?: () => void;
  onDelete?: () => void;
  onSelectAll?: () => void;
  enabled?: boolean;
}

export const useKeyboardShortcuts = ({
  onCopy,
  onPaste,
  onCut,
  onDelete,
  onSelectAll,
  enabled = true
}: UseKeyboardShortcutsProps) => {
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // 检查焦点是否在可编辑元素上
    const target = event.target as HTMLElement;
    const isEditableElement = 
      target.isContentEditable ||
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.getAttribute('contenteditable') === 'true';

    if (isEditableElement) return;

    const isCtrlOrCmd = event.ctrlKey || event.metaKey;

    // 复制 (Ctrl/Cmd + C)
    if (isCtrlOrCmd && event.key === 'c') {
      event.preventDefault();
      event.stopPropagation();
      onCopy();
      return;
    }

    // 粘贴 (Ctrl/Cmd + V)
    if (isCtrlOrCmd && event.key === 'v') {
      event.preventDefault();
      event.stopPropagation();
      // 支持异步paste函数
      Promise.resolve(onPaste()).catch(error => {
        console.error('粘贴操作失败:', error);
      });
      return;
    }

    // 剪切 (Ctrl/Cmd + X)
    if (isCtrlOrCmd && event.key === 'x' && onCut) {
      event.preventDefault();
      event.stopPropagation();
      onCut();
      return;
    }

    // 删除 (Delete 或 Backspace)
    if ((event.key === 'Delete' || event.key === 'Backspace') && onDelete) {
      event.preventDefault();
      event.stopPropagation();
      onDelete();
      return;
    }

    // 全选 (Ctrl/Cmd + A)
    if (isCtrlOrCmd && event.key === 'a' && onSelectAll) {
      event.preventDefault();
      event.stopPropagation();
      onSelectAll();
      return;
    }

  }, [enabled, onCopy, onPaste, onCut, onDelete, onSelectAll]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    enabled
  };
}; 
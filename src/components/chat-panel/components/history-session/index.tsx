import { FC } from "react";
import { SessionList } from "../session-list";
import { HistoryOutlined, CloseOutlined } from "@ant-design/icons";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import styled from "styled-components";

const HistoryPanelContainer = styled.div`
  background: #f8f9ff;
  border-left: 1px solid #e6e8f0;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const HistoryHeader = styled.div`
  background: #ffffff;
  border-bottom: 1px solid #e6e8f0;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
`;

const HeaderTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #4451af;
`;

const CloseButton = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  color: #959dcd;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e6e8f0;
    color: #4451af;
  }
`;

const HistoryContent = styled.div`
  flex: 1;
  padding: 16px 12px;
  overflow: hidden;
`;

export const HistorySession: FC = () => {
  const { setOpenHistory } = useChatStore();

  return (
    <HistoryPanelContainer>
      {/* 头部标题栏 */}
      {/* <HistoryHeader>
        <HeaderTitle>
          <HistoryOutlined />
          历史会话
        </HeaderTitle>
        <CloseButton 
          onClick={() => setOpenHistory()}
          title="关闭历史面板"
        >
          <CloseOutlined style={{ fontSize: '14px' }} />
        </CloseButton>
      </HistoryHeader> */}

      {/* 会话列表内容区域 */}
      <HistoryContent>
        <SessionList />
      </HistoryContent>
    </HistoryPanelContainer>
  );
};

{"name": "pdf-flow-space", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ant-design/icons": "^5.5.2", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.3", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@atlaskit/pragmatic-drag-and-drop-react-beautiful-dnd-migration": "^2.0.4", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.2.1", "@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@excalidraw/excalidraw": "^0.18.0", "@headless-tree/core": "^1.2.1", "@headless-tree/react": "^1.2.1", "@joplin/turndown-plugin-gfm": "^1.0.61", "@lexical/code": "^0.33.1", "@lexical/code-shiki": "^0.33.2-nightly.20250721.0", "@lexical/file": "^0.33.1", "@lexical/hashtag": "^0.33.1", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/mark": "^0.33.1", "@lexical/markdown": "^0.33.1", "@lexical/overflow": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@lexical/selection": "^0.33.1", "@lexical/table": "^0.33.1", "@lexical/utils": "^0.33.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@tailwindcss/line-clamp": "^0.4.4", "@types/dompurify": "^3.2.0", "@types/jspdf": "^2.0.0", "@types/markdown-it": "^14.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.4.2", "ai": "^4.3.16", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "animate.css": "^4.1.1", "antd": "^5.22.7", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.4", "draft-js": "^0.11.7", "excalidraw^0.18.0": "link:@excalidraw/excalidraw^0.18.0", "gh-pages": "^6.3.0", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "immer": "^10.1.1", "install": "^0.13.0", "joplin-turndown-plugin-gfm": "^1.0.12", "jspdf": "^3.0.0", "katex": "^0.16.21", "less": "^4.4.0", "lexical": "^0.33.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.488.0", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "markdown-it-copy": "^1.2.0", "markdown-it-katex": "^2.0.3", "markdown-it-table": "^4.1.1", "markdown-it-task-lists": "^2.1.1", "marked": "^15.0.7", "monaco-editor": "^0.52.2", "nanoid": "^5.0.9", "pdfjs-dist": "4.4.168", "pnpm": "^9.15.3", "pouchdb-adapter-idb": "^9.0.0", "prettier": "^3.6.2", "prismjs": "^1.30.0", "quill": "^2.0.3", "quill-image-resize-module-react": "^3.0.0", "quill-markdown": "^2.1.2", "quill-markdown-shortcuts": "^0.0.10", "quilljs-markdown": "^1.2.0", "rangy": "^1.3.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-complex-tree": "^2.6.0", "react-copy-to-clipboard": "^5.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^6.0.0", "react-grid-layout": "^1.5.0", "react-intersection-observer": "^9.15.0", "react-markdown": "^9.0.3", "react-quill": "^2.0.0", "react-remark": "^2.1.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "react-rnd": "^10.4.14", "react-router-dom": "6", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-prism-plus": "^2.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "rxdb": "^16.8.0", "rxjs": "^7.8.2", "sonner": "^2.0.6", "styled-components": "^6.1.13", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-debounce": "^4.0.0", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "uuid": "^11.1.0", "vditor": "^3.11.0", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zustand": "^5.0.2"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "deploy": "gh-pages -d build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/draft-js": "^0.11.18", "@types/lodash": "^4.17.15", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.0.0", "@types/react": "^19.0.6", "@types/react-color": "^3.0.13", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dnd": "^3.0.2", "@types/react-dnd-html5-backend": "^3.0.2", "@types/react-dom": "^19.0.3", "@types/react-resizable": "^3.0.8", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "sass": "^1.85.1", "sass-embedded": "^1.85.1", "typescript": "^5.7.2", "vite": "^5.1.0", "vite-plugin-svgr": "^4.2.0"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}
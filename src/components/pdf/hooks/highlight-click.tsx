import {useCallback} from "react";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {Tip, ViewportHighlight, viewportPositionToScaled} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import {useFlowStore} from "@/store/flow-store.ts";

export const useHighlightClick = () => {
    const {updateNodes, setCenter} = useFlowStore((state) => ({
        updateNodes: state.updateNodes,
        setCenter: state.setCenter
    }))
    // 文本高亮点击
    const handleTextClick = useCallback((
        highlight: ViewportHighlight<CustomHighlight>,
    ) => {
        const pdfState = usePdfStore.getState()
        const highlighterUtils = pdfState.pdfs.get(highlight.aid)?.pdfHighlighterUtils!
        // 自定义高亮
        const customHighlight = {
            ...highlight,
            position: viewportPositionToScaled(highlight.position, highlighterUtils.getViewer()!)
        }
        // 工具栏
        const highlightTip: Tip = {
            position: highlight.position,
            content: <HighlightToolbar customHighlight={customHighlight}/>,
        };
        // 展示工具栏
        highlighterUtils.setTip(highlightTip);
        // 节点
        updateNodes([{id: highlight.nid, selected: true, selectedFromHighlight: true}], true)
        setCenter(highlight.nid)
    }, [updateNodes, setCenter])
    return {
        handleTextClick
    }
}
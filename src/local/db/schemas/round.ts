import {RxJsonSchema} from 'rxdb';

export interface RoundDocType {
    id: string; // 轮次ID
    sid: string; // 会话ID
    rid: number; // 轮次序号
    question: string; // 问题
    answer: string; // 回答
    refs: Array<{
        type: number; // 引用类型（1:附件、2：节点、3：文本高亮 4：区域高亮）
        id: string; // 引用ID (附件id、节点id、高亮id)
        title: string; // 标题
        content: string; // 内容
        page: number; // 页码
    }>; // 引用
}

export const roundSchema: RxJsonSchema<RoundDocType> = {
    title: 'round schema',
    version: 0,
    description: '对话轮次模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        sid: {
            type: 'string',
            maxLength: 100
        },
        rid: {
            type: 'integer',
            minimum: 0
        },
        question: {
            type: 'string'
        },
        answer: {
            type: 'string'
        },
        refs: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    type: {
                        type: 'integer'
                    },
                    id: {
                        type: 'string'
                    },
                    title: {
                        type: 'string'
                    },
                    content: {
                        type: 'string'
                    },
                    page: {
                        type: 'integer'
                    }
                }
            }
        }
    },
    required: ['id', 'sid', 'rid', 'question'],
    indexes: ['sid', 'rid']
}; 
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {GhostHighlight, scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {useCallback} from "react";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import {useHighlightAdd} from "@/components/pdf/hooks/highlight-add.tsx";

export const usePdfSelection = () => {
    const {addHighlight} = useHighlightAdd()
    const onSelection = useCallback((aid: string, highlight: GhostHighlight) => {
        if (highlight.type === "text" && highlight.content.text?.trim().length === 0) {
            return
        }
        const state = usePdfStore.getState()
        const customHighlight: CustomHighlight = {
            id: crypto.randomUUID(),
            aid: aid,
            type: highlight.type,
            content: highlight.content,
            position: highlight.position,
            color: state.defaultColor,
            nid: "" // 不关联节点
        }
        const highlighterUtils = state.pdfs.get(aid)?.pdfHighlighterUtils!
        if (state.mode === 1) {
            // 工具栏
            const highlightTip: Tip = {
                position: scaledPositionToViewport(highlight.position, highlighterUtils.getViewer()!),
                content: <HighlightToolbar customHighlight={customHighlight}/>,
            }
            highlighterUtils.setTip(highlightTip)
            return
        }
        addHighlight(customHighlight)
    }, [addHighlight])
    return {
        onSelection
    }
}
# 标签页拖拽功能测试指南

## 功能概述

本次实现了以下三个核心功能：
1. **开始拖动标签页时，隐藏窗口标签栏**
2. **结束拖动标签页时，显示窗口标签栏**
3. **拖动标签页始终紧贴窗口左上角**

## 测试步骤

### 测试环境准备
1. 启动开发服务器：`npm run dev`
2. 打开浏览器访问应用
3. 确保有多个 PDF 标签页可供测试

### 测试场景 1：主标签栏拖拽创建新窗口

**步骤：**
1. 在主标签栏中选择一个标签页
2. 开始拖拽标签页，将其拖出标签栏区域
3. 观察是否创建了新窗口
4. 继续拖拽，观察窗口是否跟随鼠标移动
5. 释放鼠标，完成拖拽

**预期结果：**
- ✅ 拖拽开始时，新创建的窗口应该隐藏标签栏
- ✅ 拖拽过程中，窗口应该紧贴鼠标左上角位置
- ✅ 拖拽结束时，窗口应该显示标签栏

### 测试场景 2：单标签窗口拖拽

**步骤：**
1. 创建一个只有单个标签的浮动窗口
2. 拖拽该窗口中的标签页
3. 观察窗口移动行为

**预期结果：**
- ✅ 拖拽开始时，窗口标签栏应该隐藏
- ✅ 整个窗口应该跟随鼠标移动，紧贴鼠标左上角
- ✅ 拖拽结束时，窗口标签栏应该重新显示
- ✅ 不应该创建新窗口

### 测试场景 3：多标签窗口拖拽

**步骤：**
1. 创建一个包含多个标签的浮动窗口
2. 拖拽其中一个标签页到窗口外
3. 观察新窗口的创建和行为

**预期结果：**
- ✅ 拖拽开始时，原窗口和新窗口的标签栏都应该隐藏
- ✅ 新创建的窗口应该紧贴鼠标左上角
- ✅ 拖拽结束时，两个窗口的标签栏都应该重新显示

### 测试场景 4：标签栏合并

**步骤：**
1. 从一个窗口拖拽标签页到另一个窗口的标签栏
2. 观察合并过程中的标签栏显示状态

**预期结果：**
- ✅ 拖拽开始时，源窗口标签栏应该隐藏
- ✅ 拖拽到目标窗口时，应该能正常合并
- ✅ 拖拽结束时，目标窗口标签栏应该正常显示

## 调试信息

如果功能不正常，可以检查以下内容：

### 浏览器控制台
- 查看是否有 JavaScript 错误
- 检查自定义事件是否正常触发：
  - `tabDragStart` 事件
  - `tabDragComplete` 事件

### 关键代码位置
- `DraggableTab.tsx`: 事件发送逻辑
- `FloatingWindow.tsx`: 事件监听和标签栏显示控制
- `TabDragMonitor.tsx`: 窗口位置跟随逻辑

### 常见问题排查

**问题 1：标签栏没有隐藏**
- 检查 `tabDragStart` 事件是否正常发送
- 检查 `FloatingWindow` 是否正确监听事件
- 检查窗口状态更新是否生效

**问题 2：窗口位置不正确**
- 检查 `TabDragMonitor.tsx` 中的位置计算逻辑
- 确认所有偏移量都已移除
- 检查鼠标位置获取是否正确

**问题 3：标签栏没有重新显示**
- 检查 `tabDragComplete` 事件是否正常发送
- 检查拖拽结束时的状态重置逻辑

## 性能注意事项

- 拖拽过程中使用了 `setTimeout` 来避免频繁的状态更新
- 事件监听器在组件卸载时会正确清理
- 窗口位置更新有边界检查，防止窗口超出屏幕范围

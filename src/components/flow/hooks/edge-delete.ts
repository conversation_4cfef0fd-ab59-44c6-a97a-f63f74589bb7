import {Edge} from "@xyflow/react";
import {useFlowStore} from "@/store/flow-store.ts";
import {deleteLink} from "@/api/canvas";
import {getWsId} from "@/tools/params.ts";
import {useCallback} from "react";

const useEdgeDelete = () => {
    const {deleteEdges} = useFlowStore((state) => ({
        deleteEdges: state.deleteEdges
    }))
    // 删除边
    const handleDeleteEdge = useCallback(async (ids: string[]) => {
        try {
            for (let id of ids) {
                await deleteLink({wid: getWsId(), eid: id})
            }
            deleteEdges(ids)
        } catch (e) {
            console.log(e)
        }
    }, [deleteEdges])
    // flow 删除边
    const onEdgesDelete = useCallback(async (deletedEdges: Edge[]) => {
        const deletedIds = deletedEdges.map((edge) => edge.id);
        await handleDeleteEdge(deletedIds)
    }, [handleDeleteEdge])

    return {
        handleDeleteEdge,
        onEdgesDelete
    }
}

export {useEdgeDelete}
import { create } from 'zustand' // 从 zustand 库导入 create 函数
import type { IHighlight } from 'react-pdf-highlighter' // 导入 react-pdf-highlighter 库中的 IHighlight 类型

// 扩展的高亮接口，基于 IHighlight 并添加 color 和 uuid 属性
export interface ExtendedHighlight extends IHighlight {
  color: string; // 高亮颜色
  aid: string;
}


// 定义高亮存储的状态类型
export interface HighlighterStore {
  highlights: ExtendedHighlight[] // 高亮数组
  setHighlights: (highlights: ExtendedHighlight[]) => void;
  addHighlight: (highlight: ExtendedHighlight) => void // 添加高亮函数
  removeHighlight: (id: string) => void // 移除高亮函数
  updateHighlight: (highlight: ExtendedHighlight) => void // 更新高亮函数
  updateHighlightId: (oldId: string, newId: string) => void // 更新高亮 ID 函数
  clearHighlights: () => void;  // 添加清理方法的类型定义
}

// 使用 zustand 创建高亮存储
export const useHighlighterStore = create<HighlighterStore>((set) => ({
  highlights: [], // 初始化高亮数组为空
  setHighlights: (highlights) => set({ highlights }),
  // 添加新的高亮到高亮数组
  addHighlight: (highlight) => set((state) => ({
    highlights: [...state.highlights, highlight]
  })),

  // 根据 ID 从高亮数组中移除指定的高亮
  removeHighlight: (id) => set((state) => ({
    highlights: state.highlights.filter((attach_highlight) => {
      return attach_highlight.id !== id
    }) // 过滤掉 ID 匹配的高亮
  })),

  // 更新现有的高亮信息
  updateHighlight: (newHighlight) => {
    return set((state) => ({
      highlights: state.highlights.map((highlight) => {
        if (highlight.id === newHighlight.id) {
          return newHighlight;
        }
        return highlight;
      })
    }))
  },
  updateHighlightId: (oldId, newId) => {
    return set((state) => ({
      highlights: state.highlights.map((highlight) => {
        if (highlight.id === oldId) {
          return { ...highlight, id: newId };
        }
        return highlight;
      })
    }))
  },
  clearHighlights: () => set({ highlights: [] }),  // 添加清理方法的实现
}))


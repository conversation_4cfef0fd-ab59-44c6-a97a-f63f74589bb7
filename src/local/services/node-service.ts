import { getDatabase } from '../db';
import { EdgeDocType, generateId, NodeDocType } from '@/local';
import { DB_DEFAULTS, calculateNodeHeight } from '@/components/flow/constants/node-defaults';
import { CMapCompressionType } from 'pdfjs-dist';

/**
 * 边类型常量
 * 参考后端实现，支持"default", "straight", "step", "smoothstep", "simplebezier"
 */
const EDGE_TYPES = ["default", "straight", "step", "smoothstep", "simplebezier"];

// 定义事件总线，用于通知节点变更
export const NodeEvents = {
    // 事件监听器集合
    listeners: new Map<string, Function[]>(),

    // 添加事件监听器
    on(event: string, callback: Function) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)?.push(callback);
    },

    // 移除事件监听器
    off(event: string, callback: Function) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event) || [];
        this.listeners.set(event, callbacks.filter(cb => cb !== callback));
    },

    // 触发事件
    emit(event: string, ...args: any[]) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event) || [];
        callbacks.forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`Error in ${event} event handler:`, error);
            }
        });
    }
};

export interface CreateReq {
    wid: string;
    content: string;
    aid: string;
    color: string;
    x: number;
    y: number;
    mark: string;
    pids: string[];
    type: string;
    mid: string;
    style?: Record<string, any>;
}

export interface CreateResp {
    nid: string;
    mid: string;
    edges: any []; // todo 需要把edge的数据结构定义出来
}

export interface UpdateReq {
    nid: string;
    title: string;
    content: string;
    color: string;
    x: number;
    y: number;
    width?: number;
    height?: number;
    type: number;
}

export interface UpdatePositionReq {
    positions: Array<{
        nid: string;
        x: number;
        y: number;
    }>;
}

export interface UpdateSizeReq {
    nid: string;
    width: number;
    height: number;
}

export interface DeleteReq {
    nids: string[];
}

export interface LinkReq {
    nid: string;
    pids: string[];
    type: string;
    label?: string;
}

export interface DeleteLinkReq {
    wid: string;
    eid: string;
}

export interface ListReq {
    wid: string;
}

export interface ListResp {
    nodes: Array<{
        nid: string;
        title: string;
        content: string;
        color: string;
        x: number;
        y: number;
        width?: number;
        height?: number;
    }>;
    edges: Array<{
        eid: string;
        nid: string;
        pid: string;
        type: string;
    }>;
}

export class NodeService {
    /**
     * 创建节点
     */
    async create(req: CreateReq): Promise<CreateResp> {
        const db = await getDatabase();
        const nid = generateId();
        let mid = req.mid || generateId();

        try {
            console.log('创建节点请求参数:', JSON.stringify(req));

            // 验证工作区是否存在
            const workspace = await db.workspaces.findOne({
                selector: { id: req.wid }
            }).exec();

            if (!workspace) {
                console.error('工作区不存在:', req.wid);
                throw new Error('工作区不存在');
            }

            // 验证附件
            if (req.aid && req.aid !== '0') {
                const attachment = await db.attachments.findOne({
                    selector: { id: req.aid }
                }).exec();

                if (!attachment) {
                    console.error('附件不存在:', req.aid);
                    throw new Error('附件不存在');
                }
                console.log('附件验证通过:', req.aid);
            }

            // 处理父节点和边类型
            let validPids: string[] = [];

            if (req.pids && req.pids.length > 0) {
                // 过滤掉值为0的父节点ID
                validPids = req.pids.filter(pid => pid !== '0');
                console.log('有效父节点IDs:', validPids);

                // 验证所有父节点是否存在
                for (const pid of validPids) {
                    const parentNode = await db.nodes.findOne({
                        selector: { id: pid }
                    }).exec();

                    if (!parentNode) {
                        console.error('父节点不存在:', pid);
                        throw new Error('父节点不存在');
                    }
                }

                // 验证边类型是否有效
                // edgeType = EDGE_TYPES.indexOf(req.type);
                if (!EDGE_TYPES.includes(req.type)) {
                    console.error('无效的边类型:', req.type);
                    throw new Error('无效的边类型');
                }
            }

            // 创建节点
            console.log('开始创建节点');

            // 计算节点高度 - 如果有mark（PDF划取），则根据内容自动计算高度
            let nodeHeight: number = DB_DEFAULTS.HEIGHT;
            if (req.mark && req.content) {
                nodeHeight = calculateNodeHeight(req.content);
                console.log('根据内容计算节点高度:', nodeHeight, '内容长度:', req.content.length);
            }

            const nodeData = {
                id: nid,
                wid: req.wid,
                title: '',
                content: req.content || '',
                color: req.color || 'FFE28F',
                position_x: req.x || 0,
                position_y: req.y || 0,
                width: DB_DEFAULTS.WIDTH, // 默认宽度
                height: nodeHeight, // 动态计算的高度
            };

            await db.nodes.insert(nodeData);
            console.log('节点创建成功:', nid, '节点数据:', nodeData);

            // 如果有标记内容，创建标记
            if (req.mark && req.aid && req.aid !== '0') {
                console.log('开始创建标记, 标记内容:', req.mark);

                // 解析mark字符串，提取type和content
                let markObj;
                try {
                    markObj = JSON.parse(req.mark);
                    console.log('解析mark成功:', markObj);
                } catch (e) {
                    throw new Error('高亮数据错误')
                }

                const now = Math.floor(Date.now() / 1000);
                const markData: any = {
                    id: mid,
                    attach_id: req.aid,
                    node_id: nid,
                    wid: req.wid,
                    color: req.color || 'FFE28F',
                    create_at: now,
                    update_at: now
                };

                // 从mark中提取type和content（如果存在）
                if (typeof markObj === 'object') {
                    if (markObj.type) {
                        markData.mark_type = markObj.type;
                    }

                    if (markObj.content && markObj.content.text) {
                        markData.content = markObj.content.text;
                    }

                    if (markObj.content && markObj.content.image) {
                        markData.content = markObj.content.image;
                    }
                }
                // 删除markObj中的type和content
                delete markObj.type;
                delete markObj.content;
                markData.mark = JSON.stringify(markObj);

                try {
                    await db.marks.insert(markData);
                    console.log('标记创建成功:', mid, '标记数据:', markData);
                } catch (markError) {
                    console.error('标记创建失败:', markError);
                    // 即使标记创建失败，也继续创建节点
                }
            } else {
                console.log('不创建标记, mark:', req.mark, 'aid:', req.aid);
            }

            const newEdges = []
            // 如果有父节点，创建边
            if (validPids.length > 0) {
                console.log('开始创建边');
                for (const pid of validPids) {
                    const edgeId = generateId();
                    const edgeData = {
                        id: edgeId,
                        wid: req.wid,
                        nid: nid,
                        pid: pid,
                        type: req.type,
                        style: {
                            strokeDasharray: false, // 空为实线，8为虚线
                            stroke: '#6b6f76', // 默认颜色
                            ...req?.style
                        }
                    };

                    // 20257-24 这里的逻辑，创建节点时，需要创建边
                    // 旧逻辑: 是再node-add.ts中节点创建成功后，生成一个临时的边，但是实际再逻辑中已经生成好了 edge
                    // 新逻辑: 再生成后的节点中，直接返回创建好的边数据
                    const edge = edgeData
                    const eachEdge = {
                        source: edge.pid,
                        target: edge.nid,
                        type: 'editableEdge',
                        id: edge.id,
                        style: edge.style || {}
                    }

                    newEdges.push(eachEdge)

                    try {
                        const edgeRes = await db.edges.insert(edgeData);
                        console.log('边创建成功:', edgeId, '边数据:', edgeData, edgeRes);
                    } catch (edgeError) {
                        console.error('边创建失败:', edgeError);
                        // 继续创建其他边
                    }
                }
            }


            console.log('节点创建完成, 返回:', { nid, mid, edges: newEdges});
            return { nid, mid , edges: newEdges};
        } catch (error) {
            console.error('创建节点失败:', error);
            throw error;
        }
    }

    /**
     * 更新节点
     */
    async update(req: UpdateReq): Promise<void> {
        const db = await getDatabase();
        const node = await db.nodes.findOne({
            selector: { id: req.nid }
        }).exec();

        if (!node) {
            throw new Error('节点不存在');
        }
        let res = {}
        switch (req.type) {
            case 1:
                res = { title: req.title }
                break
            case 2:
                res = { content: req.content }
                break
            case 3:
                res = { color: req.color }
                break
            case 4:
                res = { title: req.title, content: req.content }
                break
            case 5:
                res = { position_x: req.x, position_y: req.y }
                break
            case 6:
                res = { width: req.width, height: req.height }
                break
            default:
                throw new Error("参数错误")
        }
        await node.update({
            $set: res
        });
        if (req.type === 3) {
            db.marks.findOne({
                selector: { node_id: req.nid }
            }).update({
                $set: { color: req.color }
            })
        }
    }

    /**
     * 更新节点位置
     */
    async updatePosition(req: UpdatePositionReq): Promise<void> {
        const db = await getDatabase();

        for (const position of req.positions) {
            try {
                // 简单直接的实现，不使用重试机制
                const node = await db.nodes.findOne({
                    selector: { id: position.nid }
                }).exec();

                if (node) {
                    await node.incrementalUpdate({
                        $set: {
                            position_x: position.x,
                            position_y: position.y
                        }
                    });
                }
            } catch (error) {
                console.error(`节点位置更新失败，节点ID: ${position.nid}`, error);
                throw new Error("节点位置更新失败")
            }
        }
    }

    /**
     * 更新节点尺寸
     */
    async updateSize(req: UpdateSizeReq): Promise<void> {
        const db = await getDatabase();

        try {
            console.log('开始更新节点尺寸:', req);

            const node = await db.nodes.findOne({
                selector: { id: req.nid }
            }).exec();

            if (node) {
                console.log('找到节点，当前尺寸:', { width: node.width, height: node.height });

                const updateResult = await node.incrementalUpdate({
                    $set: {
                        width: req.width,
                        height: req.height
                    }
                });

                console.log('节点尺寸更新成功:', updateResult);

                // 验证更新结果
                const updatedNode = await db.nodes.findOne({
                    selector: { id: req.nid }
                }).exec();

                if (updatedNode) {
                    console.log('更新后的节点尺寸:', { width: updatedNode.width, height: updatedNode.height });
                }
            } else {
                console.error('节点不存在:', req.nid);
                throw new Error('节点不存在');
            }
        } catch (error) {
            console.error(`节点尺寸更新失败，节点ID: ${req.nid}`, error);
            throw new Error("节点尺寸更新失败");
        }
    }

    /**
     * 删除节点
     */
    async delete(req: DeleteReq): Promise<void> {
        const db = await getDatabase();

        for (const nid of req.nids) {
            // 删除节点
            await db.nodes.find({
                selector: { id: nid }
            }).remove();

            // 删除相关的边
            await db.edges.find({
                selector: {
                    $or: [
                        { nid },
                        { pid: nid }
                    ]
                }
            }).remove();

            // 删除相关的标记
            await db.marks.find({
                selector: { node_id: nid }
            }).remove();
        }
    }

    /**
     * 连接节点
     */
    async link(req: LinkReq): Promise<void | string[]> {
        const db = await getDatabase();
        // 判断节点是否存在
        const node = await db.nodes.findOne({
            selector: { id: req.nid }
        }).exec();
        if (!node) {
            throw new Error('节点不存在');
        }
        // 删除所有边
        await db.edges.find({
            selector: {
                nid: req.nid
            }
        }).remove();
        const successResult = []
        // 新增连接
        for (const pid of req.pids) {
            const edgeId = generateId();
            const edgeData = {
                id: edgeId,
                wid: node.wid,
                nid: req.nid,
                pid: pid,
                type: req.type,
                label: req.label || ''
            };

            try {
                const res = await db.edges.insert(edgeData);
                successResult.push(edgeId)
            } catch (edgeError) {
                throw new Error("关联失败")
            }
        }

        return successResult
    }

    /**
     * 删除连接
     */
    async deleteLink(req: DeleteLinkReq): Promise<void> {
        const db = await getDatabase();
        if (req.eid.startsWith("tmp-")) {
            const data = req.eid.split("-")
            await db.edges.find({
                selector: { nid: data[1], pid: data[2] }
            }).remove();
        } else {
            await db.edges.find({
                selector: { id: req.eid }
            }).remove();
        }

    }

    /**
     * 获取节点列表
     */
    async list(req: ListReq): Promise<ListResp> {
        const db = await getDatabase();
        console.log('获取节点列表, 工作区ID:', req.wid);

        try {
            // 查询节点
            const nodes = await db.nodes.find({
                selector: { wid: req.wid }
            }).exec();
            console.log(`找到${nodes.length}个节点`);

            // 查询边
            const edges = await db.edges.find({
                selector: { wid: req.wid }
            }).exec();
            console.log(`找到${edges.length}条边`);

            const result = {
                nodes: nodes.map((node: NodeDocType) => {
                    return {
                        nid: node.id,
                        title: node.title,
                        content: node.content,
                        color: node.color,
                        x: node.position_x,
                        y: node.position_y,
                        width: node.width || DB_DEFAULTS.WIDTH,
                        height: node.height || DB_DEFAULTS.HEIGHT,
                    };
                }),
                edges: edges.map((edge: EdgeDocType) => ({
                    eid: edge.id,
                    nid: edge.nid,
                    pid: edge.pid,
                    type: edge.type,
                    label: edge.label || '',
                    style: edge.style || {}
                }))
            };
            console.log('返回节点列表结果, 节点数量:', result.nodes.length, '边数量:', result.edges.length);
            return result;
        } catch (error) {
            console.error('获取节点列表失败:', error);
            throw error;
        }
    }

    /**
     * 更新边标签
     */
    async updateEdgeLabel(req: { eid: string; label: string }): Promise<{ eid: string } | undefined> {

        const db = await getDatabase();

        // 查找边
        let edge = await db.edges.findOne({
            selector: { id: req.eid }
        }).exec();

        if (!edge) return

        // 边存在，直接更新标签
        await edge.update({
            $set: { label: req.label }
        });
        console.log('边标签更新成功:', req.eid, '新标签:', req.label);
        return { eid: req.eid };
    }

    async updateEdgeStyle(req: { eid: string; style: Record<string, any> }): Promise<{ eid: string } | undefined> {
        const db = await getDatabase();

        // 查找边
        let edge = await db.edges.findOne({
            selector: { id: req.eid }
        }).exec();

        if (!edge) return

        // 获取现有的 style 并与新的 style 合并
        const existingStyle = edge.style || {};
        const mergedStyle = { ...existingStyle, ...req.style };

        console.log('更新边样式:', req.eid, '新样式:', mergedStyle);
        await db.edges.findOne({
            selector: { id: req.eid }
        }).update({
            $set: { style: mergedStyle }
        });
        console.log('边样式更新成功:', req.eid, '新样式:', mergedStyle);
        return { eid: req.eid };
    }

}
import { useCallback, useState, useRef } from 'react';
import { useReactFlow } from '@xyflow/react';

export const useMousePosition = () => {
  const { screenToFlowPosition } = useReactFlow();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const flowWrapperRef = useRef<HTMLDivElement>(null);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!flowWrapperRef.current) return;

    const bounds = flowWrapperRef.current.getBoundingClientRect();
    const position = screenToFlowPosition({
      x: event.clientX - bounds.left,
      y: event.clientY - bounds.top,
    });
    
    setMousePosition(position);
  }, [screenToFlowPosition]);

  // 处理面板双击事件
  const handlePaneDoubleClick = useCallback((event: React.MouseEvent) => {
    if (!flowWrapperRef.current) return;

    const bounds = flowWrapperRef.current.getBoundingClientRect();
    const position = screenToFlowPosition({
      x: event.clientX - bounds.left,
      y: event.clientY - bounds.top,
    });
    
    setMousePosition(position);
    return position;
  }, [screenToFlowPosition]);

  return {
    mousePosition,
    flowWrapperRef,
    handleMouseMove,
    handlePaneDoubleClick,
  };
}; 
import React, { useRef, useEffect, useState } from "react";
import { Edit, Trash2, Plus, GripVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  draggable,
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { TagItem } from "./TagItem";
import { GroupDropZoneIndicator, GroupSortIndicator } from "./drop-indicators";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { useDragAndDrop } from "./hooks/useDragAndDrop";

export interface Tag {
  id: string;
  name: string;
}

export interface TagGroupProps {
  id: string;
  title: string;
  tags: Tag[];
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onAddTag?: (groupId: string) => void;
  onEditTag?: (groupId: string, tagId: string) => void;
  onDeleteTag?: (groupId: string, tagId: string) => void;
}

export const TagGroup: React.FC<TagGroupProps> = ({
  id,
  title,
  tags,
  onEdit,
  onDelete,
  onAddTag,
  onEditTag,
  onDeleteTag,
}) => {
  const groupRef = useRef<HTMLDivElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 使用拖拽 hook
  const { isBeingDragged, isDropTarget, getDragStyles } = useDragAndDrop();
  
  // 检查当前分组是否正在被拖拽
  const isGroupDragging = isBeingDragged(id, 'group');
  
  // 检查当前分组是否是放置目标
  const isGroupDropTarget = isDropTarget(id, 'group');

  // 设置分组拖拽功能 - 只设置拖拽元素，不处理事件
  useEffect(() => {
    const element = groupRef.current;
    if (!element) return;
    
    return combine(
      draggable({
        element,
        getInitialData: () => ({
          id,
          type: "group",
          groupId: id,
        }),
      }),
      dropTargetForElements({
        element,
        getData: () => ({
          id,
          type: "group",
          groupId: id,
        }),
        canDrop: ({ source }) => {
          // 允许分组拖拽到分组（重排序）
          if (source.data.type === "group") {
            return source.data.id !== id; // 不能拖拽到自己
          }
          // 允许标签拖拽到分组（跨分组移动）
          if (source.data.type === "tag") {
            return source.data.groupId !== id; // 不能拖拽到当前分组
          }
          return false;
        },
        getIsSticky() {
          return true;
        },
      })
    );
  }, [id]);

  // 设置拖放区域（分组内容区域）用于接收标签 - 只设置放置目标，不处理事件
  useEffect(() => {
    const element = dropZoneRef.current;
    if (!element) return;
    
    return dropTargetForElements({
      element,
      getData: () => ({
        id,
        type: "group",
        groupId: id,
        isDropZone: true, // 标记为拖放区域
      }),
      canDrop: ({ source }) => {
        // 只允许标签拖拽到拖放区域
        return source.data.type === "tag" && source.data.groupId !== id;
      },
    });
  }, [id]);

  // 合并拖拽样式
  const dragStyles = getDragStyles(id, 'group');

  return (
    <div 
      ref={groupRef} 
      className={`relative ${dragStyles}`}
      data-group-id={id} // 添加用于触发后效果的标识
    >
      {/* 分组排序指示器 */}
      <GroupSortIndicator
        isActive={isGroupDropTarget}
        position="above"
        groupTitle={title}
      />
      <Card
        className={`
        transition-all duration-300 relative
        ${
          isGroupDragging
            ? "border-2 border-dashed border-blue-400 shadow-lg"
            : isGroupDropTarget
            ? "border-2 border-solid border-green-400 shadow-md bg-green-50/20 scale-102"
            : "border border-gray-200 hover:border-gray-300 hover:shadow-sm"
        }
      `}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h3 className="text-lg font-semibold">{title}</h3>
              {tags.length > 0 && (
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {tags.length} 个标签
                </span>
              )}
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAddTag?.(id)}
                className="h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit?.(id)}
                className="h-8 w-8 p-0"
              >
                <Edit className="h-4 w-4" />
              </Button>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认删除分组</AlertDialogTitle>
                    <AlertDialogDescription className="space-y-2">
                      <div>确定要删除分组 "{title}" 吗？</div>
                      {tags.length > 0 && (
                        <div className="text-red-600">
                          此操作将同时删除组内的 {tags.length} 个标签
                        </div>
                      )}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => onDelete?.(id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      确认删除
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <div className="flex items-center cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100">
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div
            ref={dropZoneRef}
            className={`
              min-h-20 rounded-md transition-all duration-200 relative
              ${
                isGroupDropTarget
                  ? "bg-green-50/40 border-2 border-dashed border-green-300"
                  : "border-2 border-transparent"
              }
            `}
          >
            {/* 拖放区域指示器 */}
            <GroupDropZoneIndicator
              isActive={isGroupDropTarget}
              isEmpty={tags.length === 0}
              groupTitle={title}
            />
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <TagItem
                  key={tag.id}
                  id={tag.id}
                  name={tag.name}
                  groupId={id} // 传递 groupId 给 TagItem
                  onEdit={(tagId) => onEditTag?.(id, tagId)}
                  onDelete={(tagId) => onDeleteTag?.(id, tagId)}
                />
              ))}
              {tags.length === 0 && (
                <div
                  className={`
                  w-full text-center py-5 text-sm rounded-md transition-all relative z-10
                  ${
                    isGroupDropTarget
                      ? "text-green-600 font-medium opacity-0"
                      : "text-gray-500 italic"
                  }
                `}
                >
                  暂无标签，点击 + 按钮添加标签
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 分组排序指示器 - 下方 */}
      <GroupSortIndicator
        isActive={isGroupDropTarget}
        position="below"
        groupTitle={title}
      />
    </div>
  );
};

export default TagGroup;

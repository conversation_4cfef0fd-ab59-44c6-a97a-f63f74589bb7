import styled from "styled-components";
import { FC, useEffect, useRef, useState } from "react";
import { Input, message, Upload } from "antd";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatResponseRefs, NodeRef, PdfRef } from "../types";
import { useFlowStore } from "@/store/flow-store";
import { messageError } from "@/components/message/hooks";


// 引用面板的主容器样式
const ReferenceContainer = styled.div`
  width: 320px;
  height: 500px;
  background: #e0e2f4;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
`;

// 引用类别标题样式
const ReferenceHeader = styled.div`
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 10px;
`;

// 引用列表容器样式
const ReferenceList = styled.div`
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

// 单个引用项目样式，包含悬停效果
const ReferenceItem = styled.div`
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: #d4d8ec;
  }
`;

/**
 * 高亮显示搜索文本的函数
 * @param text 原始文本
 * @param search 搜索关键词
 * @returns 包含高亮标记的文本元素
 */
const highlightText = (text: string, search: string) => {
  if (!search) return text;
  // 创建正则表达式匹配搜索词（忽略大小写）
  const regex = new RegExp(`(${search})`, "gi");
  // 将文本按匹配项分割，并对匹配部分应用高亮样式
  return text.split(regex).map((part, i) =>
    part.toLowerCase() === search.toLowerCase() ? (
      <span key={i} style={{ backgroundColor: "#ffeb3b" }}>
        {part}
      </span>
    ) : (
      part
    )
  );
};

type ReferenceProps = {
  handleClick?: (source: "pdf" | "node", ref: PdfRef | NodeRef) => void
}

/**
 * 引用管理组件
 * 用于搜索、显示和选择引用资源（PDF文件和节点）
 */
export const Reference: FC<ReferenceProps> = ({ handleClick }) => {
  // 从全局状态获取引用相关数据和方法
  const {
    currentChatRid,
    pdfRefs,
    // nodeRefs,
    footerRefs,
    addFooterRef,
    currentChat,
    updateCurrentChat,
    updateChatList,
    setPdfRefs,
  } = useChatStore();

  const nodes = useFlowStore.getState().nodes;
  // 新增：将 flow-store 的 nodes 映射为 NodeRef 结构
  const flowNodesAsNodeRefs: NodeRef[] = nodes.map((node) => ({
    id: node.id,
    title: node.data?.title ?? "",
    content: node.data?.content ?? "",
  }));
  // 搜索输入框引用
  const searchRef = useRef(null);
  // 搜索关键词状态
  const [searchValue, setSearchValue] = useState("");
  // PDF搜索结果状态
  const [pdfSearchResults, setPdfSearchResults] = useState<PdfRef[]>([]);
  // 节点搜索结果状态
  const [nodeSearchResults, setNodeSearchResults] = useState<NodeRef[]>([]);

  // 组件挂载后自动聚焦搜索输入框
  useEffect(() => {
    // @ts-ignore
    searchRef.current.focus();
  }, [searchRef]);

  /**
   * 处理搜索输入变化
   * @param e 输入事件对象
   */
  const onHandleSearch = (e: any) => {
    const searchText = e.target.value;
    setSearchValue(searchText);

    if (searchText !== "") {
      // 搜索PDF文件，忽略大小写
      const pdfResults = pdfRefs.filter((pdf) =>
        pdf.fileName.toLowerCase().includes(searchText.toLowerCase())
      );
      // 搜索节点，忽略大小写
      // TODO 0708,这里以前用的逻辑是nodeRefs, 但是nodeRefs是空的, 所以改成flowNodesAsNodeRefs
      const nodeResults = flowNodesAsNodeRefs.filter((node) =>
        node.content.toLowerCase().includes(searchText.toLowerCase()) || node.title?.toLowerCase().includes(searchText.toLowerCase())
      );

      // 更新搜索结果状态
      setPdfSearchResults(pdfResults);
      setNodeSearchResults(nodeResults);
    } else {
      // 清空搜索结果
      setPdfSearchResults([]);
      setNodeSearchResults([]);
    }
  };

  /**
   * 处理引用项点击事件
   * @param source 引用来源类型（"pdf"或"node"）
   * @param ref 引用对象
   */
  const onHandleClick = (source: "pdf" | "node", ref: PdfRef | NodeRef) => {
    handleClick?.(source, ref)
    if (currentChatRid === -1) {
      if (footerRefs.length === 10) {
        message.error("最多只能添加10个页脚引用");
        return;
      }
      // 处理页脚引用（新聊天模式）
      if (source === "pdf") {
        // 检查PDF引用是否已存在
        const item = footerRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加PDF引用到页脚引用
          addFooterRef({
            id: ref.id as string,
            title: (ref as PdfRef).fileName,
            url: (ref as PdfRef).url,
            type: 1, // PDF类型
          });
        } else {
          messageError("重复引用!")
        }
      } else {
        // 检查节点引用是否已存在
        const item = footerRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加节点引用到页脚引用
          addFooterRef({
            id: ref.id as string,
            title: (ref as NodeRef).title || "未命名.node",
            content: (ref as NodeRef).content,
            type: 2, // 节点类型
          });
        } else {
          messageError("重复引用!")
        }
      }
    } else {
      // 处理当前聊天引用（编辑现有聊天模式）
      const tempRefs: ChatResponseRefs = currentChat?.refs || [];
      if (tempRefs.length === 10) {
        message.error("最多只能添加10个引用");
        return;
      }
      if (source === "pdf") {
        // 检查PDF引用是否已存在于当前聊天引用中
        const item = tempRefs.find((item) => item.id === (ref as PdfRef).id);
        if (!item) {
          // 添加PDF引用到当前聊天引用
          tempRefs.push({
            type: 1, // PDF类型
            id: (ref as PdfRef).id as string,
            title: (ref as PdfRef).fileName,
            url: (ref as PdfRef).url,
            content: "",
          });
          // 更新当前聊天和会话详情
          if (currentChat) {
            currentChat.refs = tempRefs;
            updateCurrentChat(currentChat);
            updateChatList(currentChat);
          }
        } else {
          messageError("重复引用!")
        }
      } else {
        // 检查节点引用是否已存在于当前聊天引用中
        const item = tempRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加节点引用到当前聊天引用
          tempRefs.push({
            type: 2, // 节点类型
            id: (ref as NodeRef).id as string,
            title: (ref as NodeRef).title || "未命名.node",
            content: (ref as NodeRef).content,
          } as any);
          // 更新当前聊天和会话详情
          if (currentChat) {
            currentChat.refs = tempRefs;
            updateCurrentChat(currentChat);
            updateChatList(currentChat);
          }
        } else {
          messageError("重复引用!")
        }
      }
    }
  };

  // 上传文件
  const customRequest = (options: any) => {
    const file = options.file;
    const URL = window.URL || window.webkitURL;
    const attachUrl = URL.createObjectURL(file);
    const ref = {
      id: crypto.randomUUID(),
      fileName: file.name,
      url: attachUrl,
      type: 1
    };
    onHandleClick("pdf", ref);
    // 新增：将上传的PDF添加到pdfRefs
    setPdfRefs([...pdfRefs, ref]);
  };

  return (
    <ReferenceContainer>
      {/* 搜索输入框区域 */}
      <div className="p-2.5 border-1 border-b border-blue-500">
        <Input
          ref={searchRef}
          value={searchValue}
          onChange={onHandleSearch}
          placeholder="关键词搜索"
          suffix={<SearchOutlined style={{ fontSize: "20px", opacity: 0.3 }} />}
        />
      </div>

      {/* 引用列表区域，带滚动条 */}
      <div className="scrollbar-custom pt-2.5 px-5 pb-12">
        {/* PDF引用部分 */}
        {searchValue !== "" ? (
          // 有搜索关键词时显示PDF搜索结果
          pdfSearchResults.length > 0 && (
            <ReferenceList>
              {pdfSearchResults.map((file) => (
                <ReferenceItem
                  key={file.id + `${crypto.randomUUID()}`}
                  onClick={() => {
                    onHandleClick("pdf", file);
                  }}
                >
                  <div className="w-[80%] text-md truncated">
                    {/* 高亮显示匹配的文本 */}
                    {highlightText(file.fileName, searchValue)}.pdf
                  </div>
                </ReferenceItem>
              ))}
            </ReferenceList>
          )
        ) : (
          // 无搜索关键词时显示所有PDF引用
          <div className="text-[#3D56BA]">
            {pdfRefs.length > 0 && <ReferenceHeader>File</ReferenceHeader>}
            <ReferenceList>
              {pdfRefs.map((file) => (
                <ReferenceItem
                  key={file.id + `${crypto.randomUUID()}`}
                  onClick={() => {
                    onHandleClick("pdf", file);
                  }}
                >
                  <div className=" text-sm truncate">
                    {file.fileName}
                    {/* .pdf */}
                  </div>
                </ReferenceItem>
              ))}
            </ReferenceList>
          </div>
        )}

        {/* 节点引用部分 */}
        <div className="text-[#3D56BA]">
          {searchValue !== "" ? (
            // 有搜索关键词时显示节点搜索结果
            nodeSearchResults.length > 0 && (
              <ReferenceList>
                {nodeSearchResults.map((node, index) => {
                  return (
                    <ReferenceItem
                      key={index + `${crypto.randomUUID()}`}
                      onClick={() => {
                        onHandleClick("node", node);
                      }}
                    >
                      {/* 显示节点标题，高亮匹配文本 */}
                      <div className="text-[18px]"> 
                        {/* {highlightText(node.title ?? "", searchValue)} */}
                        {highlightText(node.title || "未命名.node", searchValue)}
                      </div>
                      {/* 显示节点内容，高亮匹配文本，最多显示4行 */}
                      <div className="text-black text-[14px] line-clamp-4">
                        {highlightText(node.content ?? "", searchValue)}
                        {/* {index + 1} */}
                      </div>
                    </ReferenceItem>
                  );
                })}
              </ReferenceList>
            )
          ) : (
            // 无搜索关键词时显示 flow-store 的 nodes
            <>
              <ReferenceList>
                {flowNodesAsNodeRefs.length > 0 && <ReferenceHeader>Node</ReferenceHeader>}
                {flowNodesAsNodeRefs.map((node, index) => {
                  return (
                    <ReferenceItem
                      key={index + `${crypto.randomUUID()}`}
                      onClick={() => {
                        onHandleClick("node", node);
                      }}
                    >
                      {/* 只显示节点标题，没有 title 时显示 '未命名.node' */}
                      <div className="text-[18px]">
                        {node.title && node.title.trim() !== "" ? node.title : "未命名.node"}
                      </div>
                      <div className="text-black text-[14px] line-clamp-4">
                        {node.content}
                      </div>
                    </ReferenceItem>
                  );
                })}
              </ReferenceList>
            </>
          )}
        </div>
      </div>

      {/*底部上传按钮*/}
      <Upload
        name="avatar"
        listType="text"
        showUploadList={false}
        accept=".pdf"
        multiple={true}
        customRequest={customRequest}
      >
        <div
          className="w-full  h-10 absolute bottom-0 left-0 text-[#3D56BA] text-[18px] font-bold  bg-[#E0E2F4] flex justify-center items-center gap-2.5"
          style={{
            boxShadow: "0px -1px 0px rgba(0, 0, 0, 0.1)",
            cursor: "pointer",
          }}
        >
          <PlusOutlined
            style={{
              fontSize: "20px",
              fontWeight: 700,
            }}
          />
          上传本地文件
        </div>
      </Upload>
    </ReferenceContainer>
  );
};

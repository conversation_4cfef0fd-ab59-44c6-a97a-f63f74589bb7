import {Button, Space, Tooltip} from "antd";
import {DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {SketchPicker} from "react-color";
import {CustomHighlight} from "@/store/pdf-store.ts";
import {IconFont} from "@/components/IconFont";
import {useState} from "react";
import {useHighlightChat} from "@/components/pdf/hooks/highlight-chat.ts";
import {useHighlightNote} from "@/components/pdf/hooks/highlight-note.ts";
import {useNodeColor} from "@/components/flow/hooks/node-color.ts";
import {useHighlightAdd} from "@/components/pdf/hooks/highlight-add.tsx";
import {useNodeDelete} from "@/components/flow/hooks/node-delete.ts";
import {useEditNodeStore} from "@/store/workerspace-store/edit-node-store";

export const HighlightToolbar = ({customHighlight}: { customHighlight: CustomHighlight }) => {
    // 颜色列表
    const colorList = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF"]
    // 是否真实高亮
    const isRealHighlight = customHighlight.nid && customHighlight.nid.length > 0
    // 是否显示颜色列表
    const [showColorList, setShowColorList] = useState(false)
    // 是否显示颜色选择器面板
    const [showColorPicker, setShowColorPicker] = useState(false)
    // 编辑节点弹窗状态
    const { openEditNode } = useEditNodeStore()
    // AI聊天
    const {handleChat} = useHighlightChat()
    // 笔记本
    const {handleCopyToNotebook} = useHighlightNote()
    // 节点颜色
    const {handleColorChange} = useNodeColor()
    // 新增节点
    const {addHighlight} = useHighlightAdd()
    // 删除节点
    const {handleDeleteNode} = useNodeDelete()
    return (
        <div className="bg-black rounded-md">
            <div className="w-full relative ColorSelectToolbar">
                <div
                    className="w-full inline-flex  justify-between  items-center gap-2.5  p-2 rounded-md  text-white cursor-pointer bg-transparent">
                    <Space>
                        {/*生成摘要*/}
                        {/*<Tooltip title="生成摘要">*/}
                        {/*    <Button type="primary" shape="circle" size="small" style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<SendOutlined/>} onClick={()=>{}} loading={false}/>*/}
                        {/*</Tooltip>*/}
                        {/* 聊天按钮 */}
                        <Tooltip title="AI聊天">
                            <Button type="primary" shape="circle" size="small"
                                    style={{backgroundColor: "rgba(0,0,0,.5)"}}
                                    icon={<IconFont type="icon-chat" style={{color: "#fff"}}/>}
                                    onClick={() => handleChat(customHighlight)}/>
                        </Tooltip>
                        <Tooltip title="复制到笔记本">
                            <Button type="primary" shape="circle" size="small"
                                    style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<IconFont type="icon-note"/>}
                                    onClick={() => handleCopyToNotebook(customHighlight)}/>
                        </Tooltip>
                        {isRealHighlight && (
                            <Tooltip title="编辑节点">
                                <Button type="primary" shape="circle" size="small" icon={<EditOutlined/>}
                                        style={{backgroundColor: "rgba(0,0,0,.5)"}}
                                        onClick={() => openEditNode(customHighlight)}/>
                            </Tooltip>
                        )}
                        <Space
                            className="relative overflow-hidden "
                            onMouseEnter={() => setShowColorList(true)}
                            onMouseLeave={() => setShowColorList(false)}
                        >
                            {!isRealHighlight && (
                                <Tooltip title="新增节点">
                                    <Button type="primary" shape="circle" size="small" icon={<PlusOutlined/>}
                                            style={{backgroundColor: "rgba(0,0,0,.5)"}}/>
                                </Tooltip>
                            )}
                            <div className="flex justify-around items-center">
                                {(showColorList || isRealHighlight) && (
                                    <Space
                                        style={isRealHighlight ? {} : {animation: `${showColorList ? "slideOut" : "slideIn"} 0.3s ease-in-out forwards`}}>
                                        {colorList.map((color, index) => (
                                            <div
                                                key={index}
                                                className="w-5 h-5 rounded-full border border-white cursor-pointer"
                                                style={{backgroundColor: color}}
                                                onClick={(_) => {
                                                    if (isRealHighlight) {
                                                        handleColorChange(customHighlight.nid, color)
                                                    } else {
                                                        addHighlight({...customHighlight, color})
                                                    }
                                                }}
                                            />
                                        ))}
                                        <Tooltip title="选择颜色">
                                            <div className="w-6 h-6 rounded border border-white cursor-pointer"
                                                 style={{backgroundColor: customHighlight.color}}
                                                 onClick={() => setShowColorPicker(!showColorPicker)}
                                            />
                                        </Tooltip>
                                    </Space>
                                )}
                            </div>
                        </Space>
                        {/* 删除按钮 */}
                        {isRealHighlight && (
                            <Tooltip title="删除节点">
                                <Button type="primary" shape="circle" size="small"
                                        style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<DeleteOutlined/>}
                                        onClick={() => handleDeleteNode([customHighlight.nid])}/>
                            </Tooltip>
                        )}
                    </Space>
                </div>
                {/* 颜色选择器面板 */}
                {showColorPicker && (
                    <div className="absolute z-100 top-full mt-2">
                        <SketchPicker color={customHighlight.color} onChange={(color) => {
                            if (isRealHighlight) {
                                handleColorChange(customHighlight.nid, color.hex)
                            } else {
                                addHighlight({...customHighlight, color: color.hex})
                            }
                        }}/>
                    </div>
                )}


            </div>
        </div>
    )
}
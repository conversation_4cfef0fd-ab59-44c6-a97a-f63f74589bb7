@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 文字颜色 */
  --text-primary: #3d56ba; /* 主要文字 */
  --text-secondary: #dcdef0; /* 次要文字 */
  --text-disabled: #999999; /* 禁用文字 */
  --text-form-label: #5c5c5c; /* 表单标签文字 */
  --text-icon: #5763aa;

  /* 边框颜色 */
  --border-color: #a5b0dd; /* 边框色 */
  --border-color-2: #dcddf2;
  /* 背景色 */
  --bg-primary: #f1f2ff; /* 主背景 */
  --bg-secondary: #f5f5f5; /* 次要背景 */
  --bg-logo-color: #aab6e4; /* 主logo背景色 */
  --bg-category-tabs: #e0e2f4; /* 分类tabs背景色 */
  --bg-category-tabs-hover: #cdd2e9; /* 分类tabs背景色 */
  --bg-search-hover: #f2f2f2;
  --bg-upload: #e0e2f4;
}

html,
body {
  width: 100%;
  height: 100%;
}

/* 恢复所有 li 元素的默认样式 */
ul {
  list-style-type: disc;
  margin: 1em 0;
  padding-left: 40px;
}

ol {
  list-style-type: decimal;
  margin: 1em 0;
  padding-left: 40px;
}

ul > li {
  display: list-item;
  list-style-type: disc;
}

ol > li {
  display: list-item;
  list-style-type: decimal;
}

/* 嵌套列表样式 */
ul ul > li {
  list-style-type: circle;
}

ul ul ul > li {
  list-style-type: square;
}

ol ol > li {
  list-style-type: lower-alpha;
}

ol ol ol > li {
  list-style-type: lower-roman;
}


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
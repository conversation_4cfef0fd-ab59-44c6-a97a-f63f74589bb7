import {RxJsonSchema} from 'rxdb';

export interface EdgeDocType {
    id: string; // 边ID
    wid: string; // 工作区ID
    nid: string; // 节点ID
    pid: string; // 父节点ID
    type: string; // 类型
    label?: string; // 边标签
    style?: Object;
}

export const edgeSchema: RxJsonSchema<EdgeDocType> = {
    title: 'edge schema',
    version: 0,
    description: '边模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        nid: {
            type: 'string',
            maxLength: 100
        },
        pid: {
            type: 'string',
            maxLength: 100
        },
        type: {
            type: 'string',
            maxLength: 50
        },
        label: {
            type: 'string',
            maxLength: 200
        },
        style: {
            type: 'object'
        }
    },
    required: ['id', 'wid', 'nid', 'pid'],
    indexes: ['wid', 'nid', 'pid']
}; 
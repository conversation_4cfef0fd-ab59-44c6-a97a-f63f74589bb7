// 导出原始的 FileTreePanel
// export { default as FileTreePanel } from './FileTreePanel';
export type { FileTreeNode } from './HeadlessFileTreePanel';

// 导出新的基于 headless-tree 的组件
export { default as HeadlessFileTreePanel } from './HeadlessFileTreePanel';

// 导出自定义Hooks
export { useDragAndDrop } from './hooks/use-drag-and-drop';
export { useFolderCreation } from './hooks/use-folder-creation';
export { useDeleteOperations } from './hooks/use-delete-operations';
export { useRenameOperations } from './hooks/use-rename-operations';
export { useFileUpload } from './hooks/use-file-upload';
export { useTreeState } from './hooks/use-tree-state';

// 样式文件需要手动导入
// import './FileTreePanel.css';  // 完整文件管理
 
// 使用说明：
// - 支持完整的文件夹管理功能
// - 内置新建文件夹、重命名、拖拽等功能
// - 详细测试指南：TESTING.md
// - Loading问题调试：DEBUG-GUIDE.md 
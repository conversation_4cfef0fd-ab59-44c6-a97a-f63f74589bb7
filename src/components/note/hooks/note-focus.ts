import {useCallback} from "react";

export const useNoteFocus = () => {
    // 初始定位
    const focusToEnd = useCallback(() => {
        const selection = window.getSelection()
        if (!selection) return
        const dom = document.querySelector('#vditor-note .vditor-wysiwyg .vditor-reset')
        if (!dom || !dom.lastChild) return
        selection.removeAllRanges()
        const range = document.createRange()
        range.setStartAfter(dom.lastChild)
        range.setEndAfter(dom.lastChild)
        selection.addRange(range)
    }, [])
    // 滚动到光标位置
    const scrollToCursor = useCallback(() => {
        const editorElement = document.querySelector('#vditor-note .vditor-wysiwyg .vditor-reset')
        if (!editorElement) return
        const selection = window.getSelection()
        if (!selection || selection.rangeCount === 0) return
        const range = selection.getRangeAt(0)
        const currentNode = range.startContainer
        if (currentNode.nodeType === Node.TEXT_NODE && currentNode.parentElement) {
            currentNode.parentElement.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if (currentNode.nodeType === Node.ELEMENT_NODE) {
            (currentNode as Element).scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [])
    return {focusToEnd, scrollToCursor}
}

/* 拖拽标签系统样式 */

/* 滚动条样式 */
.scrollbar-thin {
    scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar {
    height: 6px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 拖拽预览样式 */
.drag-preview {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border: 2px solid #3b82f6;
    background: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #1e40af;
    z-index: 9999;
}

/* 标签拖拽预览样式 */
.drag-preview-tab {
    pointer-events: none;
    user-select: none;
    filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.15));
    animation: dragFloat 2s ease-in-out infinite;
}

/* 预览窗口内容样式 */
.drag-preview-tab .preview-content {
    transform-origin: top left;
    overflow: hidden;
}

/* 确保预览内容不会影响布局 */
.drag-preview-tab .preview-content * {
    pointer-events: none !important;
    user-select: none !important;
}

.drag-preview-tab .animate-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* 拖拽浮动动画 */
@keyframes dragFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-3px);
    }
}

/* 拖拽预览窗口动画 */
@keyframes previewPulse {
    0%, 100% {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow: 0 20px 45px rgba(0, 0, 0, 0.25);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 拖拽时的过渡动画 */
.drag-preview-tab .transition-colors {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 预览窗口样式 */
.drag-preview-tab .preview-window {
    animation: previewPulse 2s ease-in-out infinite;
}

/* 绿色阴影效果 */
.shadow-green-200 {
    box-shadow: 0 25px 50px -12px rgba(34, 197, 94, 0.25);
}

.shadow-gray-200 {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 拖拽悬停动画 */
.drag-hover {
    animation: dragHover 0.3s ease-in-out;
}

@keyframes dragHover {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 标签拖拽时的样式 */
.tab-dragging {
    opacity: 0.5;
    transform: rotate(2deg) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: none;
}

/* 拖拽区域指示器 */
.drop-zone-active {
    background: linear-gradient(45deg, #dbeafe, #bfdbfe);
    border-color: #3b82f6;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* 窗口拖拽时的样式 */
.window-dragging {
    opacity: 0.9;
    transform: scale(0.98);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: none;
}

/* 窗口激活时的样式 */
.window-active {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

/* 标签合并指示器 */
.merge-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(59, 130, 246, 0.1);
    border: 2px dashed #3b82f6;
    border-radius: 8px;
    pointer-events: none;
    animation: mergeIndicator 0.5s ease-in-out infinite alternate;
}

@keyframes mergeIndicator {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.7;
    }
}

/* 新窗口创建动画 */
.new-window-animation {
    animation: newWindowCreate 0.3s ease-out;
}

@keyframes newWindowCreate {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 窗口关闭动画 */
.window-closing {
    animation: windowClose 0.2s ease-in forwards;
}

@keyframes windowClose {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* 标签切换动画 */
.tab-content-enter {
    opacity: 0;
    transform: translateX(10px);
}

.tab-content-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.tab-content-exit {
    opacity: 1;
    transform: translateX(0);
}

.tab-content-exit-active {
    opacity: 0;
    transform: translateX(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .floating-window {
        width: 90vw !important;
        height: 70vh !important;
        left: 5vw !important;
        top: 15vh !important;
    }

    .new-window-drop-zone {
        width: 80px !important;
        height: 80px !important;
        bottom: 20px !important;
        right: 20px !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .draggable-tab {
        border: 2px solid;
    }

    .drop-zone-active {
        border-width: 3px;
    }
}

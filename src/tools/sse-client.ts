// TokenInfo 类型定义
import {sessionService} from "@/local";
import {fetchAllPdfContent} from "@/tools/pdfUtils.ts";

interface TokenInfo {
  token: string;
  expire: number;
}
// 实例对象接口
export interface SSEClientInstance {
  // 公共属性
  onComplete: ((data: string) => void) | null;
  // 公共方法
  start(requestBody: any, onData: (data: string) => void, onError: (error: Error) => void): Promise<void>;
  abort(): void;
  reconnect(): Promise<void>;
}

// SSE 客户端接口
interface ISSEClient {
  // 方法定义
  start(requestBody: any, onData: (data: string) => void, onError: (error: Error) => void): Promise<void>;
  abort(): void;
  reconnect(): Promise<void>;

  // 可选：如果想要暴露 parseEventStream 方法，也可以加入接口
  // parseEventStream(chunk: string, onData: (data: string) => void): void;
}

export class SSEClient implements ISSEClient {
  private controller: AbortController;
  private url: string;
  private tokenInfo: TokenInfo | null;
  private maxRetries: number = 10;  // 最大重试次数
  private retryDelay: number = 1000;  // 重试延迟(ms)
  private currentRetry: number = 0;
  private requestBody: any;
  private responseDate: Array<string> = [];
  private onData: ((data: string) => void) | null = null;
  private onError: ((error: Error) => void) | null = null;
  // 判断token

  constructor(url: string) {
    this.url = url;
    this.tokenInfo = JSON.parse(localStorage.getItem('tokenInfo') || '{}') as TokenInfo | null;
    this.controller = new AbortController();
  }

  // 发起 SSE 请求
  public async start(requestBody: any, onData: (data: string) => void, onError: (error: Error) => void) {
    console.log("请求体", requestBody)
    
    // 统一使用 OpenAI API 格式
    this.requestBody = await this.buildOpenAIRequest(requestBody);
    
    this.onData = onData;
    this.onError = onError;
    await this.connect();
  }



  // 构建 OpenAI API 请求格式
  private async buildOpenAIRequest(requestBody: any) {
    try {
      let messages: Array<{role: string, content: string}> = [];
      
      // 添加系统提示
      messages.push({
        role: 'system',
        content: '你是一个智能助手，请根据提供的上下文信息回答用户的问题。'
      });
      
      // 如果有会话ID，获取历史对话
      if (requestBody.sid) {
        try {
          const detail = await sessionService.detail({sid: requestBody.sid, rid: requestBody.rid || 0, page_size: 3})
          
          // 添加历史对话
          detail.chat.reverse().forEach((item) => {
            messages.push({
              role: 'user',
              content: item.question
            });
            messages.push({
              role: 'assistant',
              content: item.answer
            });
          });
        } catch (error) {
          console.warn('获取历史对话失败，继续发送当前问题:', error);
        }
      }
      
      // 构建当前问题内容
      let currentContent = requestBody.question;
      
      // 添加引用信息
      if (requestBody.Refs && requestBody.Refs.length > 0) {
        currentContent += '\n\n📚 参考资料：\n';
        requestBody.Refs.forEach((ref: any, index: number) => {
          currentContent += `${index + 1}. **${ref.title}**`;
          if (ref.page) currentContent += ` (第${ref.page}页)`;
          if (ref.content) currentContent += `\n   ${ref.content}`;
          currentContent += '\n';
        });
        currentContent += '\n💡 请基于以上参考资料来回答问题，并在回答中标明信息来源。';
      }
      
      messages.push({
        role: 'user',
        content: currentContent
      });
      
      return {
        model: 'gpt-3.5-turbo',
        messages: messages,
        stream: true,
        temperature: 0.7,
        max_tokens: 2000
      };
    } catch (error) {
      console.error('构建 OpenAI 请求失败:', error);
      // 如果所有都失败，至少发送当前问题
      let fallbackContent = requestBody.question || '请帮助我解答问题。';
      
      // 即使在fallback情况下也尝试添加引用
      if (requestBody.Refs && requestBody.Refs.length > 0) {
        fallbackContent += '\n\n参考资料：\n';
        requestBody.Refs.forEach((ref: any, index: number) => {
          fallbackContent += `${index + 1}. ${ref.title || 'Unknown'}: ${ref.content || ''}\n`;
        });
      }
      
      return {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个智能助手，请回答用户的问题。'
          },
          {
            role: 'user',
            content: fallbackContent
          }
        ],
        stream: true,
        temperature: 0.7,
        max_tokens: 2000
      };
    }
  }

  private async connect() {
    try {
      // 构建 OpenAI API 请求头
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-pWXcatwwLVmLvw1Kzb5V7f3v2gohFdSaTa9yeOwLtioHFrzM",
      };
      
      const response = await fetch(this.url, {
        method: "POST",
        headers,
        body: JSON.stringify(this.requestBody),
        signal: this.controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      this.currentRetry = 0; // 连接成功后重置重试次数
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      const readStream = async () => {
        try {
          const { done, value } = await reader.read();
          if (done) {
            this.onComplete?.(this.responseDate.join(''));
            this.responseDate = [];
            return;
          }

          const chunk = decoder.decode(value, { stream: true });
          // 统一使用 OpenAI 流解析
          this.parseOpenAIStream(chunk, this.onData!);
          await readStream();
        } catch (error) {
          this.handleError(error as Error);
        }
      };

      await readStream();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private async handleError(error: Error) {
    if (this.currentRetry < this.maxRetries) {
      this.currentRetry++;
      console.log(`Reconnecting... Attempt ${this.currentRetry} of ${this.maxRetries}`);
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      await this.connect();
    } else {
      this.onError?.(error);
    }
  }

  // 手动中断请求
  public abort() {
    this.controller.abort();
    this.currentRetry = this.maxRetries; // 防止重连
    this.onComplete?.(this.responseDate.join(''));
    this.responseDate = [];
  }

  // 手动重新连接
  public async reconnect() {
    this.currentRetry = 0;
    this.controller = new AbortController(); // 创建新的 controller
    await this.connect();
  }



  // 解析 OpenAI 流式数据
  private parseOpenAIStream(chunk: string, onData: (data: string) => void) {
    const lines = chunk.split('\n');
    
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6); // 移除 'data: ' 前缀
        
        if (data === '[DONE]') {
          // OpenAI 流结束标记
          return;
        }
        
        if (data.trim() === '') {
          continue;
        }
        
        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices?.[0]?.delta?.content;
          
          if (content) {
            this.responseDate.push(content);
            onData(content);
          }
        } catch (error) {
          console.warn('解析 OpenAI 数据失败:', error, data);
        }
      }
    }
  }
  public onComplete: ((data: string) => void) | null = null;
}




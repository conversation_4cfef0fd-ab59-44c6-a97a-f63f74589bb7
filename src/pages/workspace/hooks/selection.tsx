// import { IconFontNode } from "@/common/constant";
import { useChatPad } from "@/components/chat-panel/hooks/use-chat-pad";
import { ChatToNodeIcon, NotionIcon } from "@/components/icons";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { Tooltip } from "antd";
import { FC } from "react";
import ReactDOM from "react-dom/client";
import { processContent } from '@/tools/parseMarkdown'
import { useNodeAdd } from "@/components/flow/hooks/node-add";
import { useNoteStore } from "@/store/note-store";
const initRootInstance = (): any => {
    let init = false
    let root: any = null

    return () => {
        if (!init) {
            let OperateDom = document.createElement('div')
            document.body.append(OperateDom)
            init = true
            root = ReactDOM.createRoot(OperateDom)
        }
        return root
    }
}

type mouseEvent = {
    onMouseUp: React.MouseEventHandler<HTMLDivElement> | undefined,
    onMouseDown: React.MouseEventHandler<HTMLDivElement> | undefined,
}

export const useSelectionHook = (): [
    mouseEvent,
] => {

    // const { importChatToNotepad } = useChatPad();
    const { setInsertContent } = useNoteStore();
    const { notePanelOpen, toggleNotePanel } = usePanelOpenStore();
    const { handleAddNode } = useNodeAdd()
    const openNotePanel = (answer = '') => {
        if (!answer) return
        if (!notePanelOpen) {
            toggleNotePanel(true);
        }
        setInsertContent(`${answer}\n\n (来源: 引用chat) `)
    }

    const renderMenu = (e: MouseEvent | React.MouseEvent) => {
        const selectText = document.getSelection()?.toString()
        console.log('selectText',selectText)
        if (!selectText) return
        const x = e?.clientX || 0
        const y = e?.clientY || 0
        const root = initRootInstance()()

        root.render(<SelectionMenu
            position={{ left: x, top: y }}
            toNote={() => {
                openNotePanel(selectText)
            }}
            toFlow={() => {
                handleAddNode({
                    setCenter: true,
                    content: selectText,
                    // TODO : 确定引用信息
                    // refInfo: {
                    //     test: 'refinfo'
                    // }
                })
            }}
        />)

        document?.addEventListener('mousedown', (e: MouseEvent) => {
            root.render(null)
        })
    }

    const onMouseEventFn = (): mouseEvent => {
        let target: any = null
        return {
            onMouseUp(e) {
                if (e?.target === target) {
                    renderMenu(e)
                }

                target = null
            },
            onMouseDown(e) {
                target = e?.target
            }
        }
    }

    const onMouseEvent = onMouseEventFn()

    return [
        onMouseEvent
    ]
}

type PostionProps = {
    position: {
        left: number,
        top: number,
    },
    toNote?: () => void,
    toFlow?: () => void
}

const SelectionMenu: FC<PostionProps> = (props) => {
    const {
        position: {
            left = 0,
            top = 0,
        },
        toNote = () => { },
        toFlow = () => { },
    } = props

    return <div
        className="fixed padding-10 flex "
        style={{
            zIndex: 1000,
            left,
            top,
        }}
        onClick={(e) => e?.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
    >
        <Tooltip title="转到记事本">
            <div
                className="flex items-center"
                onClick={() => toNote?.()}
            >
                <NotionIcon size={30} color="#A99CF5" />{" "}
            </div>
        </Tooltip>

        <Tooltip title="创建节点">
            <div
                className="flex items-center"
                onClick={() => toFlow?.()}
            >
                <ChatToNodeIcon size={30} color="#A99CF5" />{" "}
            </div>
        </Tooltip>
    </div>
}

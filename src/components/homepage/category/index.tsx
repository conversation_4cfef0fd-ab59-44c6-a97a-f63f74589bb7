import {useEffect, useState} from "react";
import styled from "styled-components";
import {CreateProjectButton} from "./create-project-button";
import {CategoryItem} from "./category-item";
import {ConfirmModalWrapper} from "@/components/pop-window/confirm-window";
import {HomeIcon, PlusMini, TagIcon} from "@/components/icons";
import {useHomeStore} from "@/store/home-store";
import {CategoryInput} from "./newcategory";
import {getRandomColor} from "@/tools";
import {createCategory, deleteCategory, updateCategory} from "@/api/category";
import {Dropdown, message, Space} from "antd";
import {DeleteOutlined, EditOutlined, MoreOutlined} from "@ant-design/icons";
import {CategoryType} from "../response-type";
import {useCategoryData} from "./useCategoryData";

const Container = styled.div`
  width: 17.5rem;
  height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
  background: #e0e2f4;
  border-top-right-radius: 1.875rem;
`;
const BottomBarWrapper = styled.div<{ $isPinned?: boolean }>`
  position: sticky;
  bottom: 0;
  padding: 5px 28px;
  background: var(--bg-category-tabs);
  z-index: 20;

  ${(props) =>
    props.$isPinned &&
    `
    &::before {
      content: "";
      position: absolute;
      top: -10px;
      left: 0;
      right: 0;
      height: 10px;
      background: linear-gradient(transparent, var(--bg-category-tabs));
      pointer-events: none;
    }
  `}

  ${(props) =>
    props.$isPinned &&
    `
    box-shadow: 0 -3px 3px -1px #000;
  `}
`;
const BottomBar = styled.div<{ $isPinned?: boolean }>`
  width: 100%;
  height: 40px;
  display: flex;
  transition: all 0.2s ease;
  cursor: pointer;
  background: var(--bg-category-tabs);
  border-radius: 8px;
  position: relative;
  padding-left: 10px;

  &:hover {
    background: linear-gradient(
      to right,
      var(--bg-category-tabs-hover) 0%,
      var(--bg-category-tabs-hover) 40%,
      transparent 100%
    );
  }

  ${(props) =>
    props.$isPinned &&
    `
    box-shadow: 0 -4px 6px -1px var(--bg-category-tabs);
  `}

  &.active {
    background: linear-gradient(
      to right,
      var(--bg-category-tabs-hover) 0%,
      var(--bg-category-tabs-hover) 40%,
      transparent 100%
    );
    &::before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -7px;
      width: 4px;
      border-radius: 2px;
      height: 60%;
      background: var(--text-primary);
    }
  }
`;
export const Category = () => {
  useCategoryData();
  const {
    categories,
    setCurrentCategory,
    setSelectCategoryId,
    removeCategory,
    removeProjectByCid,
    modifyCategory,
    addCategory,
  } = useHomeStore();
  const [colorList, setColorList] = useState<{ cid: string; color: string }[]>(
    []
  );
  const [clickCreateBtn, setClickCreateBtn] = useState<string | null>(null);
  const [createCategoryName, setCreateCategoryName] = useState<string>("");
  const [openMode, setOpenMode] = useState<"create" | "edit" | null>(null);
  const [randomColor, setRandomColor] = useState<string>("");
  const [editId, setEditId] = useState<number | string>(-1);
  const [newCategoryName, setNewCategoryName] = useState<string>("");
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<number | string>(-1);
  // 添加新的分类
  const onAddCategoryTab = async () => {
    if (createCategoryName.trim() === "") {
      setOpenMode(null);
      setNewCategoryName("");
    } else {
      if ((createCategoryName.trim() as string).length > 0) {
        try {
          const res = (await createCategory({
            cname: createCategoryName,
          })) as any;
          if (res.code === 0) {
            addCategory({
              cid: res.data.cid,
              cname: createCategoryName,
              wnum: 0,
            });
            setOpenMode(null);
            setCreateCategoryName("");
            setSelectCategoryId(res.data.cid);
            message.success("新建成功");
            const colors =
              localStorage.getItem("category_color_list") !== null
                ? JSON.parse(
                    localStorage.getItem("category_color_list") as string
                  )
                : [];
            if (colors) {
              colors.push({
                cid: res.data.cid,
                color: randomColor,
              });
              localStorage.setItem(
                "category_color_list",
                JSON.stringify(colors)
              );
              setColorList(colors);
            }
          } else {
            message.error(res.msg);
          }
        } catch (error) {
          message.error((error as any).msg);
        }
      }
    }
    setClickCreateBtn(null);
    setNewCategoryName("");
  };
  useEffect(() => {
    if (colorList) {
      const colors =
        localStorage.getItem("category_color_list") !== null
          ? JSON.parse(localStorage.getItem("category_color_list") as string)
          : [];
      setColorList(colors);
    }
  }, [categories]);
  return (
    <Container>
      <CreateProjectButton />
      <div className="h-[calc(100vh-90px)]  scrollbar-custom box-border flex-1 flex flex-col gap-[3px] relative">
        {categories.map((item, index) => {
          const curCategory = colorList.find((color) => {
            return color.cid === item.cid;
          });
          return openMode === "edit" && editId === item.cid ? (
            <CategoryInput
              key={item.cid}
              value={newCategoryName}
              randomColor={curCategory?.color ? curCategory?.color : ""}
              placeholder={"Edit category"}
              onChange={(value: string) => setNewCategoryName(value)}
              onConfirm={async () => {
                try {
                  const res = await updateCategory({
                    cid: item.cid,
                    cname: newCategoryName,
                  });
                  if ((res as any).code === 0) {
                    modifyCategory({
                      cid: item.cid,
                      cname: newCategoryName,
                      wnum: item.wnum,
                    });
                    setOpenMode(null);
                    setNewCategoryName("");
                    message.success("更新成功");
                  }
                } catch (error) {
                  message.error((error as any).msg);
                }
              }}
            />
          ) : index <= 1 ? (
            <CategoryItem
              key={item.cid}
              category={item}
              index={index}
              icon={
                index === 0 ? (
                  <HomeIcon color="#3856ce" size={12} />
                ) : (
                  <div
                    className="flex justify-center items-center rounded-[6px] "
                    style={{
                      width: "18px",
                      height: "18px",
                      background: "#8e9bd6",
                    }}
                  >
                    <TagIcon color="white" />
                  </div>
                )
              }
            />
          ) : (
            <div key={item.cid} className="relative group">
              <CategoryItem
                category={item}
                index={index}
                icon={
                  index === 0 ? (
                    <HomeIcon color="var(--color-primary)" />
                  ) : (
                    <div
                      className="flex justify-center items-center rounded-[6px] "
                      style={{
                        width: "18px",
                        height: "18px",
                        backgroundColor: colorList.find(
                          (colorItem) => colorItem.cid === item.cid
                        )?.color,
                      }}
                    >
                      <TagIcon color="white" />
                    </div>
                  )
                }
                onEdit={(category: CategoryType) => {
                  setEditId(category.cid);
                  setNewCategoryName(category.cname);
                  setOpenMode("edit");
                }}
              />
              <div className="absolute right-10 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: "edit",
                        label: (
                          <Space>
                            <EditOutlined />
                            <div>重命名</div>
                          </Space>
                        ),
                        onClick: () => {
                          setOpenMode("edit");
                          setEditId(item.cid);
                          setNewCategoryName(item.cname);
                        },
                      },
                      {
                        key: "delete",
                        label: (
                          <Space
                            onClick={() => {
                              setOpenConfirm(true);
                              setDeleteId(item.cid);
                            }}
                          >
                            <DeleteOutlined />
                            <div>删除</div>
                          </Space>
                        ),
                      },
                    ],
                  }}
                  trigger={["click"]}
                  placement="bottomRight"
                >
                  <MoreOutlined className="cursor-pointer text-lg p-1 hover:bg-gray-200 hover:bg-opacity-50 rounded-full" style={{ fontSize: '20px' }} />
                </Dropdown>
              </div>
            </div>
          );
        })}
        {openMode === "create" && (
          <CategoryInput
            value={createCategoryName}
            placeholder="新建分类"
            randomColor={randomColor}
            onChange={(value: string) => {
              setCreateCategoryName(value);
            }}
            onConfirm={onAddCategoryTab}
          />
        )}
        <BottomBarWrapper>
          <BottomBar
            className={`${clickCreateBtn === "create_btn" ? "active" : ""}`}
            onClick={() => {
              setClickCreateBtn("create_btn");
              setOpenMode("create");
              setRandomColor(getRandomColor());
            }}
          >
            <Space className="flex-1 flex items-center gap-3">
              <div
                className="flex justify-center items-center rounded-[6px] "
                style={{
                  width: "18px",
                  height: "18px",
                  background: "#8e9bd6",
                }}
              >
                <PlusMini color="white" />
              </div>
              <div className="text-[14px]" style={{ letterSpacing: "0.7px" }}>
                创建新的分类
              </div>
            </Space>
          </BottomBar>
        </BottomBarWrapper>
      </div>
      {openConfirm && (
        <ConfirmModalWrapper
          confirmOpen={openConfirm}
          title="删除分类"
          onClose={() => {
            setOpenConfirm(false);
          }}
          onConfirmHandle={async () => {
            try {
              const res = await deleteCategory({ cid: deleteId });
              if ((res as any).code === 0) {
                setOpenConfirm(false);
                setDeleteId("");
                removeCategory(deleteId);
                removeProjectByCid(deleteId);
                const deleteCategory = categories.find(
                  (category) => category.cid === deleteId
                );
                const deleteIndex = categories.findIndex(
                  (category) => category.cid === deleteId
                );
                if (deleteIndex && deleteCategory) {
                  if (categories.length - 1 === deleteIndex) {
                    setCurrentCategory(categories[categories.length - 2]);
                    setSelectCategoryId(categories[categories.length - 2].cid);
                  } else {
                    setCurrentCategory(categories[categories.length - 1]);
                    setSelectCategoryId(categories[categories.length - 1].cid);
                  }
                  // 删除全部里的项目数
                  const allItem = categories.find((item) => item.cid === "-1");
                  if (allItem) {
                    modifyCategory({
                      ...allItem,
                      wnum: allItem.wnum - deleteCategory.wnum,
                    });
                  }
                }
                message.success("删除成功");
              }
            } catch (error) {
              message.error("删除失败");
            }
          }}
          handleType="delete"
          component={<div>确定删除分类吗？</div>}
        />
      )}
    </Container>
  );
};

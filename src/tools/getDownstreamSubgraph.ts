import { Node, Edge } from "@xyflow/react";

export const getDownstreamSubgraph = (
    startNodeId: string,
    nodes: Node[],
    edges: Edge[]
): { nodes: Node[]; edges: Edge[] } => {
    const visitedNodeIds = new Set<string>();
    const visitedEdgeIds = new Set<string>();

    const queue = [startNodeId];

    while (queue.length > 0) {
        const currentNodeId = queue.shift()!;
        if (visitedNodeIds.has(currentNodeId)) continue;

        visitedNodeIds.add(currentNodeId);
        const outgoingEdges = edges.filter(
            edge => edge.source === currentNodeId && !visitedEdgeIds.has(edge.id)
        );

        outgoingEdges.forEach(edge => {
            visitedEdgeIds.add(edge.id);
            if (!visitedNodeIds.has(edge.target)) {
                queue.push(edge.target);
            }
        });
    }

    return {
        nodes: nodes.filter(node => visitedNodeIds.has(node.id)),
        edges: edges.filter(edge => visitedEdgeIds.has(edge.id))
    };
}; 
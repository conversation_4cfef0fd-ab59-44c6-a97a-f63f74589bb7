.react-flow__node {
    /* border-width: 2px; */
}

.react-flow__node.highlight {
    border-color: #40a9ff;
}

.react-flow__node.selectable.selected,
.react-flow__node.selectable.selected:focus {
    border-color: #40a9ff;
}

.hidden .react-flow__edge-path {
    display: none;
}

.react-flow__edge.selectable:hover .react-flow__edge-path,
.react-flow__edge.selectable.selected .react-flow__edge-path {
    stroke: black;
}

.react-flow__handle {
    background-color: #ffffff;
    border-color: grey;
    width: 8px;
    height: 8px;
}

.react-flow__handle.connectionindicator:hover {
    pointer-events: all;
    border-color: black;
    background-color: white;
}

.react-flow__handle.connectionindicator:focus,
.react-flow__handle.connectingfrom,
.react-flow__handle.connectingto {
    border-color: black;
}

@keyframes dashdraw {
    from {
        stroke-dashoffset: 10;
    }
    to {
        stroke-dashoffset: 0;
    }
}

/* Helper Lines 辅助线样式 */
.react-flow__helper-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.react-flow__helper-line {
  stroke: black;
  stroke-width: 1;
  /* stroke-dasharray: 4 4; */
  opacity: 0.8;
  animation: helperLinePulse 1s ease-in-out infinite;
}

@keyframes helperLinePulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 节点高亮样式已合并到上面的 .react-flow__node.highlight 中 */

/* 临时连线样式 */
.react-flow__edge.temp {
  stroke: #ff6b6b;
  /* stroke-dasharray: 5 5; */
  animation: tempEdgePulse 1s ease-in-out infinite;
}

@keyframes tempEdgePulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 隐藏的连线 */
.react-flow__edge.hidden {
  opacity: 0.2;
  /* stroke-dasharray: 2 2; */
}

/* 编辑节点窗口样式 */
.edit-node-window {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.edit-node-drag-handle {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  transition: background 0.2s ease;
}

.edit-node-drag-handle:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.edit-node-drag-handle:active {
  cursor: grabbing;
  background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
}

/* 确保 Vditor 在编辑窗口中正常显示 */
.edit-node-window .vditor {
  border: none;
  box-shadow: none;
}

.edit-node-window .vditor-content {
  background: white;
}

/* 防止编辑窗口内的元素触发父级拖拽 */
.edit-node-window .ant-input,
.edit-node-window .ant-btn,
.edit-node-window .vditor-content,
.edit-node-window .vditor-toolbar {
  pointer-events: auto;
}

/* 编辑窗口动画效果 */
.edit-node-window {
  animation: editNodeFadeIn 0.3s ease-out;
}

@keyframes editNodeFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 确保编辑窗口在所有其他元素之上 */
.edit-node-window {
  z-index: 100000 !important;
}

/* 编辑窗口悬浮效果 */
.edit-node-window:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  transition: all 0.2s ease-out;
}

/* Vditor 在编辑窗口中的样式优化 */
.edit-node-window .vditor {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.edit-node-window .vditor-content {
  flex: 1;
  min-height: 0;
}

.edit-node-window .vditor-wysiwyg {
  flex: 1;
  min-height: 0;
}

/* 节点大小调整器样式 */
.react-flow__resize-control {
  /* border: 2px solid #3367d9 !important; */
  background: transparent !important;
}

.react-flow__resize-control.handle {
  background: #3367d9 !important;
  border: 1px solid #fff !important;
  width: 10px !important;
  height: 10px !important;
  /* border-radius: 2px !important; */
  /* 修复偏移问题：确保控制点居中对齐 */
  /* transform: translate(-50%, -50%) !important; */
  position: absolute !important;
  /* 确保控制点在鼠标悬停时保持稳定 */
  transform-origin: center center !important;
}

/* 取消节点四个顶点圆角 */
.rounded-lg {
  border-radius: 0 !important;
}

/* .react-flow__edge-path {
  stroke-dasharray: none !important;
} */
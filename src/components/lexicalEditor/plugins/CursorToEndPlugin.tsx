import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';
import { $getRoot, $getSelection, $isRangeSelection, $createRangeSelection } from 'lexical';

export default function CursorToEndPlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // 延迟执行，确保编辑器完全初始化
    const timeoutId = setTimeout(() => {
      editor.update(() => {
        const root = $getRoot();
        const lastChild = root.getLastChild();
        
        if (lastChild) {
          // 创建新的选择范围
          const selection = $createRangeSelection();
          const textContentSize = lastChild.getTextContentSize();
          
          // 设置光标到最后一个节点的末尾
          selection.anchor.set(lastChild.getKey(), textContentSize, 'text');
          selection.focus.set(lastChild.getKey(), textContentSize, 'text');
          
          // 应用选择
          const currentSelection = $getSelection();
          if (currentSelection) {
            currentSelection.dirty = true;
          }
        }
      });
    }, 100); // 延迟100ms确保编辑器完全加载

    return () => {
      clearTimeout(timeoutId);
    };
  }, [editor]);

  return null;
} 
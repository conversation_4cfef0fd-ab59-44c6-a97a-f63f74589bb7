import {CustomNode, useFlowStore} from "@/store/flow-store.ts";
import {useCallback} from "react";
import {removeNode} from "@/api/canvas";
import {message} from "antd";
import {getNodeHighlight, usePdfStore} from "@/store/pdf-store.ts";
import { useChatStore } from "@/store/workerspace-store/chat-store";

const useNodeDelete = () => {
    const {deleteNodes} = useFlowStore((state) => ({
        deleteNodes: state.deleteNodes
    }))
    const {batchDelFooterRef} = useChatStore()
    const {batchRemoveHighlights} = usePdfStore((state) => ({
        batchRemoveHighlights: state.batchRemoveHighlights,
    }))
    // 删除节点
    const handleDeleteNode = useCallback(async (ids: string[]) => {
        // 接口
        try {
            await removeNode(ids)
        } catch (e) {
            message.error("删除失败");
            return
        }
        const pdfState = usePdfStore.getState()
        // 移除tip
        pdfState.pdfs.get(pdfState.activeAid)?.pdfHighlighterUtils?.setTip(null)
        // 删除节点
        deleteNodes(ids)
        batchDelFooterRef(ids)
        // 删除高亮
        const highlights = getNodeHighlight(ids)
        if (highlights.length === 0) {
            return
        }
        batchRemoveHighlights(highlights)
    }, [deleteNodes, batchRemoveHighlights])

    // flow 删除节点
    const onNodesDelete = useCallback(async (deletedNodes: CustomNode[]) => {
        const deletedIds = deletedNodes.map((node) => node.id);
        await handleDeleteNode(deletedIds)
        batchDelFooterRef(deletedIds)
    }, [handleDeleteNode])

    return {
        onNodesDelete,
        handleDeleteNode
    }
}

export {useNodeDelete}
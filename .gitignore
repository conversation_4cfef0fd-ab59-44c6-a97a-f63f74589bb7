# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
src/test.tsx
.vscode
.idea
src copy
npm-debug.log*
yarn-debug.log*
yarn-error.log*
src/备份

test.html
test.svg
test.ts

# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
.taskmaster
.cursor

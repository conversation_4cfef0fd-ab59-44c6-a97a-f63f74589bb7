import { useNavigate } from "react-router-dom";

interface UserProps {
  className?: string;
}

export const User = ({ className }: UserProps) => {
  const navigate = useNavigate();

  return (
    <div
      className={`flex-shrink-0 w-[42px] h-[42px] text-[10px] cursor-pointer text-[var(--text-primary)] bg-[var(--text-secondary)] rounded-full text-center leading-[42px] mr-[85px]  ${className}`}
      onClick={() => {
        localStorage.removeItem("tokenInfo");
        navigate("/login");
      }}
    >
      User
    </div>
  );
};

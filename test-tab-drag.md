# 标签页拖拽功能测试

## 实现的功能

1. **每个标签页都是一个窗口**：
   - 每个标签页在创建时都会对应一个窗口实例
   - 窗口有两种状态：在标签栏中（隐藏）和独立显示

2. **拖拽行为**：
   - 拖拽标签页时，实际上是在拖拽对应的窗口
   - 脱离标签栏时，窗口的标题栏和内容区域显示出来
   - 窗口跟随鼠标移动

3. **视觉效果**：
   - 在标签栏内拖拽：标签有旋转和透明度效果
   - 脱离标签栏：标签隐藏，显示完整窗口
   - 窗口实时跟随鼠标位置

## 测试步骤

1. 打开应用，确保有多个PDF标签页
2. 拖拽一个标签页，观察以下效果：
   - 在标签栏内：标签有视觉反馈
   - 拖出标签栏：标签消失，窗口出现并跟随鼠标
3. 释放拖拽：
   - 在空白区域：创建独立窗口
   - 在其他标签栏：合并到目标容器

## 技术实现要点

- 使用 `isInTabBar` 和 `isDragging` 状态控制窗口显示
- 实时更新窗口位置跟随鼠标
- 使用 `getEmptyImage()` 隐藏默认拖拽预览
- 通过 `requestAnimationFrame` 优化性能

import {useCallback} from "react";
import {CustomNode, useFlowStore} from "@/store/flow-store.ts";

const useNodeClick = () => {
    const {updateNodes} = useFlowStore((state) => ({
        updateNodes: state.updateNodes,
    }))
    const onNodeClick = useCallback((_: React.MouseEvent, node: CustomNode) => {
        updateNodes([{"id": node.id, selectedFromHighlight: false}])
    }, []);
    return {
        onNodeClick
    }
}

export {useNodeClick}
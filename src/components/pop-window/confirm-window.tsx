import styled from "styled-components";
import { Modal } from "antd";
import { ReactNode } from "react";
// 确认操作的二次确认弹窗样式
const ConfirmModal = styled(Modal)`
  :where(.css-dev-only-do-not-override-xu9wm8).ant-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .ant-modal-content {
    height: 200px;
    width: 420px;
    background: #f1f2ff;
    padding: 0;
    margin-top: 180px; // 添加负的上边距使弹窗整体向上移动
    margin-left: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
  }
  .ant-modal-header {
    background: #f1f2ff;
    .ant-modal-title {
      color: #3d56ba;
    }
  }

  .ant-modal-footer {
    background: #f1f2ff;
  }
`;

type ConfirmModalProps = {
  title: string | ReactNode;
  confirmOpen: boolean;
  onClose: () => void;
  onConfirmHandle: () => void;
  handleType: string;
  component: ReactNode;
};
export const ConfirmModalWrapper = ({
  title,
  confirmOpen,
  onConfirmHandle,
  onClose,
  handleType,
  component,
}: ConfirmModalProps) => {
  return (
    <ConfirmModal
      title={title}
      open={confirmOpen}
      onClose={onClose}
      onOk={onConfirmHandle}
      onCancel={onClose}
      okText={handleType === "delete" ? "删除" : "确认"}
      cancelText={handleType === "delete" ? "取消" : "取消"}
      okButtonProps={{
        style: {
          backgroundColor: handleType === "delete" ? "#e46759" : "#3D56BA",
          width: "120px",
          height: "40px",
          borderRadius: "4px",
          border: "none",
        },
      }}
      cancelButtonProps={{
        style: {
          backgroundColor: "#E0E2F4",
          color: "#000",
          width: "120px",
          height: "40px",
          borderRadius: "4px",
          border: "none",
        },
      }}
    >
      {component}
    </ConfirmModal>
  );
};

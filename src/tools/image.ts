export const convertImageToBase64 = (files: File[]): Promise<Array<{ name: string; base64: string }>> => {
    const promises = files.map((file) => {
        return new Promise<{ name: string; base64: string }>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve({name: file.name, base64: reader.result as string});
            reader.onerror = (error) => reject(error);
            reader.readAsDataURL(file);
        });
    });
    return Promise.all(promises);
}
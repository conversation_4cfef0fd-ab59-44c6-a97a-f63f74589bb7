# IndexedDB 本地数据库

本目录包含了使用 IndexedDB 实现的本地数据库服务。

## 文件结构

- `db/`: 数据库配置和Schema定义
- `services/`: 各种业务服务实现
- `utils/`: 工具函数
- `index.ts`: 入口文件，导出所有服务

## 使用方法

### 初始化

```javascript
import { getDatabase } from '@/local';

// 初始化数据库
const db = await getDatabase();
```

### 文件夹管理

```javascript
import { folderService } from '@/local';

// 创建文件夹
const { id } = await folderService.createFolder({
    wid: 'workspace_123',
    name: '新文件夹',
    parent_id: 'root'
});

// 获取文件列表
const files = await folderService.getList({
    wid: 'workspace_123',
    type: 'file'
});

// 重命名文件夹
await folderService.rename({
    id: 'folder_id',
    new_name: '新名称'
});

// 删除文件夹
await folderService.delete({ id: 'folder_id' });
```

### 工作区管理

```javascript
import { workspaceService } from '@/local';

// 创建工作区
const createResult = await workspaceService.create({
    Cid: 123456789,
    Title: '我的工作区'
});

// 获取工作区列表
const workspaceList = await workspaceService.list({
    Cid: 123456789,
    Page: 1,
    PageSize: 10
});
```

### 节点管理

```javascript
import { nodeService } from '@/local';

// 创建节点
const createResult = await nodeService.create({
    Wid: 123456789,
    Content: '节点内容',
    X: 100,
    Y: 100
});

// 获取节点列表
const nodeList = await nodeService.list({
    Wid: 123456789
});
```

## 数据库结构

数据库名称: `notes_db`

主要集合:

- `workspaces`: 工作区信息
- `categories`: 分类信息
- `nodes`: 节点信息
- `edges`: 边信息（节点连接）
- `sessions`: 会话信息
- `rounds`: 对话轮次
- `attachments`: 附件和文件夹信息
- `marks`: 标记信息
- `tags`: 标签信息
- `tagGroups`: 标签组信息
- `nodeTags`: 节点标签关系

## 文件夹数据结构

```typescript
interface AttachmentDocType {
    id: string;              // 文件/文件夹ID
    wid: string;             // 工作区ID
    file_name: string;       // 文件名/文件夹名
    type: 'file' | 'folder'; // 类型
    parent_id: string;       // 父文件夹ID
    order: number;           // 排序顺序
    size?: number;           // 文件大小
    mime_type?: string;      // MIME类型
    file_path?: string;      // 文件路径
    create_at: number;       // 创建时间
    update_at: number;       // 更新时间
}
```

## 注意事项

1. 所有接口都返回 Promise 对象，需要使用 async/await 处理
2. 错误处理已在各个服务中实现，可以通过 try/catch 捕获异常
3. 本实现使用浏览器的 IndexedDB 存储数据，数据仅保存在本地
4. 数据会自动添加时间戳（create_at, update_at）
5. 文件夹管理支持层次结构和拖拽排序

## 📋 清理摘要

为了简化代码结构和提高维护性，已移除以下不必要的功能：

### 已删除的文件
- `init/app-init.ts` - 复杂的应用初始化逻辑
- `init/database-init.ts` - 复杂的数据库初始化逻辑
- `services/migration-service.ts` - 数据迁移服务
- `utils/database-test.ts` - 数据库测试工具

### 已移除的功能
- `initializeApp()` - 复杂的应用初始化函数
- `quickDatabaseTest()` - 数据库快速测试
- `DatabaseTester` - 数据库测试类
- `AppInitializer` - 应用初始化管理器
- `migrationService` - 数据迁移服务
- `DatabaseInitializer` - 数据库初始化管理器

### 简化后的优势
- ✅ **代码更简洁** - 移除了复杂的初始化逻辑
- ✅ **更易维护** - 减少了不必要的抽象层
- ✅ **构建更快** - 移除了调试和测试相关代码
- ✅ **逻辑更清晰** - 保留核心功能，专注业务逻辑

现在只需要调用 `getDatabase()` 即可初始化数据库，无需复杂的初始化流程。 
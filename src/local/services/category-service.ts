import {getDatabase} from '../db';
import {generateId} from '@/local';

export interface CreateReq {
    cname: string;
}

export interface CreateResp {
    cid: string;
}

export interface UpdateReq {
    cid: string;
    cname: string;
}

export interface ListReq {
    page: number;
    page_size: number;
}

export interface ListResp {
    list: Array<{
        cid: string;
        cname: string;
        wnum: number;
    }>;
    total: number;
    page_count: number;
}

export interface DeleteReq {
    cid: string;
}

export class CategoryService {
    /**
     * 创建分类
     */
    async create(req: CreateReq): Promise<CreateResp> {
        const db = await getDatabase();
        const cid = generateId();

        await db.categories.insert({
            id: cid,
            cate_name: req.cname,
            ws_num: 0,
        });

        return {cid};
    }

    /**
     * 更新分类
     */
    async update(req: UpdateReq): Promise<void> {
        const db = await getDatabase();
        const category = await db.categories.findOne({
            selector: {id: req.cid}
        }).exec();

        if (!category) {
            throw new Error('分类不存在');
        }

        await category.update({
            $set: {
                cate_name: req.cname
            }
        });
    }

    /**
     * 获取分类列表
     */
    async list(req: ListReq): Promise<ListResp> {
        const db = await getDatabase();
        const skip = (req.page - 1) * req.page_size;
        const limit = req.page_size;

        // 查询总数
        const {total, ws_num} = await db.categories.find().exec().then((docs: any[]) => {
            return {
                total: docs.length,
                ws_num: docs.reduce((acc, cur) => acc + cur.ws_num, 0)
            }
        });

        // 查询列表
        const categories = await db.categories.find({
            sort: [{"create_at": "desc"}],
            skip,
            limit
        }).exec();

        // 查询未分类的工作区数量
        const uncatWorkspacesCount = await db.workspaces.find({
            selector: {cate_id: ""}
        }).exec().then((docs: string | any[]) => docs.length);
        categories.unshift({
            id: "-1",
            cate_name: "全部",
            ws_num: ws_num + uncatWorkspacesCount
        }, {
            id: "0",
            cate_name: "未分类",
            ws_num: uncatWorkspacesCount
        })

        const list = categories.map((category: { id: any; cate_name: any; ws_num: any; }) => ({
            cid: category.id,
            cname: category.cate_name,
            wnum: category.ws_num
        }));

        return {
            list,
            total,
            page_count: Math.ceil(total / req.page_size)
        };
    }

    /**
     * 删除分类
     */
    async delete(req: DeleteReq): Promise<void> {
        const db = await getDatabase();

        // 检查分类下是否有工作区
        const workspaces = await db.workspaces.find({
            selector: {cate_id: req.cid, del_at: 0}
        }).exec();

        if (workspaces.length > 0) {
            throw new Error('分类下存在工作区，无法删除');
        }

        // 删除分类
        await db.categories.find({
            selector: {id: req.cid}
        }).remove();

        // 批量更新分类下的工作区，假删除
        const currentTime = Math.floor(Date.now() / 1000)
        await db.workspaces.find({
            selector: {cate_id: req.cid}
        }).update({
            $set: {
                cate_id: "",
                del_at: currentTime,
                update_at: currentTime,
            }
        });
    }

    /**
     * 更新分类下的工作区数量
     */
    async updateWorkspaceCount(cid: string): Promise<void> {
        const db = await getDatabase();

        // 查询分类
        const category = await db.categories.findOne({
            selector: {id: cid}
        }).exec();

        if (!category) {
            return;
        }

        // 查询分类下的工作区数量
        const count = await db.workspaces.find({
            selector: {cate_id: cid, del_at: 0}
        }).exec().then((docs: string | any[]) => docs.length);

        // 更新数量
        await category.update({
            $set: {
                ws_num: count
            }
        });
    }
} 
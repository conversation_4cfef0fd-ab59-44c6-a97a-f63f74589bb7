import {Checkbox, List, message, Modal} from "antd";
import {useState} from "react";
import styled from "styled-components";
import {ProjectType} from "../response-type";
import {useWorkerData} from "./useRecyleData";
import {ConfirmModalWrapper} from "@/components/pop-window/confirm-window";
import {useHomeStore} from "@/store/home-store";
import {deleteProjectPermanently, restoreProject} from "@/api/project";
// 自定义弹窗样式组件
// 用于设置回收站主弹窗的整体样式
const CustomModal = styled(Modal)`
  display: flex;
  justify-content: center; // 水平居中
  align-items: center; // 垂直居中

  .ant-modal-content {
    background: #f1f2ff; // 设置浅蓝色背景
    padding: 0;
    display: flex;
    flex-direction: column; // 垂直排列子元素
    justify-content: center;
    align-items: center;
    overflow: hidden; // 隐藏溢出内容
  }

  // 弹窗头部样式
  .ant-modal-header {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f1f2ff; // 添加浅蓝色背景
    .ant-modal-title {
      font-size: 24px;
      font-weight: 300;
      color: var(--text-primary); // 使用主文本颜色变量
    }
  }

  // 弹窗主体内容区域样式
  .ant-modal-body {
    width: 674px;
    height: 420px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  // 弹窗底部区域样式
  .ant-modal-footer {
    width: 100%;
    height: 60px;
  }
`;

// 弹窗底部按钮容器样式
const Footer = styled.div`
  height: 100%;
  display: flex;
  justify-content: space-between; // 两端对齐
  align-items: center;
  background: var(--bg-category-tabs);
  padding: 0 100px;
  color: var(--text-primary);
  font-size: 19px;
  font-weight: 400;

  // 所有直接子元素的通用样式
  :nth-child(n) {
    width: 120px;
    height: 45px;
    border-radius: 22.5px; // 圆角按钮
    cursor: pointer;
    text-align: center;
    line-height: 45px;
  }

  // 子元素悬停效果
  :nth-child(n):hover {
    background: #cdd2e9; // 悬停时的背景色
    :nth-child(2) {
      border-bottom: 2px solid var(--text-primary);
    }
  }
`;

// 全局样式组件，主要用于自定义 Checkbox 样式
const GlobalStyle = styled.div`
  width: 100%;
  height: 100%;

  // 自定义圆形复选框样式
  .rounded-full .ant-checkbox-inner {
    border-radius: 50% !important;
    border-color: var(--text-primary) !important;
    transition: all 0.3s ease;
  }

  // 复选框悬停效果
  .rounded-full .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .list-item-container:hover ~ .rounded-full .ant-checkbox-inner,
  .ant-list-item:hover .rounded-full .ant-checkbox-inner {
    border-color: #3d56ba !important;
    border-width: 2px !important;
  }

  // 复选框选中状态样式
  .rounded-full .ant-checkbox-checked .ant-checkbox-inner {
    background-color: transparent !important; // 透明背景
    &::after {
      border: none !important;
      width: 10px !important;
      height: 10px !important;
      background: var(--text-primary) !important; // 使用主文本颜色作为选中标记
      border-radius: 50% !important; // 圆形选中标记
      transform: translate(-50%, -50%) !important; // 居中定位
      top: 50% !important;
      left: 50% !important;
    }
  }

  // 项目容器悬停效果
  .list-item-container {
    transition: all 0.3s ease;
  }

  // 通过列表项来控制悬停效果
  .ant-list-item:hover {
    .list-item-container {
      transform: scale(1.01);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 全选复选框样式
  .select-all-checkbox {
    .ant-checkbox-inner {
      width: 11px !important;
      height: 11px !important;
      border-radius: 4px !important;
      border-color: var(--text-primary) !important;
      transition: all 0.3s ease;

      &::after {
        border-color: #ff0000 !important;
      }
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: transparent !important;
      border-color: var(--text-primary) !important;
    }

    &:hover .ant-checkbox-inner {
      border-color: #3d56ba !important;
    }

    // 添加文字样式
    .ant-checkbox + span {
      color: var(--text-primary);
      font-size: 12px;
      padding-left: 8px;
    }
  }
`;

// 添加时间格式化辅助函数
const formatTimeAgo = (timestamp: number) => {
  const now = Date.now();
  const diffInSeconds = Math.floor((now - timestamp * 1000) / 1000);

  if (diffInSeconds < 60) {
    return "刚刚删除";
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前删除`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前删除`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前删除`;
  }
};

// 组件属性类型定义
type WorkerspaceRecycleBinDialogProps = {
  open: boolean; // 控制弹窗显示状态
  onClose: () => void; // 关闭弹窗的回调函数
};

export const ProjectRecycleBinDialog = ({
  open,
  onClose,
}: WorkerspaceRecycleBinDialogProps) => {
  // 控制确认弹窗的显示状态
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [confirmTitle, setConfirmTitle] = useState("");
  // 从自定义 Hook 获取回收站数据和操作方法
  useWorkerData();
  const {
    recycleBinList,
    completeDeleteProject,
    updateRecycleBinList,
    setIsRestore,
    restoreProjectSyncCategory,
  } = useHomeStore();
  // 已选中项目列表
  const [checkedList, setCheckedList] =
    useState<Array<ProjectType & { checked?: boolean }>>();
  // 操作类型：delete-删除，restore-恢复
  const [handleType, setHandleType] = useState<"delete" | "restore">("delete");

  // 确认操作处理函数
  const onConfirmHandle = async () => {
    if (!checkedList) return;
    const ids = checkedList.map((item) => item.wid);
    try {
      if (handleType === "delete") {
        // 执行永久删除操作
        completeDeleteProject(ids);
        const res = await deleteProjectPermanently(ids);
        if ((res as any).code === 0) {
          setConfirmOpen(false);
        }
      } else {
        // 执行恢复操作
        const res = await restoreProject({ wids: ids });
        if ((res as any).code === 0) {
          completeDeleteProject(ids);
          setConfirmOpen(false);
          setIsRestore(crypto.randomUUID());
          restoreProjectSyncCategory(ids.length);
        }
      }
    } catch (error) {
      message.error("删除失败");
    }
  };

  return (
    <CustomModal
      title="Recycle Bin"
      open={open}
      onCancel={onClose}
      footer={
        <Footer>
          {/* 还原按钮 */}
          <div
            className="flex flex-col items-center gap-0.5"
            onClick={() => {
              // 获取选中的项目列表
              const checkedList = recycleBinList.filter((item) => item.checked);
              if (checkedList.length > 0) {
                setHandleType("restore");
                setConfirmOpen(true);
                setConfirmTitle("Restore");
                setCheckedList(checkedList);
              } else {
                message.error("请先选择要还原的工作区");
              }
            }}
          >
            <div>还原</div>
            <div className="!w-[80%] !h-[2px]" />
          </div>
          {/* 永久删除按钮 */}
          <div
            className="flex flex-col items-center gap-0.5"
            onClick={() => {
              // 获取选中的项目列表
              const checkedList = recycleBinList.filter((item) => item.checked);
              if (checkedList.length > 0) {
                setConfirmOpen(true);
                setCheckedList(checkedList);
                setConfirmTitle("确定要永久删除吗？");
                setHandleType("delete");
              } else {
                message.error("请先选择要删除的工作区");
              }
            }}
          >
            <div>永久删除</div>
            <div className="!w-[80%] !h-[2px]" />
          </div>
        </Footer>
      }
    >
      {/* 列表内容区域：使用 GlobalStyle 包装以应用自定义样式 */}
      <GlobalStyle className="relative">
        {/* List 组件：展示回收站中的项目列表 */}
        <List
          className="w-full h-full px-12 scrollbar-custom" // 设置列表宽高和内边距，使用自定义滚动条样式
          bordered={false} // 移除列表边框
        >
          {/* 只有当回收站列表不为空时才渲染内容 */}
          {recycleBinList.length > 0 &&
            recycleBinList.map((item: any, index) => (
              <List.Item
                key={item.wid}
                className="w-full h-12 !p-0 mb-2.5" // 设置列表项高度和间距
                style={{ borderBottom: 0 }} // 移除底部边框
              >
                {/* 圆形复选框：用于选择项目 */}
                <Checkbox
                  className="rounded-full w-10 h-10 ml-2"
                  checked={item.checked}
                  onChange={() => {
                    updateRecycleBinList([item.wid]); // 更新选中状态
                  }}
                />
                {/* 项目内容容器 */}
                <div
                  className="flex-1 h-full leading-[48px] rounded-md bg-[var(--bg-category-tabs)] pl-2.5 pr-4 flex justify-between hover:border-[1px] hover:border-[var(--text-primary)] list-item-container -ml-2"
                  style={{
                    border: item.checked
                      ? "1px solid var(--text-primary)" // 选中时显示边框
                      : "",
                  }}
                  onClick={() => {
                    updateRecycleBinList([item.wid]); // 点击项目时更新选中状态
                  }}
                >
                  {/* 左侧：项目图标和标题 */}
                  <div className="h-full leading-[48px] flex items-center gap-2">
                    {/* 项目缩略图 */}
                    <div
                      className="w-10 h-10 rounded-md overflow-hidden"
                      style={{
                        backgroundImage: `url(${item.preview})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                      }}
                    />
                    {/* 项目标题 */}
                    <div className="text-[var(--text-primary)]">
                      {item.title}
                    </div>
                  </div>
                  {/* 右侧：删除时间信息 */}
                  <div className="h-full flex items-center text-[var(--text-primary)] opacity-60 text-[10px] tracking-[0.6px] -ml-[3px]">
                    {formatTimeAgo(item.del_at)}
                  </div>
                </div>
              </List.Item>
            ))}
        </List>
        {/* 全选按钮 */}
        <Checkbox
          className="absolute right-11 -top-10 select-all-checkbox"
          disabled={recycleBinList.length === 0}
          checked={
            recycleBinList.length > 0 &&
            checkedList?.length === recycleBinList.length
          }
          onChange={() => {
            setCheckedList(
              checkedList?.length === recycleBinList.length
                ? []
                : recycleBinList
            );
            updateRecycleBinList(
              recycleBinList.map((item) => item.wid) as Array<string | number>
            );
          }}
        >
          全选
        </Checkbox>
      </GlobalStyle>
      {confirmOpen && (
        <ConfirmModalWrapper
          title={confirmTitle}
          confirmOpen={confirmOpen}
          onClose={() => {
            setConfirmOpen(false);
          }}
          onConfirmHandle={onConfirmHandle}
          component={null}
          handleType={handleType}
        />
      )}
    </CustomModal>
  );
};

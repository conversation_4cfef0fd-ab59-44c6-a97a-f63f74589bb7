import { useCallback, useState } from 'react';
import { CustomNode, useFlowStore } from '@/store/flow-store';
import { XYPosition } from '@xyflow/react';

export interface HelperLine {
  id: string;
  position: number;
  orientation: 'horizontal' | 'vertical';
}

const SNAP_DISTANCE = 5; // 吸附距离阈值

export const useHelperLines = () => {
  const [helperLines, setHelperLines] = useState<HelperLine[]>([]);

  // 获取节点的边界坐标
  const getNodeBounds = useCallback((node: CustomNode) => {
    const width = node.measured?.width || 404;
    const height = node.measured?.height || 114;
    
    return {
      left: node.position.x,
      right: node.position.x + width,
      top: node.position.y,
      bottom: node.position.y + height,
      centerX: node.position.x + width / 2,
      centerY: node.position.y + height / 2,
      width,
      height,
    };
  }, []);

  // 计算辅助线和吸附位置
  const calculateHelperLines = useCallback((draggedNode: CustomNode) => {
    const nodes = useFlowStore.getState().nodes;
    const otherNodes = nodes.filter(node => node.id !== draggedNode.id);
    
    if (otherNodes.length === 0) {
      setHelperLines([]);
      return draggedNode.position;
    }

    const draggedBounds = getNodeBounds(draggedNode);
    const lines: HelperLine[] = [];
    let snappedPosition: XYPosition = { ...draggedNode.position };
    let snappedX = false;
    let snappedY = false;

    // 检查每个其他节点
    otherNodes.forEach((node) => {
      const nodeBounds = getNodeBounds(node);
      
      // 垂直对齐线 (X轴)
      if (!snappedX) {
        // 左边对齐
        if (Math.abs(draggedBounds.left - nodeBounds.left) < SNAP_DISTANCE) {
          snappedPosition.x = nodeBounds.left;
          lines.push({
            id: `vertical-left-${node.id}`,
            position: nodeBounds.left,
            orientation: 'vertical',
          });
          snappedX = true;
        }
        // 右边对齐
        else if (Math.abs(draggedBounds.right - nodeBounds.right) < SNAP_DISTANCE) {
          snappedPosition.x = nodeBounds.right - draggedBounds.width;
          lines.push({
            id: `vertical-right-${node.id}`,
            position: nodeBounds.right,
            orientation: 'vertical',
          });
          snappedX = true;
        }
        // 中心对齐
        else if (Math.abs(draggedBounds.centerX - nodeBounds.centerX) < SNAP_DISTANCE) {
          snappedPosition.x = nodeBounds.centerX - draggedBounds.width / 2;
          lines.push({
            id: `vertical-center-${node.id}`,
            position: nodeBounds.centerX,
            orientation: 'vertical',
          });
          snappedX = true;
        }
        // 左边与右边对齐
        else if (Math.abs(draggedBounds.left - nodeBounds.right) < SNAP_DISTANCE) {
          snappedPosition.x = nodeBounds.right;
          lines.push({
            id: `vertical-left-right-${node.id}`,
            position: nodeBounds.right,
            orientation: 'vertical',
          });
          snappedX = true;
        }
        // 右边与左边对齐
        else if (Math.abs(draggedBounds.right - nodeBounds.left) < SNAP_DISTANCE) {
          snappedPosition.x = nodeBounds.left - draggedBounds.width;
          lines.push({
            id: `vertical-right-left-${node.id}`,
            position: nodeBounds.left,
            orientation: 'vertical',
          });
          snappedX = true;
        }
      }

      // 水平对齐线 (Y轴)
      if (!snappedY) {
        // 顶部对齐
        if (Math.abs(draggedBounds.top - nodeBounds.top) < SNAP_DISTANCE) {
          snappedPosition.y = nodeBounds.top;
          lines.push({
            id: `horizontal-top-${node.id}`,
            position: nodeBounds.top,
            orientation: 'horizontal',
          });
          snappedY = true;
        }
        // 底部对齐
        else if (Math.abs(draggedBounds.bottom - nodeBounds.bottom) < SNAP_DISTANCE) {
          snappedPosition.y = nodeBounds.bottom - draggedBounds.height;
          lines.push({
            id: `horizontal-bottom-${node.id}`,
            position: nodeBounds.bottom,
            orientation: 'horizontal',
          });
          snappedY = true;
        }
        // 中心对齐
        else if (Math.abs(draggedBounds.centerY - nodeBounds.centerY) < SNAP_DISTANCE) {
          snappedPosition.y = nodeBounds.centerY - draggedBounds.height / 2;
          lines.push({
            id: `horizontal-center-${node.id}`,
            position: nodeBounds.centerY,
            orientation: 'horizontal',
          });
          snappedY = true;
        }
        // 顶部与底部对齐
        else if (Math.abs(draggedBounds.top - nodeBounds.bottom) < SNAP_DISTANCE) {
          snappedPosition.y = nodeBounds.bottom;
          lines.push({
            id: `horizontal-top-bottom-${node.id}`,
            position: nodeBounds.bottom,
            orientation: 'horizontal',
          });
          snappedY = true;
        }
        // 底部与顶部对齐
        else if (Math.abs(draggedBounds.bottom - nodeBounds.top) < SNAP_DISTANCE) {
          snappedPosition.y = nodeBounds.top - draggedBounds.height;
          lines.push({
            id: `horizontal-bottom-top-${node.id}`,
            position: nodeBounds.top,
            orientation: 'horizontal',
          });
          snappedY = true;
        }
      }
    });

    setHelperLines(lines);
    return snappedPosition;
  }, [getNodeBounds]);

  // 清除辅助线
  const clearHelperLines = useCallback(() => {
    setHelperLines([]);
  }, []);

  return {
    helperLines,
    calculateHelperLines,
    clearHelperLines,
  };
}; 
# PDF Flow Space

PDF Flow Space 是一个现代化的 PDF 文档处理和可视化工具，它提供了流程图编辑、PDF 阅读和标注等功能的综合解决方案。

## 🌟 主要特性

- 📝 PDF 文档查看和标注
- 🔄 流程图编辑和可视化
- 📊 Markdown 支持
- 🎨 富文本编辑器
- 🖼️ 可拖拽的界面布局
- 🌈 自定义主题和样式

## 🛠️ 技术栈

- **前端框架**: React 18
- **类型系统**: TypeScript
- **路由**: React Router 6
- **状态管理**: Zustand
- **UI 组件**: 
  - Ant Design
  - React Flow
  - React PDF
  - React Quill
- **样式**: 
  - TailwindCSS
  - SCSS
  - Styled Components
- **构建工具**: Vite

## 📦 安装

```bash
# 使用 pnpm 安装依赖
pnpm install
```

## 🚀 开发

```bash
# 启动开发服务器
pnpm dev
```

## 📦 构建

```bash
# 构建生产版本
pnpm build
```

## 🔍 预览

```bash
# 预览构建版本
pnpm preview
```

## 🌐 浏览器支持

- 生产环境
  - 市场份额大于 0.2%
  - 非过时浏览器
  - 非 Opera Mini

- 开发环境
  - 最新版本的 Chrome
  - 最新版本的 Firefox
  - 最新版本的 Safari

## 📄 许可证

[MIT](LICENSE)

// chat请求参数类型
export type SessionListType = {
  wid: number | string
  page?: number | string
  page_size?: number | string
}

export type SessionDetailType = {
  sid: string | number
  rid: string | number  // 轮次id
  page_size: string | number
}

export type UpdateSessionType = {
  sid: string | number
  title: string
}
export type DeleteSessionType = {
  sid: string | number
}

export type ConversationType = {
  wid: string
  sid: string
  Refs?: {             // 引用
    type: number      // 1-pdf 2-node
    path: string
    nid: string
    title: string     // 文件名
    content: string   // 引用内容
  }[]
  question: string    // 问题
  rid?: number  // 轮次id
}

export type CreateSessionType = {
  wid: string | number
  question: string // <255
}


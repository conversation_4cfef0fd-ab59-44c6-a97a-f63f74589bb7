import { useState, useEffect } from "react";
import type { SessionType } from "../types";

export const useSessionGroups = (sessions: SessionType[]) => {
  const [newSessions, setNewSessions] = useState<Array<{
    dateTitle: string | number;
    sessions: SessionType[];
  }>>([]);

  const onJudgeDate = (session: SessionType, newSessions: Array<{
    dateTitle: string | number;
    sessions: SessionType[];
  }>) => {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();

    const sessionDate = new Date(session.datetime * 1000);
    const sessionYear = sessionDate.getFullYear();
    const sessionMonth = sessionDate.getMonth() + 1;
    const sessionDay = sessionDate.getDate();

    if (sessionYear === year) {
      if (sessionMonth === month) {
        if (sessionDay === day) {
          addToGroup("今天", session, newSessions);
        } else if (sessionDay === day - 1) {
          addToGroup("昨天", session, newSessions);
        } else if (sessionDay >= day - 7) {
          addToGroup("本周", session, newSessions);
        } else {
          addToGroup("本月", session, newSessions);
        }
      } else {
        addToGroup(`${month}月`, session, newSessions);
      }
    } else {
      addToGroup(sessionYear, session, newSessions);
    }
  };

  const addToGroup = (dateTitle: string | number, session: SessionType, groups: Array<{
    dateTitle: string | number;
    sessions: SessionType[];
  }>) => {
    const existingGroup = groups.find(item => item.dateTitle === dateTitle);
    if (existingGroup) {
      existingGroup.sessions.push(session);
    } else {
      groups.push({
        dateTitle,
        sessions: [session]
      });
    }
  };

  useEffect(() => {
    if (sessions && sessions.length > 0) {
      const tempSessions: Array<{
        dateTitle: string | number;
        sessions: SessionType[];
      }> = [];

      const sortedSessions = [...sessions].sort((a, b) => b.datetime - a.datetime);

      sortedSessions.forEach(session => {
        onJudgeDate(session, tempSessions);
      });

      setNewSessions(tempSessions);
    } else {
      setNewSessions([])
    }
  }, [sessions]);

  return { newSessions };
}; 
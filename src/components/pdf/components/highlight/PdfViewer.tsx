import {usePdfStore} from "@/store/pdf-store.ts";
import {PdfHighlighter, PdfLoader, PdfSelection} from "@/components/pdf/components/highlight/src";
import {HighlightContainer} from "@/components/pdf/components/highlight/HighlightContainer.tsx";
import {PdfZoom} from "@/components/pdf/components/highlight/PdfZoom.tsx";
import {PdfPage} from "@/components/pdf/components/highlight/PdfPage.tsx";
import {usePdfSelection} from "@/components/pdf/hooks/pdf-selection.tsx";

type PdfViewerProps = {
    aid: string
}
export const PdfViewer = ({aid}: PdfViewerProps) => {
    // pdf信息
    const {url, scale, highlights, setPdfHighlighterUtils, selectionColor} = usePdfStore((state) => ({
        url: state.pdfs.get(aid)?.url || "",
        scale: state.pdfs.get(aid)?.scale || 1,
        highlights: state.pdfs.get(aid)?.highlights || [],
        setPdfHighlighterUtils: state.setPdfHighlighterUtils,
        selectionColor: state.selectionColor
    }));
    const {onSelection} = usePdfSelection()
    return (
        <div className="w-full h-full">
            <PdfLoader document={url}>
                {(pdfDocument) => {
                    return (
                        <div className="w-full">
                            <PdfHighlighter
                                enableAreaSelection={(event) => event.altKey}
                                mouseSelectionStyle={{backgroundColor: selectionColor}}
                                pdfDocument={pdfDocument}
                                utilsRef={(_pdfHighlighterUtils) => {
                                    setPdfHighlighterUtils(aid, _pdfHighlighterUtils)
                                }}
                                pdfScaleValue={scale}
                                textSelectionColor={selectionColor}
                                highlights={highlights}
                                onSelection={(selection: PdfSelection) => onSelection(aid, selection.makeGhostHighlight())}>
                                <HighlightContainer/>
                            </PdfHighlighter>
                            <PdfZoom aid={aid}/>
                            <PdfPage aid={aid} totalPages={pdfDocument.numPages}/>
                        </div>
                    )
                }}
            </PdfLoader>
        </div>
    )
}
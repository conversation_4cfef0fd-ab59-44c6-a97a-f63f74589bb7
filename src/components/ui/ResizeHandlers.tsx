import React from 'react';

// 定义右侧边缘调整处理器接口
interface EdgeResizeHandlerProps {
    // 调整组件的尺寸设置函数
    setSize: (updater: (prev: any) => any) => void;
    // 当前尺寸属性
    currentSize: {
        width: number;
        height: number;
    };
    // 位置信息，用于计算最大限制
    position?: {
        x: number;
        y: number;
    };
    // 最小尺寸限制
    minSize?: {
        width: number;
        height: number;
    };
    // 调整位置的函数（可选）
    setPosition?: (updater: (prev: any) => any) => void;
    // 指定调整的面板类名，用于寻找要触发的原始处理器
    panelClassName: string;
    // 是否进行原始事件委派而不是自定义逻辑
    useEventDelegation?: boolean;
    // 调整开始回调函数
    onResizeStart?: () => void;
    // 调整结束回调函数
    onResizeStop?: () => void;
}

// 右侧边缘处理器 - 只调整宽度
export const RightEdgeHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                       setSize,
                                                                       currentSize,
                                                                       position,
                                                                       setPosition,
                                                                       minSize = {width: 300, height: 300},
                                                                       useEventDelegation = false,
                                                                       panelClassName,
                                                                       onResizeStart,
                                                                       onResizeStop
                                                                   }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();

        if (useEventDelegation) {
            // 使用事件委派，触发原始的resize处理器
            const currentPanel = e.currentTarget.closest(`.${panelClassName}`);
            if (currentPanel) {
                const resizeHandle = currentPanel.querySelector('.react-resizable-handle-se');
                if (resizeHandle) {
                    const event = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        clientX: e.clientX,
                        clientY: e.clientY,
                    });
                    resizeHandle.dispatchEvent(event);
                }
            }
            return;
        }

        // 自定义调整逻辑
        const startX = e.clientX;
        const startWidth = currentSize.width;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaX = moveEvent.clientX - startX;
            const newWidth = startWidth + deltaX;

            // // 添加最小和最大宽度限制
            const minWidth = minSize.width;
            const maxWidth = position ? window.innerWidth - position.x : window.innerWidth;

            // 应用新宽度和位置（带限制）
            if (newWidth >= minWidth) {
                setPosition(prev => ({
                    ...prev,
                    width: newWidth
                }));
                setSize(prev => ({
                    ...prev,
                    width: Math.max(minWidth, Math.min(maxWidth, newWidth))
                }));
            }
        };

        // 添加鼠标移动事件监听
        document.addEventListener('mousemove', handleResize);

        // 清理事件监听
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', handleMouseUp);
            if (onResizeStop) onResizeStop();
        };
        document.addEventListener('mouseup', handleMouseUp);

        // 阻止默认事件和冒泡
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-e" onMouseDown={handleMouseDown}/>
    );
};

// 底部边缘处理器 - 只调整高度
export const BottomEdgeHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                        setSize,
                                                                        currentSize,
                                                                        position,
                                                                        setPosition,
                                                                        minSize = {width: 300, height: 300},
                                                                        useEventDelegation = false,
                                                                        panelClassName,
                                                                        onResizeStart,
                                                                        onResizeStop
                                                                    }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();

        if (useEventDelegation) {
            // 使用事件委派，触发原始的resize处理器
            const currentPanel = e.currentTarget.closest(`.${panelClassName}`);
            if (currentPanel) {
                const resizeHandle = currentPanel.querySelector('.react-resizable-handle-se');
                if (resizeHandle) {
                    const event = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        clientX: e.clientX,
                        clientY: e.clientY,
                    });
                    resizeHandle.dispatchEvent(event);
                }
            }
            return;
        }

        // 自定义调整逻辑
        const startY = e.clientY;
        const startHeight = currentSize.height;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaY = moveEvent.clientY - startY;
            const newHeight = startHeight + deltaY;

            // 添加最小和最大高度限制
            const minHeight = minSize.height;
            const maxHeight = position ? window.innerHeight - position.y : window.innerHeight;

            // 应用新高度（带限制）
            setPosition(prev => ({
                ...prev,
                height: Math.max(minHeight, Math.min(maxHeight, newHeight))
            }));
            setSize(prev => ({
                ...prev,
                height: Math.max(minHeight, Math.min(maxHeight, newHeight))
            }));
        };

        // 添加鼠标移动事件监听
        document.addEventListener('mousemove', handleResize);

        // 清理事件监听
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', handleMouseUp);
            if (onResizeStop) onResizeStop();
        };
        document.addEventListener('mouseup', handleMouseUp);

        // 阻止默认事件和冒泡
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-s" onMouseDown={handleMouseDown}/>
    );
};

// 左侧边缘处理器 - 只调整宽度并同时更新位置
export const LeftEdgeHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                      setSize,
                                                                      currentSize,
                                                                      position,
                                                                      setPosition,
                                                                      minSize = {width: 300, height: 300},
                                                                      useEventDelegation = false,
                                                                      panelClassName,
                                                                      onResizeStart,
                                                                      onResizeStop
                                                                  }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();

        if (useEventDelegation) {
            // 使用事件委派，触发原始的resize处理器
            const currentPanel = e.currentTarget.closest(`.${panelClassName}`);
            if (currentPanel) {
                const resizeHandle = currentPanel.querySelector('.react-resizable-handle-sw');
                if (resizeHandle) {
                    const event = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        clientX: e.clientX,
                        clientY: e.clientY,
                    });
                    resizeHandle.dispatchEvent(event);
                }
            }
            return;
        }

        if (!position || !setPosition) {
            console.error("Position and setPosition are required for LeftEdgeHandler");
            return;
        }

        // 自定义调整逻辑
        const startX = e.clientX;
        const startWidth = currentSize.width;
        const startLeft = position.x;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaX = moveEvent.clientX - startX;

            // 计算新的宽度和位置
            const newWidth = startWidth - deltaX;
            const newLeft = startLeft + deltaX;

            // 添加最小宽度限制
            const minWidth = minSize.width;

            // 应用新宽度和位置（带限制）
            if (newWidth >= minWidth) {
                setPosition(prev => ({
                    ...prev,
                    x: newLeft,
                    width: Math.min(window.innerWidth - newLeft, newWidth)
                }));

                setSize(prev => ({
                    ...prev,
                    width: Math.min(window.innerWidth - newLeft, newWidth)
                }));
            }
        };

        // 添加鼠标移动事件监听
        document.addEventListener('mousemove', handleResize);

        // 清理事件监听
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', handleMouseUp);
            if (onResizeStop) onResizeStop();
        };
        document.addEventListener('mouseup', handleMouseUp);

        // 阻止默认事件和冒泡
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-w" onMouseDown={handleMouseDown}/>
    );
};

// 顶部边缘处理器 - 只调整高度并更新位置
export const TopEdgeHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                     setSize,
                                                                     currentSize,
                                                                     position,
                                                                     setPosition,
                                                                     minSize = {width: 300, height: 300},
                                                                     useEventDelegation = false,
                                                                     panelClassName,
                                                                     onResizeStart,
                                                                     onResizeStop
                                                                 }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();

        if (useEventDelegation) {
            // 使用事件委派，触发原始的resize处理器
            const currentPanel = e.currentTarget.closest(`.${panelClassName}`);
            if (currentPanel) {
                const resizeHandle = currentPanel.querySelector('.react-resizable-handle-nw');
                if (resizeHandle) {
                    const event = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        clientX: e.clientX,
                        clientY: e.clientY,
                    });
                    resizeHandle.dispatchEvent(event);
                }
            }
            return;
        }

        if (!position || !setPosition) {
            console.error("Position and setPosition are required for TopEdgeHandler");
            return;
        }

        // 自定义调整逻辑
        const startY = e.clientY;
        const startHeight = currentSize.height;
        const startTop = position.y;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaY = moveEvent.clientY - startY;

            // 计算新的高度和位置
            const newHeight = startHeight - deltaY;
            const newTop = startTop + deltaY;

            // 添加最小高度限制
            const minHeight = minSize.height;

            // 应用新高度和位置（带限制）
            if (newHeight >= minHeight) {
                setPosition(prev => ({
                    ...prev,
                    y: newTop
                }));

                setSize(prev => ({
                    ...prev,
                    height: Math.min(window.innerHeight - newTop, newHeight)
                }));
            }
        };

        // 添加鼠标移动事件监听
        document.addEventListener('mousemove', handleResize);

        // 清理事件监听
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', handleMouseUp);
            if (onResizeStop) onResizeStop();
        };
        document.addEventListener('mouseup', handleMouseUp);

        // 阻止默认事件和冒泡
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-n" onMouseDown={handleMouseDown}/>
    );
};

// 右下角处理器 - 同时调整宽度和高度
export const BottomRightCornerHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                               setSize,
                                                                               currentSize,
                                                                               position,
                                                                               minSize = {width: 300, height: 300},
                                                                               onResizeStart,
                                                                               onResizeStop
                                                                           }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();

        // 自定义调整逻辑
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = currentSize.width;
        const startHeight = currentSize.height;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaX = moveEvent.clientX - startX;
            const deltaY = moveEvent.clientY - startY;

            const newWidth = startWidth + deltaX;
            const newHeight = startHeight + deltaY;

            // 计算最大限制
            const maxWidth = position ? window.innerWidth - position.x : window.innerWidth;
            const maxHeight = position ? window.innerHeight - position.y : window.innerHeight;

            // 应用新尺寸（带限制）
            setSize(prev => ({
                width: Math.max(minSize.width, Math.min(maxWidth, newWidth)),
                height: Math.max(minSize.height, Math.min(maxHeight, newHeight))
            }));
        };

        document.addEventListener('mousemove', handleResize);

        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', handleMouseUp);
            if (onResizeStop) onResizeStop();
        };

        document.addEventListener('mouseup', handleMouseUp, {once: true});

        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-se" onMouseDown={handleMouseDown}/>
    );
};

// 左下角处理器 - 同时调整宽度和位置以及高度
export const BottomLeftCornerHandler: React.FC<EdgeResizeHandlerProps> = ({
                                                                              setSize,
                                                                              currentSize,
                                                                              position,
                                                                              setPosition,
                                                                              minSize = {width: 300, height: 300},
                                                                              onResizeStart,
                                                                              onResizeStop
                                                                          }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
        if (onResizeStart) onResizeStart();
        if (!position || !setPosition) {
            console.error("Position and setPosition are required for BottomLeftCornerHandler");
            return;
        }

        // 自定义调整逻辑
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = currentSize.width;
        const startHeight = currentSize.height;
        const startLeft = position.x;

        const handleResize = (moveEvent: MouseEvent) => {
            const deltaX = moveEvent.clientX - startX;
            const deltaY = moveEvent.clientY - startY;

            // 计算新的宽度、位置和高度
            const newWidth = startWidth - deltaX;
            const newLeft = startLeft + deltaX;
            const newHeight = startHeight + deltaY;

            // 应用新的尺寸和位置（带限制）
            if (newWidth >= minSize.width) {
                setPosition(prev => ({
                    ...prev,
                    x: newLeft
                }));

                setSize(prev => ({
                    width: Math.min(window.innerWidth - newLeft, newWidth),
                    height: Math.max(minSize.height, Math.min(window.innerHeight - position.y, newHeight))
                }));
            }
        };

        document.addEventListener('mousemove', handleResize);

        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleResize);
            if (onResizeStop) onResizeStop();
        };

        document.addEventListener('mouseup', handleMouseUp, {once: true});

        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div className="custom-handle-sw" onMouseDown={handleMouseDown}/>
    );
};

// 边缘调整处理器组件，封装所有边缘和角落的处理器
interface ResizeHandlersProps {
    // 面板类名
    panelClassName: string;
    // 调整大小的函数
    setSize: (updater: (prev: any) => any) => void;
    // 当前尺寸
    currentSize: {
        width: number;
        height: number;
    };
    // 当前位置
    position: {
        x: number;
        y: number;
    };
    // 设置位置的函数
    setPosition: (updater: (prev: any) => any) => void;
    // 最小尺寸限制
    minSize?: {
        width: number;
        height: number;
    };
    // 右侧边缘是否使用自定义逻辑
    customRightEdge?: boolean;
    // 底部边缘是否使用自定义逻辑
    customBottomEdge?: boolean;
    // 左侧边缘是否使用自定义逻辑
    customLeftEdge?: boolean;
    // 顶部边缘是否使用自定义逻辑
    customTopEdge?: boolean;
    // 调整开始回调函数
    onResizeStart?: () => void;
    // 调整结束回调函数
    onResizeStop?: () => void;
    showRightEdge?: boolean;
    showBottomEdge?: boolean;
    showLeftEdge?: boolean;
    showTopEdge?: boolean;
}

// 导出完整的边缘调整处理器组件
export const ResizeHandlers: React.FC<ResizeHandlersProps> = ({
                                                                  panelClassName,
                                                                  setSize,
                                                                  currentSize,
                                                                  position,
                                                                  setPosition,
                                                                  minSize = {width: 300, height: 300},
                                                                  customRightEdge = true,
                                                                  customBottomEdge = true,
                                                                  customLeftEdge = true,
                                                                  customTopEdge = true,
                                                                  showRightEdge = true,
                                                                  showLeftEdge = true,
                                                                  showBottomEdge = true,
                                                                  showTopEdge = true,
                                                                  onResizeStart,
                                                                  onResizeStop
                                                              }) => {
    console.log("resizeHandlers", showRightEdge, showLeftEdge, showBottomEdge, showTopEdge)
    return (
        <>
            {showRightEdge && <RightEdgeHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                setPosition={setPosition}
                minSize={minSize}
                useEventDelegation={!customRightEdge}
                panelClassName={panelClassName}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
            />}

            {showBottomEdge && <BottomEdgeHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                setPosition={setPosition}
                minSize={minSize}
                useEventDelegation={!customBottomEdge}
                panelClassName={panelClassName}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
            />}

            {showLeftEdge && <LeftEdgeHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                setPosition={setPosition}
                minSize={minSize}
                useEventDelegation={!customLeftEdge}
                panelClassName={panelClassName}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
            />}

            {showTopEdge && <TopEdgeHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                setPosition={setPosition}
                minSize={minSize}
                useEventDelegation={!customTopEdge}
                panelClassName={panelClassName}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
            />}

            <BottomRightCornerHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                minSize={minSize}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
                panelClassName={panelClassName}
            />

            <BottomLeftCornerHandler
                setSize={setSize}
                currentSize={currentSize}
                position={position}
                setPosition={setPosition}
                minSize={minSize}
                onResizeStart={onResizeStart}
                onResizeStop={onResizeStop}
                panelClassName={panelClassName}
            />
        </>
    );
}; 
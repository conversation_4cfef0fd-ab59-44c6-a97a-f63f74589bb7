import { message } from "antd";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { deleteSession, updateSession, getSessionDetail } from "@/api/chat";
export const useSessionActions = () => {
  const { sessions, session_detail_list, setSessionId, setChatList, setFooterRefs, removeSession, cleanSession, addSessionDetail, removeSessionDetail } = useChatStore();

  const handleModifyTitle = async (sid: string, title: string) => {
    try {
      const res = await updateSession({ sid, title });
      if ((res as any)?.code === 0) {
        //
        return res
      }
    } catch (error) {
      console.error(error);
    }
  };
  const handleDelete = async (sid: string) => {
    try {
      if (sessions.length > 1) {
        const deleteSessionIndex = sessions.findIndex((session) => session.sid === sid);
        if (deleteSessionIndex === sessions.length - 1) {
          const currentSessionDetail = session_detail_list.find((session_detail) => session_detail.sid === sessions[deleteSessionIndex - 1].sid)
          if (currentSessionDetail) {
            setChatList(currentSessionDetail.chatList)
          } else {
            const res = await getSessionDetail({
              sid: sessions[deleteSessionIndex - 1].sid,
              rid: 0,
              page_size: 10
            })
            if ((res as any).code === 0) {
              const chatList = (res as any).data.chat ? (res as any).data.chat : []
              setChatList(chatList)
              addSessionDetail({
                sid,
                chatList: chatList
              })
            }
          }
        } else {
          const currentSessionDetail = session_detail_list.find((session_detail) => session_detail.sid === sessions[deleteSessionIndex + 1].sid)
          if (currentSessionDetail) {
            setChatList(currentSessionDetail.chatList)
          } else {
            const res = await getSessionDetail({
              sid: sessions[deleteSessionIndex + 1].sid,
              rid: 0,
              page_size: 10
            })
            if ((res as any).code === 0) {
              const chatList = (res as any).data.chat ? (res as any).data.chat : []
              setChatList(chatList)
              addSessionDetail({
                sid,
                chatList: chatList
              })
            }
          }
        }
      } else {
        setChatList([])
      }
      const res = await deleteSession({ sid });
      if ((res as any).code === 0) {
        message.success("删除成功");
        if (sessions.length === 1) {

          removeSession(sid)
          removeSessionDetail(sid)
          cleanSession();
        } else {
          removeSession(sid)
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleNewSession = () => {
    setSessionId(0);
    setFooterRefs([]);
  };

  return { handleModifyTitle, handleDelete, handleNewSession };
}; 
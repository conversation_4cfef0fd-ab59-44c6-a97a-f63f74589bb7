import { Form, Input, Button } from "antd";
import { useFlowStore } from "@/store/flow-store.ts";
import useNodeEdit from "@/components/flow/hooks/node-edit.ts";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import Vditor from "vditor";
import "vditor/dist/index.css";
import { Rnd } from 'react-rnd';
import { CloseOutlined } from '@ant-design/icons';
import { useEditNodeStore } from '@/store/workerspace-store/edit-node-store';
import { FlowEditor } from "@/components/lexicalEditor/App";

export const EditNode = () => {
    // 所有 hooks 必须在条件判断之前调用
    const [updateKey, setUpdateKey] = useState(0)
    const { isOpen, highlight, closeEditNode } = useEditNodeStore();
    const { onNodeEdit } = useNodeEdit()
    const [form] = Form.useForm();
    const [vd, setVd] = useState<Vditor>()
    // const [windowSize, setWindowSize] = useState({ width: 600, height: 650 })

    useEffect(() => {
        return () => {
            form?.resetFields()
        }
    }, [isOpen])
    // 处理 ESC 键关闭
    useEffect(() => {
        console.log('isOpen', isOpen)
        if (!isOpen) return;

        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                closeEditNode();
            }
        };

        document.addEventListener('keydown', handleEsc);
        return () => {
            document.removeEventListener('keydown', handleEsc);
        };
    }, [isOpen, closeEditNode]);

    useEffect(() => {
        if (!isOpen || !highlight) return;

        let nid;
        if (typeof highlight === "string") {
            nid = highlight
        } else {
            nid = highlight.nid
        }

        const editNode = useFlowStore.getState().nodes.find(node => node.id === nid)
        if (!editNode) return;

        // 延迟初始化，确保容器已渲染完成
        setTimeout(() => {
            const container = document.getElementById("modal-vditor");
            if (!container) return;

            // 动态计算可用高度
            const windowElement = container.closest('.edit-node-window');
            if (windowElement) {
                const windowHeight = windowElement.clientHeight;
                const headerHeight = windowElement.querySelector('.edit-node-drag-handle')?.clientHeight || 60;
                const footerHeight = 60; // 底部按钮区域
                const formPadding = 40; // 表单内边距
                const titleFieldHeight = 80; // 标题字段高度
                const labelHeight = 30; // 内容标签高度

                const availableHeight = windowHeight - headerHeight - footerHeight - formPadding - titleFieldHeight - labelHeight;
                const finalHeight = Math.max(300, availableHeight); // 确保最小高度300px

                const vditor = new Vditor("modal-vditor", {
                    after: () => {
                        vditor.setValue(editNode.data.content);
                        setVd(vditor);
                    },
                    esc: (value) => {
                        console.log("esc", value)
                    },
                    fullscreen: {
                        index: 1500
                    },
                    preview: {
                        maxWidth: 1200
                    },
                    height: finalHeight,
                    mode: "wysiwyg",
                    resize: {
                        enable: true,
                    },
                });
            }
        }, 100);
        return () => {
            vd?.destroy();
            setVd(undefined);
        };
    }, [isOpen, highlight]);

    // 条件渲染放在所有 hooks 之后
    if (!isOpen || !highlight) return null;

    let nid
    if (typeof highlight === "string") {
        nid = highlight
    } else {
        nid = highlight.nid
    }
    const editNode = useFlowStore.getState().nodes.find(node => node.id === nid)
    if (!editNode) return null

    const handleOk = async () => {
        const data = form.getFieldsValue()
        await onNodeEdit({ id: nid, title: data.title, content: data?.content || editNode.data.content })
        closeEditNode()
        setUpdateKey(Math.random())
    };

    const handleCancel = () => {
        closeEditNode();
        setUpdateKey(Math.random())
    };

    // 使用 Portal 渲染到 document.body，无遮罩层
    const editWindow = (
        <Rnd
            default={{
                x: window.innerWidth / 2 - 300, // 居中显示
                y: window.innerHeight / 2 - 325,
                width: 600,
                height: 650,
            }}
            minWidth={400}
            minHeight={500}
            bounds="window"
            dragHandleClassName="edit-node-drag-handle"
            style={{
                zIndex: 100000, // 确保在最顶层
                position: 'fixed', // 使用 fixed 定位
            }}
        >
            <div
                className="edit-node-window h-full flex flex-col"
                // 只在特定事件上阻止冒泡，不影响拖拽
                onClick={(e) => e.stopPropagation()}
                onDoubleClick={(e) => e.stopPropagation()}
            >
                {/* 标题栏 - 可拖拽区域 */}
                <div className="edit-node-drag-handle px-4 py-3 rounded-t-lg cursor-move flex justify-between items-center select-none">
                    <h3 className="text-lg font-medium text-gray-900">编辑节点</h3>
                    <Button
                        type="text"
                        icon={<CloseOutlined />}
                        onClick={handleCancel}
                        className="hover:bg-gray-200"
                        size="small"
                    />
                </div>

                {/* 内容区域 */}
                <div className="flex-1 p-4 overflow-hidden flex flex-col">
                    {isOpen && <Form form={form} preserve={false}
                        initialValues={{ title: editNode?.data.title, content: editNode?.data.content }}
                        className="h-full flex flex-col">
                        <Form.Item label="标题" name="title" className="mb-4">
                            <Input
                                placeholder="请输入标题"
                                autoFocus
                            />
                        </Form.Item>
                        <Form.Item label='内容' name="content" className="flex-1 flex flex-col hide-scrollbar"
                            style={{
                                boxSizing: 'border-box',
                                overflow: 'auto',
                            }}
                        >
                            {/* <div
                                id="modal-vditor"
                                className="vditor flex-1 min-h-0"
                            ></div> */}
                            {isOpen && <FlowEditor key={updateKey} />}
                        </Form.Item>
                    </Form>}
                </div>

                {/* 底部按钮区域 */}
                <div className="border-t border-gray-200 px-4 py-3 bg-gray-50 rounded-b-lg flex justify-end space-x-2">
                    <Button onClick={handleCancel}>取消</Button>
                    <Button type="primary" onClick={handleOk}>确定</Button>
                </div>
            </div>
        </Rnd>
    );

    if (!isOpen) return null;
    // 使用 Portal 将组件渲染到 document.body
    return createPortal(editWindow, document.body);
}
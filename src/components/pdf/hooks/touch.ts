import { useEffect, useRef, useCallback } from 'react';

interface TouchPoint {
  x: number;
  y: number;
}

interface TouchHookOptions {
  minScale?: number;
  maxScale?: number;
  sensitivity?: number;
  onScale?: (scale: number) => void;
  onScaleStart?: () => void;
  onScaleEnd?: () => void;
}

const getDistance = (touch1: TouchPoint, touch2: TouchPoint): number => {
  const dx = touch2.x - touch1.x;
  const dy = touch2.y - touch1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

const getTouchPoint = (touch: Touch): TouchPoint => ({
  x: touch.clientX,
  y: touch.clientY,
});

export const usePinchZoom = (
  targetRef: React.RefObject<HTMLElement | null>,
  options: TouchHookOptions = {}
) => {
  const {
    minScale = 0.5,
    maxScale = 3.0,
    sensitivity = 0.002,
    onScale,
    onScaleStart,
    onScaleEnd
  } = options;

  const lastDistance = useRef<number | null>(null);
  const isZooming = useRef(false);
  const currentScale = useRef(1);
  const startScale = useRef(1);

  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (event.touches.length === 2) {
      event.preventDefault();
      
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      lastDistance.current = getDistance(touch1, touch2);
      startScale.current = currentScale.current;
      isZooming.current = true;
      
      onScaleStart?.();
    }
  }, [onScaleStart]);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (event.touches.length === 2 && isZooming.current && lastDistance.current !== null) {
      event.preventDefault();
      
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      const currentDistance = getDistance(touch1, touch2);
      
      const distanceChange = currentDistance - lastDistance.current;
      const scaleChange = distanceChange * sensitivity;
      const newScale = Math.max(minScale, Math.min(maxScale, startScale.current + scaleChange));
      
      if (newScale !== currentScale.current) {
        currentScale.current = newScale;
        onScale?.(newScale);
      }
    }
  }, [sensitivity, minScale, maxScale, onScale]);

  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (event.touches.length < 2) {
      lastDistance.current = null;
      if (isZooming.current) {
        isZooming.current = false;
        onScaleEnd?.();
      }
    }
  }, [onScaleEnd]);

  const handleTouchCancel = useCallback(() => {
    lastDistance.current = null;
    if (isZooming.current) {
      isZooming.current = false;
      onScaleEnd?.();
    }
  }, [onScaleEnd]);

  useEffect(() => {
    const element = targetRef.current;
    if (!element) return;

    element.style.touchAction = 'none';

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });
    element.addEventListener('touchcancel', handleTouchCancel, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      element.removeEventListener('touchcancel', handleTouchCancel);
      element.style.touchAction = '';
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, handleTouchCancel]);

  const setScale = useCallback((scale: number) => {
    currentScale.current = Math.max(minScale, Math.min(maxScale, scale));
  }, [minScale, maxScale]);

  return {
    currentScale: currentScale.current,
    setScale,
    isZooming: isZooming.current
  };
};

export const usePdfPinchZoom = (
  targetRef: React.RefObject<HTMLElement | null>,
  onZoomIn: () => void,
  onZoomOut: () => void
) => {
  const lastDistance = useRef<number | null>(null);
  const isZooming = useRef(false);
  const zoomThreshold = 20;

  // 阻止浏览器默认缩放行为的事件处理器
//   const preventDefaultZoom = useCallback((event: Event) => {
//     event.preventDefault();
//   }, []);

  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (event.touches.length === 2) {
      event.preventDefault();
      event.stopPropagation();
      
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      lastDistance.current = getDistance(touch1, touch2);
      isZooming.current = true;
      
      console.log('[PDF缩放] 开始双指缩放', {
        初始距离: Math.round(lastDistance.current),
        触摸点1: { x: Math.round(touch1.x), y: Math.round(touch1.y) },
        触摸点2: { x: Math.round(touch2.x), y: Math.round(touch2.y) },
        时间: new Date().toLocaleTimeString()
      });
    }
  }, []);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (event.touches.length === 2 && isZooming.current && lastDistance.current !== null) {
      event.preventDefault();
      event.stopPropagation();
      
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      const currentDistance = getDistance(touch1, touch2);
      
      const distanceChange = currentDistance - lastDistance.current;
      
      console.log('[PDF缩放] 手指移动检测', {
        当前距离: Math.round(currentDistance),
        距离变化: Math.round(distanceChange),
        阈值: zoomThreshold,
        是否超过阈值: Math.abs(distanceChange) > zoomThreshold
      });
      
      if (Math.abs(distanceChange) > zoomThreshold) {
        const zoomDirection = distanceChange > 0 ? '放大' : '缩小';
        console.log(`[PDF缩放] 触发${zoomDirection}`, {
          距离变化: Math.round(distanceChange),
          新距离: Math.round(currentDistance),
          时间: new Date().toLocaleTimeString()
        });
        
        if (distanceChange > 0) {
          onZoomIn();
        } else {
          onZoomOut();
        }
        lastDistance.current = currentDistance;
      }
    }
  }, [onZoomIn, onZoomOut]);

  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (event.touches.length < 2) {
      console.log('[PDF缩放] 结束缩放', {
        剩余触摸点: event.touches.length,
        时间: new Date().toLocaleTimeString()
      });
      
      lastDistance.current = null;
      isZooming.current = false;
    }
  }, []);

  // 阻止Ctrl+滚轮缩放
  const handleWheel = useCallback((event: WheelEvent) => {
    if (event.ctrlKey) {
      event.preventDefault();
      event.stopPropagation();
    }
  }, []);

  // 阻止手势事件（Safari/Webkit）
  const handleGestureStart = useCallback((event: Event) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  // 阻止pointer events的缩放
  const handlePointerDown = useCallback((event: PointerEvent) => {
    if (event.pointerType === 'touch') {
      event.preventDefault();
      event.stopPropagation();
    }
  }, []);

  useEffect(() => {
    const element = targetRef.current;
    if (!element) {
      console.log('[Touch Hook] 警告: 目标元素未找到');
      return;
    }

    console.log('[Touch Hook] 初始化缩放事件监听器', element);

    // 设置CSS样式阻止默认行为
    element.style.touchAction = 'none';
    element.style.userSelect = 'none';
    (element.style as any).webkitUserSelect = 'none';
    (element.style as any).webkitTouchCallout = 'none';

    // 添加事件监听器
    element.addEventListener('touchstart', handleTouchStart, { passive: false, capture: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: false, capture: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: false, capture: true });
    element.addEventListener('touchcancel', handleTouchEnd, { passive: false, capture: true });
    
    // 阻止wheel事件的缩放
    element.addEventListener('wheel', handleWheel, { passive: false, capture: true });
    
    // 阻止手势事件（主要用于Safari）
    element.addEventListener('gesturestart', handleGestureStart, { passive: false, capture: true });
    element.addEventListener('gesturechange', handleGestureStart, { passive: false, capture: true });
    element.addEventListener('gestureend', handleGestureStart, { passive: false, capture: true });
    
    // 阻止pointer events
    element.addEventListener('pointerdown', handlePointerDown, { passive: false, capture: true });

    // 在document级别也阻止缩放事件（作为备选方案）
    const handleDocumentWheel = (event: WheelEvent) => {
      if (event.ctrlKey && element.contains(event.target as Node)) {
        event.preventDefault();
        event.stopPropagation();
      }
    };

    const handleDocumentKeydown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && (event.key === '+' || event.key === '-' || event.key === '0')) {
        if (element.contains(document.activeElement)) {
          event.preventDefault();
          event.stopPropagation();
        }
      }
    };

    document.addEventListener('wheel', handleDocumentWheel, { passive: false, capture: true });
    document.addEventListener('keydown', handleDocumentKeydown, { passive: false, capture: true });

    return () => {
      // 清理元素事件监听器
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      element.removeEventListener('touchcancel', handleTouchEnd);
      element.removeEventListener('wheel', handleWheel);
      element.removeEventListener('gesturestart', handleGestureStart);
      element.removeEventListener('gesturechange', handleGestureStart);
      element.removeEventListener('gestureend', handleGestureStart);
      element.removeEventListener('pointerdown', handlePointerDown);
      
      // 清理document事件监听器
      document.removeEventListener('wheel', handleDocumentWheel);
      document.removeEventListener('keydown', handleDocumentKeydown);
      
      // 恢复样式
      element.style.touchAction = '';
      element.style.userSelect = '';
      (element.style as any).webkitUserSelect = '';
      (element.style as any).webkitTouchCallout = '';
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, handleWheel, handleGestureStart, handlePointerDown]);

  return { isZooming: isZooming.current };
};

/**
 * 调试用的Touch事件监听Hook
 * 帮助开发者了解touch事件是否被正确识别和处理
 */
export const useDebugTouch = (
  targetRef: React.RefObject<HTMLElement | null>,
  enabled: boolean = false
) => {
  const debugInfo = useRef({
    touchCount: 0,
    lastEvent: '',
    lastTimestamp: 0,
    isBlocked: false
  });

  const logEvent = useCallback((eventType: string, event: TouchEvent) => {
    if (!enabled) return;
    
    debugInfo.current = {
      touchCount: event.touches.length,
      lastEvent: eventType,
      lastTimestamp: Date.now(),
      isBlocked: event.defaultPrevented
    };
    
    console.log(`[Touch Debug] ${eventType}:`, {
      touchCount: event.touches.length,
      defaultPrevented: event.defaultPrevented,
      timestamp: new Date().toISOString()
    });
  }, [enabled]);

  useEffect(() => {
    if (!enabled) return;
    
    const element = targetRef.current;
    if (!element) return;

    const handleTouch = (event: TouchEvent) => {
      logEvent(event.type, event);
    };

    element.addEventListener('touchstart', handleTouch, true);
    element.addEventListener('touchmove', handleTouch, true);
    element.addEventListener('touchend', handleTouch, true);

    return () => {
      element.removeEventListener('touchstart', handleTouch, true);
      element.removeEventListener('touchmove', handleTouch, true);
      element.removeEventListener('touchend', handleTouch, true);
    };
  }, [enabled, logEvent, targetRef]);

  return debugInfo.current;
};

import {Flex} from "antd";
import {AiIcon, ChildThemeIcon, FreeNodeIcon, NotionIcon} from "@/components/icons";
import {useNodeAdd} from "@/components/flow/hooks/node-add.ts";
import {useFlowStore} from "@/store/flow-store.ts";
import {useNodeChat} from "@/components/flow/hooks/node-chat.ts";
import {useNodeNote} from "@/components/flow/hooks/node-note.ts";
import {useEffect} from "react";
import {useReactFlow} from "@xyflow/react";
import {asElement, isHTMLElement} from "@/components/pdf/components/highlight/src/lib/pdfjs-dom.ts";

const FlowBar = () => {
    const {selectedNodes} = useFlowStore((state) => ({
        selectedNodes: state.selectedNodes,
    }))
    // 新增节点
    const {handleAddNode} = useNodeAdd()
    // AI聊天
    const {handleChat} = useNodeChat()
    // 记事本
    const {handleCopyToNotebook} = useNodeNote()
    // flow实例
    const {screenToFlowPosition} = useReactFlow()
    useEffect(() => {
        // 双击创建节点
        document.addEventListener("dblclick", async (e) => {
            if (
                !isHTMLElement(e.target) ||
                !asElement(e.target).closest(".react-flow__pane") ||
                asElement(e.target).closest(".react-flow__node")) {
                return
            }
            const position = screenToFlowPosition({x: e.clientX, y: e.clientY})
            await handleAddNode({x: position.x, y: position.y})
        })
    }, [handleAddNode]);
    return null
    return (
        <Flex align="center" gap="35px" className="px-4"
              style={{background: "linear-gradient(90deg, #d0d3e9, transparent) padding-box,linear-gradient(90deg, #a1a8da, transparent) border-box"}}>
            <Flex align="center" vertical onClick={() => handleAddNode({setCenter: true})} className="cursor-pointer">
                <FreeNodeIcon size={40} color="#4654B7"/>
                <div>自由节点</div>
            </Flex>
            <Flex align="center" vertical
                  className={selectedNodes.length === 1 ? "cursor-pointer" : "cursor-not-allowed"}
                  onClick={() => selectedNodes.length === 1 && handleAddNode({pids: [selectedNodes[0].id]})}>
                <ChildThemeIcon size={40} color="#4654B7" {...(selectedNodes.length === 1 ? {} : {disabled: true})}/>
                <div>子节点</div>
            </Flex>
            <Flex align="center" vertical className={selectedNodes.length > 1 ? "cursor-pointer" : "cursor-not-allowed"}
                  onClick={() => selectedNodes.length > 1 && handleAddNode({pids: selectedNodes.map((node) => node.id)})}>
                <ChildThemeIcon size={40} color="#4654B7" {...(selectedNodes.length > 1 ? {} : {disabled: true})}/>
                <div>概括</div>
            </Flex>
            <Flex align="center" vertical className={selectedNodes.length > 0 ? "cursor-pointer" : "cursor-not-allowed"}
                  onClick={() => {
                selectedNodes.forEach((node) => {
                    handleChat(node.id)
                })
            }}>
                <AiIcon size={40} color="#4654B7" {...(selectedNodes.length > 0 ? {} : {disabled: true})}/>
                <div>AI聊天</div>
            </Flex>
            <Flex align="center" vertical className={selectedNodes.length > 0 ? "cursor-pointer" : "cursor-not-allowed"}
                  onClick={() => {
                handleCopyToNotebook(selectedNodes.map((node) => node.id))
            }}>
                <NotionIcon size={40} color="#4654B7" {...(selectedNodes.length > 0 ? {} : {disabled: true})}/>
                <div>复制到笔记本</div>
            </Flex>
            {/*<Flex align="center" vertical className="cursor-pointer">*/}
            {/*    <ExportIcon size={40} color="#4654B7"/>*/}
            {/*    <div>导出</div>*/}
            {/*</Flex>*/}
        </Flex>
    );
}

export {FlowBar}
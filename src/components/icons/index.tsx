import styled from "styled-components";
import React from "react";
import type { FunctionComponent, SVGProps } from "react";
import { SVG_ICONS } from "./svg-data";
interface SvgIconProps {
  color?: string;
  size?: number;
  opacity?: number;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

// 基础包装函数
const wrapIcon = (SvgComponent: FunctionComponent<SVGProps<SVGSVGElement>>) =>
  SvgComponent;

// 单色图标处理
const monochromeIcon = (
  SvgComponent: FunctionComponent<SVGProps<SVGSVGElement>>,
  enabledFillColor?: string
) => styled(SvgComponent)<{
  disabled?: number;
}>`
  path {
    ${(props) =>
      enabledFillColor ? `fill: var(${enabledFillColor});` : undefined};
    opacity: ${(props) => (props.disabled ? 0.25 : 1)};
    transition: opacity 0.2s;
  }
`;

// 创建可点击的SVG按钮
const StyledSvg = styled.svg<SvgIconProps>`
  width: ${(props) => props.size || 9}px;
  height: ${(props) => props.size || 9}px;
  opacity: ${(props) => (props.disabled ? 0.25 : props.opacity || 1)};
  transition: opacity 0.2s;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  path {
    fill: ${(props) => props.color || "currentColor"};
  }
`;

// SVG组件创建函数
const createSvgIcon = (
  SvgComponent: FunctionComponent<SVGProps<SVGSVGElement>> | string
) => {
  const SvgIcon: React.FC<SvgIconProps> = ({
    color, // 默认使用随机颜色
    size,
    opacity,
    className,
    disabled,
  }) => {
    if (typeof SvgComponent === "string") {
      // 处理SVG字符串
      const viewBoxMatch = SvgComponent.match(/viewBox="([^"]+)"/);
      const pathMatch = SvgComponent.match(/<path[^>]*d="([^"]+)"[^>]*>/);
      const viewBox = viewBoxMatch ? viewBoxMatch[1] : "0 0 24 24";
      const pathD = pathMatch ? pathMatch[1] : "";

      return (
        <StyledSvg
          viewBox={viewBox}
          xmlns="http://www.w3.org/2000/svg"
          color={color}
          size={size}
          opacity={opacity}
          className={className}
          disabled={disabled}
        >
          <path d={pathD} />
        </StyledSvg>
      );
    } else {
      // 处理SVG组件
      const Svg = monochromeIcon(SvgComponent);
      return <Svg disabled={disabled ? 1 : 0} className={className} />;
    }
  };

  return SvgIcon;
};

// 导出图标组件
export const TagIcon = createSvgIcon(SVG_ICONS.tag);
export const HomeIcon = createSvgIcon(SVG_ICONS.home);
export const PlusIcon = createSvgIcon(SVG_ICONS.plus);
export const PlusMini = createSvgIcon(SVG_ICONS.plus_mini);
export const SearchIcon = createSvgIcon(SVG_ICONS.search);
export const CloseIcon = createSvgIcon(SVG_ICONS.close);
export const GridIcon = createSvgIcon(SVG_ICONS.grid);
export const ListIcon = createSvgIcon(SVG_ICONS.list);
export const PdfUploadThumbIcon = createSvgIcon(SVG_ICONS.pdf_upload_thumb);
export const PdfDeleteIcon = createSvgIcon(SVG_ICONS.pdf_delete);
export const ExpandIcon = createSvgIcon(SVG_ICONS.expand);
export const CollapseIcon = createSvgIcon(SVG_ICONS.collapse);
export const FreeNodeIcon = createSvgIcon(SVG_ICONS.free_node);
export const ChildThemeIcon = createSvgIcon(SVG_ICONS.child_theme);
export const ExportIcon = createSvgIcon(SVG_ICONS.export_icon);
export const AiIcon = createSvgIcon(SVG_ICONS.ai_icon);
export const NotionIcon = createSvgIcon(SVG_ICONS.notion_icon);
export const EnterIcon = createSvgIcon(SVG_ICONS.enter_icon);
export const ChatEditIcon = createSvgIcon(SVG_ICONS.chat_edit_icon);
export const ChatReloadIcon = createSvgIcon(SVG_ICONS.chat_reload_icon);
export const ChatToNodeIcon = createSvgIcon(SVG_ICONS.chat_to_node_icon);
export const LinkIcon = createSvgIcon(SVG_ICONS.link_icon);
export const ChatSideIcon: React.FC<SvgIconProps> = ({
  color = "#3D56BA",
  size = 24,
  opacity = 1,
  className,
  disabled,
  onClick,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      opacity: disabled ? 0.25 : opacity,
      cursor: "pointer",
    }}
    className={className}
    onClick={onClick}
  >
    <rect
      x="4"
      y="4"
      width="16"
      height="16"
      rx="3"
      stroke={color}
      strokeWidth="3"
      fill="none"
    />
    <line x1="9" y1="4" x2="9" y2="20" stroke={color} strokeWidth="3" />
  </svg>
);
// 可以继续添加更多图标...

// 导出工具函数
export { wrapIcon, monochromeIcon, createSvgIcon };

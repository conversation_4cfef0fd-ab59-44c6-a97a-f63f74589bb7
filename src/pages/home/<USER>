import styled from "styled-components";
import { Category } from "@/components/homepage/category";
import { Project } from "@/components/homepage/project";
import { Header } from "@/components/homepage/header";
const Container = styled.div`
  width: 100%;
  height: 100%;
  background: #f1f2ff;
  display: flex;
  flex-direction: column;
`;

const Content = styled.div`
  display: flex;
  flex: 1;
`;

export const Home = () => {
  return (
    <Container>
      <Header />
      <Content>
        {/* 分类 */}
        <Category />
        {/* 项目 */}
        <Project />
      </Content>
    </Container>
  );
};

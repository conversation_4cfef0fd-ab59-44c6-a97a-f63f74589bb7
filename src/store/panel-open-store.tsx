import { createWithEqualityFn } from "zustand/traditional";
import { shallow } from "zustand/shallow";
import { persist } from "zustand/middleware";

type PanelOpenType = {
  pdfPanelOpen: boolean;
  chatPanelOpen: boolean;
  notePanelOpen: boolean;
  tagPanelOpen: boolean;
  togglePdfPanel: (open: boolean) => void;
  toggleChatPanel: (open: boolean) => void;
  toggleNotePanel: (open: boolean) => void;
  toggleTagPanel: (open: boolean) => void;
};

export const usePanelOpenStore = createWithEqualityFn<
  PanelOpenType,
  [["zustand/persist", PanelOpenType]]
>(
  persist(
    (set) => ({
      pdfPanelOpen: true,
      chatPanelOpen: false,
      notePanelOpen: false,
      tagPanelOpen: false,
      togglePdfPanel: (open: boolean) =>
        set(() => ({
          pdfPanelOpen: open,
        })),
      toggleChatPanel: (open: boolean) =>
        set(() => ({
          chatPanelOpen: open,
        })),
      toggleNotePanel: (open: boolean) =>
        set(() => ({
          notePanelOpen: open,
        })),
      toggleTagPanel:(open:boolean) => set(()=>({tagPanelOpen: open}))
    }),
    {
      name: "panel-open-storage",
    }
  ),
  shallow
);

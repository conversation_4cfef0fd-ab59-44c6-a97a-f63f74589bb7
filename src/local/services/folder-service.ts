import { getDatabase } from '../db';
import { AttachmentDocType, generateId } from '@/local';

/**
 * 文件夹和文件管理服务
 */
export class FolderService {
    
    /**
     * 创建文件夹
     */
    async createFolder(req: {
        wid: string;
        name: string;
        parent_id?: string;
    }): Promise<{ id: string }> {
        const db = await getDatabase();
        const now = Math.floor(Date.now() / 1000);
        const id = generateId();
        
        // 检查同一父文件夹下是否已存在同名文件夹
        const existingFolder = await db.attachments.findOne({
            selector: {
                wid: req.wid,
                parent_id: req.parent_id || 'root',
                file_name: req.name,
                type: 'folder'
            }
        }).exec();
        
        if (existingFolder) {
            throw new Error(`文件夹 "${req.name}" 已存在`);
        }
        
        // 获取父文件夹下的最大order值
        const maxOrder = await this.getMaxOrderInParent(req.wid, req.parent_id || 'root');
        
        const folderData: AttachmentDocType = {
            id,
            wid: req.wid,
            file_name: req.name,
            type: 'folder',
            parent_id: req.parent_id || 'root',
            order: maxOrder + 1,
            create_at: now,
            update_at: now
        };

        await db.attachments.insert(folderData);
        
        console.log('文件夹创建成功:', req.name);
        return { id };
    }

    /**
     * 创建文件记录
     */
    async createFile(req: {
        wid: string;
        file_name: string;
        parent_id?: string;
        size?: number;
        mime_type?: string;
        file_path?: string;
    }): Promise<{ id: string }> {
        const db = await getDatabase();
        const now = Math.floor(Date.now() / 1000);
        const id = generateId();
        
        // 获取父文件夹下的最大order值
        const maxOrder = await this.getMaxOrderInParent(req.wid, req.parent_id || 'root');
        
        const fileData: AttachmentDocType = {
            id,
            wid: req.wid,
            file_name: req.file_name,
            type: 'file',
            parent_id: req.parent_id || 'root',
            order: maxOrder + 1,
            size: req.size,
            mime_type: req.mime_type,
            file_path: req.file_path,
            create_at: now,
            update_at: now
        };

        await db.attachments.insert(fileData);
        
        console.log('文件记录创建成功:', req.file_name);
        return { id };
    }

    /**
     * 获取指定父文件夹下的最大order值
     */
    private async getMaxOrderInParent(wid: string, parent_id: string): Promise<number> {
        const db = await getDatabase();
        
        const items = await db.attachments.find({
            selector: {
                wid,
                parent_id
            },
            sort: [{ order: 'desc' }],
            limit: 1
        }).exec();

        return items.length > 0 ? items[0].order : 0;
    }

    /**
     * 获取文件夹/文件列表（按order排序）
     */
    async getList(req: {
        wid: string;
        parent_id?: string;
        type?: 'file' | 'folder';
    }): Promise<AttachmentDocType[]> {
        const db = await getDatabase();
        
        const selector: any = {
            wid: req.wid,
            parent_id: req.parent_id || 'root'
        };

        if (req.type) {
            selector.type = req.type;
        }

        const results = await db.attachments.find({
            selector,
            sort: [{ order: 'asc' }, { create_at: 'asc' }]
        }).exec();

        return results.map((doc: any) => doc.toJSON());
    }

    /**
     * 获取所有文件和文件夹（树形结构数据）
     */
    async getTreeData(wid: string): Promise<AttachmentDocType[]> {
        const db = await getDatabase();
        
        const results = await db.attachments.find({
            selector: { wid },
            sort: [{ parent_id: 'asc' }, { order: 'asc' }]
        }).exec();

        return results.map((doc: any) => doc.toJSON());
    }

    /**
     * 重命名文件/文件夹
     */
    async rename(req: {
        id: string;
        new_name: string;
    }): Promise<void> {
        const db = await getDatabase();
        const now = Math.floor(Date.now() / 1000);

        const item = await db.attachments.findOne({
            selector: { id: req.id }
        }).exec();

        if (!item) {
            throw new Error('文件/文件夹不存在');
        }

        // 检查同一父文件夹下是否已存在同名文件/文件夹
        const existingItem = await db.attachments.findOne({
            selector: {
                wid: item.wid,
                parent_id: item.parent_id,
                file_name: req.new_name,
                id: { $ne: req.id } // 排除当前项目本身
            }
        }).exec();

        if (existingItem) {
            throw new Error(`"${req.new_name}" 已存在`);
        }

        await item.update({
            $set: {
                file_name: req.new_name,
                update_at: now
            }
        });

        console.log('重命名成功:', req.new_name);
    }

    /**
     * 移动文件/文件夹到新的父文件夹
     */
    async move(req: {
        id: string;
        new_parent_id: string;
        new_order?: number;
    }): Promise<void> {
        const db = await getDatabase();
        const now = Math.floor(Date.now() / 1000);

        const item = await db.attachments.findOne({
            selector: { id: req.id }
        }).exec();

        if (!item) {
            throw new Error('文件/文件夹不存在');
        }

        // 如果没有指定新的order，则放到目标文件夹的末尾
        let order = req.new_order;
        if (order === undefined) {
            order = await this.getMaxOrderInParent(item.wid, req.new_parent_id) + 1;
        }

        await item.update({
            $set: {
                parent_id: req.new_parent_id,
                order,
                update_at: now
            }
        });

        console.log('移动成功');
    }

    /**
     * 更新排序（拖拽排序时使用）
     */
    async updateOrder(req: {
        items: Array<{
            id: string;
            order: number;
        }>;
    }): Promise<void> {
        const db = await getDatabase();
        const now = Math.floor(Date.now() / 1000);

        // 批量更新
        for (const item of req.items) {
            const doc = await db.attachments.findOne({
                selector: { id: item.id }
            }).exec();

            if (doc) {
                await doc.update({
                    $set: {
                        order: item.order,
                        update_at: now
                    }
                });
            }
        }

        console.log('排序更新成功');
    }

    /**
     * 删除文件/文件夹（递归删除子项）
     */
    async delete(req: {
        id: string;
    }): Promise<void> {
        const db = await getDatabase();

        const item = await db.attachments.findOne({
            selector: { id: req.id }
        }).exec();

        if (!item) {
            throw new Error('文件/文件夹不存在');
        }

        // 如果是文件夹，递归删除所有子项
        if (item.type === 'folder') {
            await this.deleteChildrenRecursively(item.wid, req.id);
        }

        // 删除自身
        await item.remove();

        console.log('删除成功');
    }

    /**
     * 递归删除文件夹下的所有子项
     */
    private async deleteChildrenRecursively(wid: string, parent_id: string): Promise<void> {
        const db = await getDatabase();

        const children = await db.attachments.find({
            selector: {
                wid,
                parent_id
            }
        }).exec();

        for (const child of children) {
            if (child.type === 'folder') {
                // 递归删除子文件夹
                await this.deleteChildrenRecursively(wid, child.id);
            }
            
            // 删除相关的标记（marks）
            await db.marks.find({
                selector: { attach_id: child.id }
            }).remove();

            // 删除相关的节点（如果有的话）
            await db.nodes.find({
                selector: { id: child.id }
            }).remove();

            // 删除子项
            await child.remove();
        }
    }

    /**
     * 获取文件夹路径（面包屑导航用）
     */
    async getFolderPath(wid: string, folder_id: string): Promise<Array<{id: string, name: string}>> {
        if (folder_id === 'root') {
            return [{ id: 'root', name: '根目录' }];
        }

        const db = await getDatabase();
        const path: Array<{id: string, name: string}> = [];
        
        let current_id = folder_id;
        
        while (current_id !== 'root') {
            const folder = await db.attachments.findOne({
                selector: { id: current_id, type: 'folder' }
            }).exec();

            if (!folder) break;
            
            path.unshift({ id: folder.id, name: folder.file_name });
            current_id = folder.parent_id;
        }
        
        // 添加根目录
        path.unshift({ id: 'root', name: '根目录' });
        
        return path;
    }

    /**
     * 搜索文件/文件夹
     */
    async search(req: {
        wid: string;
        keyword: string;
        type?: 'file' | 'folder';
    }): Promise<AttachmentDocType[]> {
        const db = await getDatabase();
        
        const selector: any = {
            wid: req.wid,
            file_name: {
                $regex: new RegExp(req.keyword, 'i')
            }
        };

        if (req.type) {
            selector.type = req.type;
        }

        const results = await db.attachments.find({
            selector,
            sort: [{ order: 'asc' }]
        }).exec();

        return results.map((doc: any) => doc.toJSON());
    }
}

export const folderService = new FolderService(); 
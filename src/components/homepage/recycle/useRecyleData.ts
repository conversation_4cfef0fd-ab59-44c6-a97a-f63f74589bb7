import { useEffect } from 'react'
import { getProjectList } from '@/api/project'
import { useHomeStore } from '@/store/home-store'

export const useWorkerData = () => {
  const { setRecycleBinList, openReCyclcModal } = useHomeStore()
  // 初始化
  const onFetchData = async () => {
    try {
      const { code, data } = await getProjectList({
        cid: '-1',
        page: 1,
        page_size: 100,
        keyword: '',
        from: 1
      }) as any
      if (code === 0) {
        setRecycleBinList(data.list ? data.list : [])
      }
    } catch (error) {
      console.log(error)
    }
  }
  // 更新数据
  useEffect(() => {
    if (openReCyclcModal) {
      onFetchData()
    }
  }, [openReCyclcModal]);
}
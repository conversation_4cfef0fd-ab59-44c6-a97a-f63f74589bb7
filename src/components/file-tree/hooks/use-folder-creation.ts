import { useState, useCallback, useEffect, useRef } from 'react';

interface UseFolderCreationProps {
  onFolderCreate?: (path: string, name: string) => void;
  onExpandParent?: (parentId: string) => void; // 展开父文件夹的回调
}

export const useFolderCreation = ({ onFolderCreate, onExpandParent }: UseFolderCreationProps = {}) => {
  const [creatingFolder, setCreatingFolder] = useState(false);
  const [creatingFolderPath, setCreatingFolderPath] = useState('/');
  const [creatingFolderParentId, setCreatingFolderParentId] = useState<string>('root');
  const [newFolderName, setNewFolderName] = useState('');
  const newFolderInputRef = useRef<HTMLInputElement>(null);

  // 开始创建新文件夹
  const startCreateFolder = useCallback((path: string, parentId?: string) => {
    setCreatingFolder(true);
    setCreatingFolderPath(path);
    setCreatingFolderParentId(parentId || 'root');
    setNewFolderName('');
  }, []);

  // 取消创建文件夹
  const cancelCreateFolder = useCallback(() => {
    setCreatingFolder(false);
    setCreatingFolderParentId('root');
    setNewFolderName('');
  }, []);

  // 创建文件夹的逻辑
  const createFolder = useCallback(() => {
    if (newFolderName.trim()) {
      // 发送文件夹创建请求事件
      document.dispatchEvent(new CustomEvent('ht-folder-create-request', {
        detail: {
          path: creatingFolderPath,
          name: newFolderName.trim(),
          parentId: creatingFolderParentId
        }
      }));
      cancelCreateFolder();
    } else {
      cancelCreateFolder();
    }
  }, [newFolderName, creatingFolderPath, cancelCreateFolder]);

  // 处理新建文件夹输入框的键盘事件
  const handleNewFolderKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      createFolder();
    } else if (e.key === 'Escape') {
      cancelCreateFolder();
    }
  }, [createFolder, cancelCreateFolder]);

  // 处理新建文件夹输入框的失焦事件
  const handleNewFolderBlur = useCallback(() => {
    // 延迟执行，避免与点击事件冲突
    setTimeout(() => {
      if (creatingFolder) {
        createFolder();
      }
    }, 100);
  }, [creatingFolder, createFolder]);

  // 当进入新建文件夹模式时，自动聚焦输入框
  useEffect(() => {
    if (creatingFolder && newFolderInputRef.current) {
      newFolderInputRef.current.focus();
    }
  }, [creatingFolder]);

  // 监听自定义事件，开始新建文件夹
  useEffect(() => {
    const handleCreateFolderEvent = (e: CustomEvent<{path: string, parentId?: string}>) => {
      startCreateFolder(e.detail.path, e.detail.parentId);
    };
    
    document.addEventListener('ht-create-folder', handleCreateFolderEvent as EventListener);
    
    return () => {
      document.removeEventListener('ht-create-folder', handleCreateFolderEvent as EventListener);
    };
  }, [startCreateFolder]);

  // 监听文件夹创建结果事件
  useEffect(() => {
    const handleFolderCreateResult = (e: CustomEvent<{success: boolean, message: string}>) => {
      // 只在失败时显示错误消息
      if (!e.detail.success) {
        alert(`❌ ${e.detail.message}`);
      } else {
        // 成功时展开父文件夹
        if (onExpandParent && creatingFolderParentId !== 'root') {
          onExpandParent(creatingFolderParentId);
        }
      }
    };

    document.addEventListener('ht-folder-create-result', handleFolderCreateResult as EventListener);

    return () => {
      document.removeEventListener('ht-folder-create-result', handleFolderCreateResult as EventListener);
    };
  }, [onExpandParent, creatingFolderParentId]);

  return {
    // 状态
    creatingFolder,
    creatingFolderPath,
    creatingFolderParentId,
    newFolderName,
    newFolderInputRef,
    
    // 事件处理器
    startCreateFolder,
    cancelCreateFolder,
    handleNewFolderKeyDown,
    handleNewFolderBlur,
    
    // 工具函数
    setNewFolderName,
  };
}; 
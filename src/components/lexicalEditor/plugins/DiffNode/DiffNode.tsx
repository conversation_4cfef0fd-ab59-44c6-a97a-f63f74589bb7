import React from 'react';
import { DecoratorNode } from 'lexical';
import './node.less'

export class DiffNode extends DecoratorNode<any> {
  __diffData: any;
  __diffId: any;

  constructor(diffData: any, diffId: any, key?: string) {
    super(key);
    this.__diffData = diffData;
    this.__diffId = diffId;
  }

  static getType() {
    return 'diff';
  }

  static clone(node) {
    return new DiffNode(node.__diffData, node.__diffId, node.__key);
  }

  getDiffData() {
    return this.__diffData;
  }

  getDiffId() {
    return this.__diffId;
  }

  setDiffData(diffData) {
    const writable = this.getWritable();
    writable.__diffData = diffData;
  }

  createDOM() {
    const element = document.createElement('div');
    element.className = 'diff-container';
    return element;
  }

  updateDOM() {
    return false;
  }

  decorate() {
    return (
      <DiffComponent
        diffData={this.__diffData}
        diffId={this.__diffId}
        node={this}
      />
    );
  }

  static importJSON(serializedNode) {
    const { diffData, diffId } = serializedNode;
    return $createDiffNode(diffData, diffId);
  }

  exportJSON() {
    return {
      type: 'diff',
      diffData: this.__diffData,
      diffId: this.__diffId,
      version: 1,
    };
  }
}

function DiffComponent({ diffData, diffId, node }) {

  const handleAccept = (index) => {
    console.log('Accept diff:', diffId, index);
    // 你的 accept 逻辑
  };

  const handleReject = (index) => {
    console.log('Reject diff:', diffId, index);
    // 你的 reject 逻辑
  };

  return (
    <div className="diff-block">
      <div className="diff-content">
        {diffData.changes.map((change, index) => (
          <React.Fragment key={index}>
            {change.type === 'added' && (
              <HoverActionButtons onAccept={() => handleAccept(index)} onReject={() => handleReject(index)}>
                <span className="diff-added"> {change.text} </span>
              </HoverActionButtons>
            )}
            {change.type === 'removed' && (
              <HoverActionButtons onAccept={() => handleAccept(index)} onReject={() => handleReject(index)}>
                <span className="diff-removed">{change.text}</span>
              </HoverActionButtons>
            )}
            {change.type === 'unchanged' && (
              <span className="diff-unchanged">{change.text}</span>
            )}
          </React.Fragment>
        ))}
      </div>
    </div >
  );
}

export function $createDiffNode(diffData, diffId) {
  return new DiffNode(diffData, diffId);
}

export function $isDiffNode(node) {
  return node instanceof DiffNode;
}


// React 组件用于渲染 diff 内容
function HoverActionButtons({ onAccept, onReject, children }) {
  return (
    <span
      className="diff-added"
    >
      {
        children
      }
    </span>
  );
}
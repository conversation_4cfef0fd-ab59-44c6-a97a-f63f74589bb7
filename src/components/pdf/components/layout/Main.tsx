import {usePdfStore} from "@/store/pdf-store.ts";
import {DraggableTabSystem} from "@/components/pdf/components/draggable-tabs/DraggableTabSystem.tsx";

export const Main = () => {
    const {tabItems, activeAid} = usePdfStore((state) => ({
        tabItems: state.tabItems,
        activeAid: state.activeAid
    }))
    // 获取当前活动标签的内容
    const activeTabContent = tabItems?.find(item => item?.key === activeAid)?.children;

    return (
        <PdfMainLayout>
            <div className="h-full flex flex-col flex-1 min-w-0 pdf-main">
                <DraggableTabSystem>
                    {activeTabContent}
                </DraggableTabSystem>
            </div>
        </PdfMainLayout>

    )
}

const PdfMainLayout = (props: { children: React.ReactNode }) => {
    return (
        <div
            className="flex-1 overflow-hidden"
        >
            <div className="h-[100%] w-[100%] border-box overflow-hidden">
                {props.children}
            </div>
        </div>
    )
}
import {createWithEqualityFn} from "zustand/traditional";
import {shallow} from "zustand/shallow";
import {applyEdgeChanges, applyNodeChanges, Edge, Node, OnEdgesChange, OnNodesChange, Viewport} from "@xyflow/react";
import MarkdownNode from "@/components/flow/node/MarkdownNode.tsx";

// 节点存储数据
export type nodeData = {
    title?: string,
    content: string,
    color: string,
}
// 自定义节点
export type CustomNode = Node<nodeData> & {
    selectedFromHighlight?: boolean, // 节点选中是否来自高亮
    measured?: {
        width: number;
        height: number;
    }; // 节点尺寸信息，用于布局和调整大小
}

// 节点分类
export enum NodeType {
    markdownNode = "markdownNode"
}

export const nodeTypes = {
    [NodeType.markdownNode]: MarkdownNode,
}

export type flowAttr = {
    // 节点
    nodes: CustomNode[], //所有节点
    selectedNodes: CustomNode[], // 选中节点
    setNodes: (nodes: CustomNode[]) => void, // 初始化节点
    setSelectedNodes: (nodes: CustomNode[]) => void, // 设置选中节点
    addNodes: (nodes: CustomNode[]) => void, // 新增节点
    deleteNodes: (nodeIds: string[]) => void; // 删除节点
    updateNodes: (nodes: (Required<Pick<CustomNode, 'id'>> & Partial<Omit<CustomNode, 'data'>> & {
        data?: Partial<nodeData>;
        width?: number;
        height?: number;
        measured?: {
            width: number;
            height: number;
        };
    })[], clearSelected?: boolean) => void, // 更改节点
    onNodesChange: OnNodesChange<CustomNode>, // 节点变更
    // 边
    edges: Edge[], // 所有边
    setEdges: (edges: Edge[]) => void, // 初始化边
    addEdges: (edges: Edge[]) => void, // 新增边
    deleteEdges: (edgeIds: string[]) => void, // 删除边
    onEdgesChange: OnEdgesChange, // 边变更
    // 边编辑状态
    editingEdgeId: string | null, // 当前正在编辑的边ID
    setEditingEdgeId: (edgeId: string | null) => void, // 设置编辑边ID
    // 视图
    viewport: Viewport, // 视图
    center: string | null, // 中心节点
    setCenter: (center: string) => void, // 设置中心节点
    onViewportChange: (viewport: Partial<Viewport>) => void, // 视图变化
    updateEdges: (updateEdges: string, data?: any) => void, // 更新边
}

export const useFlowStore = createWithEqualityFn<flowAttr>((set, get) => ({
    nodes: [],
    selectedNodes: [],
    setNodes: (nodes) =>
        set(() => {
            return {nodes}
        }),
    setSelectedNodes: (selectedNodes) =>
        set(() => {
            return {selectedNodes}
        }),
    addNodes: (nodes) =>
        set((state) => {
            // 移除其它节点选中状态
            const updatedNodes = state.nodes.map((node) => ({
                ...node,
                selected: false,
            }));
            return {
                nodes: [...updatedNodes, ...nodes]
            }
        }),
    deleteNodes: (nodes) =>
        set((state) => {
            // 删除边
            const nextEdges = state.edges.filter((edge) => !nodes.includes(edge.source) && !nodes.includes(edge.target))
            state.setEdges(nextEdges)
            return {
                nodes: state.nodes.filter((node) => !nodes.includes(node.id))
            }
        }),
    updateNodes: (nodes, clearSelected = false) =>
        set((state) => {
            const nodesMap = new Map(nodes.map((node) => [node.id, node]));
            return {
                nodes: state.nodes.map((n) => {
                    const node = nodesMap.get(n.id)
                    if (node) {
                        const updatedData = node.data ? {...n.data, ...node.data} : n.data;
                        return {
                            ...n,
                            ...node,
                            data: updatedData,
                        };
                    } else {
                        if (clearSelected) {
                            return {
                                ...n,
                                selected: false,
                                selectedFromHighlight: false
                            }
                        }
                    }
                    return n;
                }),
            };
        }),

    onNodesChange: (changes) =>
        set((state) => {
            return {
                nodes: applyNodeChanges(changes, state.nodes)
            }
        }),
    edges: [],
    setEdges: (edges) =>
        set(() => {
            const nextEdges = edges.map(edge => ({
                ...edge,
                animated: true,
                zIndex: 1
            }))
            return {edges: nextEdges}
        }),
    addEdges: (edges) =>
        set((state) => {
            const nextEdges = edges.map(edge => ({
                ...edge,
                animated: true,
                zIndex: 1
            }))
            return {
                edges: [...state.edges, ...nextEdges]
            }
        }),
    deleteEdges: (edges) =>
        set((state) => {
            return {
                edges: state.edges.filter((edge) => !edges.includes(edge.id))
            }
        }),
    onEdgesChange: (changes) =>
        set((state) => {
            return {
                edges: applyEdgeChanges(changes, state.edges)
            }
        }),
    // 边编辑状态实现
    editingEdgeId: null,
    setEditingEdgeId: (edgeId) =>
        set(() => {
            return { editingEdgeId: edgeId }
        }),

    viewport: {x: 0, y: 0, zoom: 1},
    center: null,
    setCenter: (center) =>
        set(() => {
            return {center}
        }),
    onViewportChange: (viewport) =>
        set((state) => ({
            viewport: {
                ...state.viewport,
                ...viewport,
            },
        })),
    updateEdges: (tmpId, updateInfo) => {
        set((state) => {
            return {
                edges:state.edges.map((edge) => {
                    if (edge.id === tmpId) {
                        return {
                            ...edge,
                            ...updateInfo
                        }
                    }
                    return edge
                })
            }
        })
    }
        
}), shallow)

// 辅助函数：判断边是否正在编辑
export const isEdgeEditing = (edgeId: string): boolean => {
    return useFlowStore.getState().editingEdgeId === edgeId;
}
import styled from "styled-components";
import { useHomeStore } from "@/store/home-store";
import { GridIcon, ListIcon } from "@/components/icons";
const Container = styled.div`
  width: 100%;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  margin-top: 40px;
  margin-bottom: 36px;
`;

interface CustomSwitchProps {
  checked: boolean;
}

const CustomSwitch = styled.div<CustomSwitchProps>`
  width: 64px;
  height: 32px;
  background-color: #aab6e4;
  border-radius: 16px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  padding: 2px;
  margin-right: 55px;

  .switch-circle {
    width: 28px;
    height: 28px;
    background-color: white;
    border-radius: 50%;
    position: absolute;
    transition: transform 0.3s;
    transform: translateX(${(props) => (props.checked ? "32px" : "2px")});
  }

  .icons-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
    z-index: 1;
  }
`;

export const ProjectHeader = () => {
  const { layoutType, currentCategory, setlayoutType } = useHomeStore();
  return (
    <Container>
      <div
        className="text-[var(--text-primary)] flex items-center"
        style={{ letterSpacing: "1.4px" }}
      >
        <div className="text-[28px] font-[400] flex flex-col gap-[3px]">
          <span>{currentCategory?.cname}</span>
          <span className="h-1 w-[98px] bg-[var(--text-primary)] rounded-full" />
        </div>
        <span className="text-[18px] font-[300] ">
          ({currentCategory?.wnum})
        </span>
      </div>
      <CustomSwitch
        checked={layoutType === "list"}
        onClick={() => setlayoutType(layoutType === "list" ? "grid" : "list")}
      >
        <div className="switch-circle" />
        <div className="icons-container">
          <GridIcon
            size={13}
            color={layoutType === "grid" ? "var(--text-primary)" : "#C4CCEA"}
          />
          <ListIcon
            color={layoutType === "list" ? "var(--text-primary)" : "#C4CCEA"}
            size={13}
          />
        </div>
      </CustomSwitch>
    </Container>
  );
};

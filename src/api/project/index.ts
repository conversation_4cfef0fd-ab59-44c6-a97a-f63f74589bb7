import { httpRequest } from '../index'
import { DeleteProjectType, CreateProjectType, UpdateProjectType, ProjectListType, RestoreProjectType } from './types.d'
// 新建工作区
export const createProject = (data: CreateProjectType) => httpRequest.post('/ws', data)
// 更新工作区
export const updateProject = (data: UpdateProjectType) => httpRequest.patch('/ws', data)
// 删除工作区
export const deleteProject = (data: DeleteProjectType) => httpRequest.delete('/ws', {
  data
})
// 获取工作区列表
export const getProjectList = (data: ProjectListType) => httpRequest.get('/ws/list', {
  params: {
    cid: data.cid,
    page: data.page || 1,
    page_size: data.page_size || 100,
    keyword: data.keyword || '',
    from: data.from || 0
  }
})

// 彻底删除项目
export const deleteProjectPermanently = (ids: Array<string | number>) => httpRequest.post('ws/clean', {
  wids: ids
})
// 上传canvas预览图
// 回收站项目还原
export const restoreProject = (data: RestoreProjectType) => httpRequest.post('/ws/restore', data)

export * from './types.d'
import {CustomNode, NodeType, useFlowStore} from "@/store/flow-store.ts";
import {getNodesList} from "@/api/canvas";
import {getWsId} from "@/tools/params.ts";
import {Edge, useReactFlow} from "@xyflow/react";
import {useEffect} from "react";
import {getPdfList} from "@/api/pdf";
import {CustomHighlight, pdfSingleState, usePdfStore} from "@/store/pdf-store.ts";
import {PdfViewer} from "@/components/pdf/components/highlight/PdfViewer.tsx";

const useFetchData = () => {
    const wid = getWsId();
    if (!wid) return;

    const {setNodes, setEdges} = useFlowStore((state) => ({
        setNodes: state.setNodes,
        setEdges: state.setEdges,
    }));
    const {setPdfs, setActiveAid, setTabItems} = usePdfStore((state) => ({
        setPdfs: state.setPdfs,
        setActiveAid: state.setActiveAid,
        setTabItems: state.setTabItems,
    }))
    const { fitView } = useReactFlow();
    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await getNodesList(wid);
                const {nodes, edges} = response.data;

                // 初始化节点
                const customNodes: CustomNode[] = nodes.map((node: any) => ({
                    id: node.nid,
                    type: NodeType.markdownNode,
                    position: {x: node.x, y: node.y},
                    data: {
                        title: node.title,
                        content: node.content,
                        color: `#${node.color}`,
                    },
                    // 设置节点尺寸 - 用于显示
                    width: node.width || 400,
                    height: node.height || 114,
                    // 设置测量尺寸 - 用于布局计算
                    measured: {
                        width: node.width || 400,
                        height: node.height || 114,
                    },
                }));

                // 初始化边
                const customEdges: Edge[] = edges.map((edge: any) => ({
                    id: edge.eid,
                    source: edge.pid,
                    target: edge.nid,
                    type: 'editableEdge',
                    data: { label: edge.label || '' },
                    style: edge.style || {},
                }));

                // 更新状态
                setNodes(customNodes);
                setEdges(customEdges);
                
                // 如果有节点，自动调整视图使所有节点都可见
                if (customNodes.length > 0) {
                    // 使用 setTimeout 确保节点已经渲染完成
                    setTimeout(() => {
                        fitView({
                            padding: 0.1, // 10% 的边距
                            includeHiddenNodes: false,
                            minZoom: 0.1,
                            maxZoom: 1.5,
                            duration: 800, // 动画时长
                        });
                    }, 100);
                }
            } catch (error) {
                console.error("初始化节点和边失败：", error);
            }
        };

        fetchData();
    }, [wid, setNodes, setEdges]);

    // 获取附件列表
    useEffect(() => {
        if (!wid) return;
        const fetchAttachList = async () => {
            try {
                // 获取附件列表
                const response = await getPdfList(wid);

                // 使用类型断言处理API返回值
                const attachs = response as any;

                // 检查列表是否为空
                if (attachs.data.list.length === 0) {
                    return;
                }

                // 初始化附件列表
                const initPdfs = new Map<string, Partial<pdfSingleState>>();

                attachs.data.list.forEach((item: any) => {
                    // 初始化高亮
                    const highlights: CustomHighlight[] = [];
                    item.marks && item.marks.forEach((mark: any) => {
                        try {
                            const position = mark.mark ? JSON.parse(mark.mark) : {};

                            // 创建高亮对象
                            const highlight: CustomHighlight = {
                                id: mark.mid,
                                aid: mark.aid,
                                nid: mark.nid,
                                color: `#${mark.color}`,
                                position: position,
                                type: mark.mark_type,
                                content: {
                                    text: mark.mark_type !== "area" ? mark.content : "",
                                    image: mark.mark_type === "area" ? mark.content : ""
                                },
                            };

                            highlights.push(highlight);
                        } catch (error) {
                            console.error(`解析高亮 ${mark.mid} 失败:`, error);
                        }
                    });

                    initPdfs.set(item.aid, {
                        aid: item.aid,
                        filename: item.fname,
                        url: item.url,
                        scale: 1,
                        highlights: highlights,
                    });
                });
                // 更新pdfs
                setPdfs(initPdfs);
                setActiveAid(attachs.data.list[0].aid);
                setTabItems([{
                    key: attachs.data.list[0].aid,
                    label: attachs.data.list[0].fname,
                    children: <PdfViewer aid={attachs.data.list[0].aid}/>,
                }])
            } catch (error) {
                console.error("获取附件列表失败:", error);
            }
        }
        fetchAttachList();

        // 组件卸载时清理状态
        return () => {
            setPdfs(new Map());
            setActiveAid('');
            setTabItems([]);
        };
    }, [wid, setPdfs, setActiveAid, setTabItems]);
};

export {useFetchData};

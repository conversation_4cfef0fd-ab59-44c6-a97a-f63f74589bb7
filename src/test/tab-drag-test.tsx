import React, { useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { usePdfStore } from '@/store/pdf-store';
import { DraggableTabSystem } from '@/components/pdf/components/draggable-tabs';
import { GlobalWindowManager } from '@/components/pdf/components/draggable-tabs/GlobalWindowManager';

/**
 * 标签拖拽测试页面
 * 用于测试标签页脱离标签栏创建窗口时的拖拽预览功能
 */
export const TabDragTest: React.FC = () => {
  const { setTabItems, tabItems } = usePdfStore((state) => ({
    setTabItems: state.setTabItems,
    tabItems: state.tabItems,
  }));

  // 初始化测试数据
  useEffect(() => {
    const testTabs = [
      {
        key: 'tab1',
        label: '测试文档1',
        children: <div className="p-4">这是测试文档1的内容</div>,
        closable: true,
      },
      {
        key: 'tab2',
        label: '测试文档2',
        children: <div className="p-4">这是测试文档2的内容</div>,
        closable: true,
      },
      {
        key: 'tab3',
        label: '长标题测试文档3',
        children: <div className="p-4">这是测试文档3的内容</div>,
        closable: true,
      },
    ];

    if (tabItems.length === 0) {
      setTabItems(testTabs);
    }
  }, [setTabItems, tabItems.length]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen w-full bg-gray-100">
        <div className="h-full flex flex-col">
          <div className="bg-white border-b border-gray-200 p-4">
            <h1 className="text-xl font-bold text-gray-800">标签拖拽测试</h1>
            <p className="text-sm text-gray-600 mt-1">
              拖拽标签页脱离标签栏，观察窗口左上角是否显示拖拽预览层
            </p>
          </div>
          
          <div className="flex-1 relative">
            <DraggableTabSystem>
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <h2 className="text-lg font-medium mb-2">拖拽测试说明</h2>
                  <ul className="text-sm space-y-1">
                    <li>1. 拖拽上方的标签页脱离标签栏</li>
                    <li>2. 观察新创建的窗口左上角的蓝色模拟标签页</li>
                    <li>3. 模拟标签页可以拖拽到其他标签栏进行合并</li>
                    <li>4. 模拟标签页不受窗口边界限制，可以触发drop操作</li>
                    <li>5. 拖拽结束后模拟标签页会消失</li>
                  </ul>
                </div>
              </div>
            </DraggableTabSystem>
          </div>
        </div>
        
        {/* 全局窗口管理器 */}
        <GlobalWindowManager />
      </div>
    </DndProvider>
  );
};

export default TabDragTest;

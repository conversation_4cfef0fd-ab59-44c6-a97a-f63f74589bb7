import React, { useEffect } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>alog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// 导入我们新创建的分离逻辑
import { useTagGroups, useTagStore } from "./stores/useTagStore";
import { useTagOperations } from "./hooks/useTagOperations";
import { useDragAndDrop } from "./hooks/useDragAndDrop";
import { useModalManager } from "./hooks/useModalManager";

// 导入组件
import { TagGroup } from "./TagGroup";
import { TagItem } from "./TagItem";

import TagIcon from "@/assets/icons/tag.svg?react";

interface TagManagementPageRefactoredProps {
  onClose: () => void;
}
export const TagManagementPageRefactored: React.FC<
  TagManagementPageRefactoredProps
> = ({ onClose }) => {
  const { loadTagGroups, isLoading, error } = useTagStore();

  // 数据状态
  const tagGroups = useTagGroups();

  // 组件挂载时自动加载数据
  useEffect(() => {
    loadTagGroups();
  }, [loadTagGroups]);

  // CRUD 操作
  const tagOperations = useTagOperations();

  // 拖拽功能
  useDragAndDrop({
    enableGroupReordering: true,
    enableTagReordering: true,
    enableCrossGroupMove: true,
    showDropIndicators: true,
  });

  // 模态框管理
  const {
    modalState,
    formData,
    isGroupModal,
    isCreateMode,
    openCreateGroup,
    openEditGroup,
    openCreateTag,
    openEditTag,
    closeModal,
    setGroupTitle,
    setTagName,
    getModalTitle,
    getFieldLabel,
    getCurrentValue,
    getPlaceholder,
    validateForm,
  } = useModalManager();

  // 处理表单提交
  const handleFormSubmit = async () => {
    const validation = validateForm();
    if (!validation.isValid) {
      return;
    }

    try {
      if (isGroupModal) {
        if (isCreateMode) {
          await tagOperations.createGroup(formData.groupTitle);
        } else if (modalState.editingId) {
          await tagOperations.editGroup(
            modalState.editingId,
            formData.groupTitle
          );
        }
      } else {
        if (isCreateMode && modalState.currentGroupId) {
          await tagOperations.createTag(
            modalState.currentGroupId,
            formData.tagName
          );
        } else if (modalState.editingId && modalState.currentGroupId) {
          await tagOperations.editTag(
            modalState.currentGroupId,
            modalState.editingId,
            formData.tagName
          );
        }
      }
      closeModal();
    } catch (error) {
      console.error("表单提交失败:", error);
    }
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    if (isGroupModal) {
      setGroupTitle(value);
    } else {
      setTagName(value);
    }
  };

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div>错误: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl max-h-[100vh] overflow-hidden">
      {/* 页面头部 */}
      <div className="flex flex-col justify-between items-start gap-3 mb-8">
        <div className="flex justify-between items-center w-full">
          <h2 className="text-xl font-bold text-gray-900 flex gap-2 items-center">
            <TagIcon /> 标签
          </h2>
          <Button variant="ghost" onClick={onClose}>
            返回
          </Button>
        </div>
        <Button
          variant="outline"
          onClick={openCreateGroup}
          className="flex items-center gap-2 font-normal"
        >
          <Plus className="h-4 w-4" />
          新建分组
        </Button>
      </div>

      {/* 标签分组网格 */}
      <div className="flex flex-col gap-3 overflow-y-auto h-[calc(100vh-100px)]">
        {tagGroups.map((group) => (
          <TagGroup
            key={group.id}
            id={group.id}
            title={group.title}
            tags={group.tags}
            onEdit={(groupId) => openEditGroup(groupId, group.title)}
            onDelete={tagOperations.deleteGroup}
            onAddTag={openCreateTag}
            onEditTag={(groupId, tagId) => {
              const tag = group.tags.find((t) => t.id === tagId);
              openEditTag(groupId, tagId, tag?.name);
            }}
            onDeleteTag={tagOperations.deleteTag}
          />
        ))}

        {/* 空状态 */}
        {tagGroups.length === 0 && (
          <Card className="col-span-full">
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="text-gray-400 text-4xl mb-4">📁</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无分组
              </h3>
              <p className="text-gray-500 mb-4">
                点击"新建分组"按钮创建您的第一个标签分组
              </p>
              <Button onClick={openCreateGroup}>
                <Plus className="h-4 w-4 mr-2" />
                新建分组
              </Button>
            </div>
          </Card>
        )}
      </div>

      {/* 模态框 */}
      <Dialog open={modalState.isOpen} onOpenChange={closeModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{getModalTitle()}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="input-field">{getFieldLabel()}</Label>
              <Input
                id="input-field"
                value={getCurrentValue()}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder={getPlaceholder()}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleFormSubmit();
                  }
                }}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeModal}>
              取消
            </Button>
            <Button onClick={handleFormSubmit}>
              {isCreateMode ? "创建" : "保存"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TagManagementPageRefactored;

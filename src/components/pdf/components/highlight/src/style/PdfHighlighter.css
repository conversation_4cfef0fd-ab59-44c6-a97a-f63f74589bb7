.PdfHighlighter {
    position: absolute;
    overflow: auto;
    width: 100%;
    height: 100%;


    /* 默认隐藏滚动条 */
    &::-webkit-scrollbar {
        width: 6px;
        background-color: transparent;  /* 使用透明背景 */
        opacity: 0;  /* 使用透明度代替 display:none */
    }

    /* 滚动时的样式+悬停的样式 */
    &:hover,
    &.scrolling {
        /* 显示滚动条 */
        &::-webkit-scrollbar {
            display: block;
            width: 15px;
        }

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
            background-color: rgba(255, 255, 255, 0.8);  /* 添加透明度 */
            border-radius: 8px;
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
            background-color:#E0E2F4;;
            border-radius: 4px;
            min-height: 40px;
        }

        /* 滚动条-轨道悬停时的样式 */
        &::-webkit-scrollbar-track:hover {
            background-color: rgba(228, 238, 255, 0.8);  /* 浅蓝色 */
        }

        /* 滚动条-滑块悬停效果 */
        &::-webkit-scrollbar-thumb:hover {
            background-color: rgba(24, 144, 255, 1);  /* 悬停时更深的蓝色 */
        }


    }


}











.PdfHighlighter__tip-container {
    z-index: 6;
    position: absolute;
}

.PdfHighlighter--disable-selection {
    user-select: none;
    /* pointer-events: none; */
}

.PdfHighlighter--disable-selection .textLayer,
.PdfHighlighter--disable-selection .textLayer > span {
    user-select: none;
    /* pointer-events: none; */
}

/* 优化文本层选择行为 */
.textLayer {
    position: absolute;
    inset: 0;
    overflow: hidden;
    opacity: 0.2;
    line-height: 1;
    text-align: initial;
    pointer-events: auto; /* 改为auto，允许空白区域接收鼠标事件 */
    user-select: text;
}

.textLayer > span {
    color: transparent;
    position: absolute;
    white-space: pre;
    cursor: text;
    transform-origin: 0% 0%;
    pointer-events: all;
    /* 不再使用contain，允许选择跨越多个span */
    user-select: text;
}

/* 防止空白区域被选中，但允许事件传递 */
.textLayer > br,
.textLayer:empty,
.textLayer > span:empty {
    user-select: none;
    /* 不再禁用pointer-events */
}

/* 优化选择时的视觉效果 */
.textLayer ::selection {
    /* background-color: var(--selection-color, rgba(153, 193, 218, 0.4)) !important; */
    mix-blend-mode: multiply;
}

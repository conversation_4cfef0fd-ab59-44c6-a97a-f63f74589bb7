# 标签管理系统 - 重构版本

标签和标签组的增删改功能已从拖拽容器中分离，实现了更好的代码组织和维护性。

## 🏗️ 架构设计

### 1. 分层架构
```
┌─────────────────────────────────────────┐
│           UI 组件层                      │
│  TagManagementPage / TagGroup / TagItem │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           Hook 层                        │
│  useTagOperations / useDragAndDrop      │
│  useModalManager                        │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           Store 层                       │
│          useTagStore                     │
└─────────────────────────────────────────┘
```

### 2. 职责分离

| 模块 | 职责 | 文件路径 |
|------|------|----------|
| **Store** | 数据状态管理 | `stores/useTagStore.ts` |
| **CRUD Hook** | 业务逻辑操作 | `hooks/useTagOperations.ts` |
| **拖拽 Hook** | 拖拽交互逻辑 | `hooks/useDragAndDrop.ts` |
| **模态框 Hook** | 弹窗状态管理 | `hooks/useModalManager.ts` |
| **UI 组件** | 用户界面展示 | `TagManagementPageRefactored.tsx` |

## 📦 模块详解

### useTagStore - 状态管理
```typescript
// 统一的数据状态管理
const tagGroups = useTagGroups(); // 获取所有分组
const actions = useTagActions();   // 获取操作方法
const queries = useTagQueries();   // 获取查询方法

// 示例：添加分组
const groupId = actions.addGroup({ title: '新分组', tags: [] });
```

**特性:**
- ✅ 使用 Zustand + Immer 管理状态
- ✅ 支持 Redux DevTools
- ✅ 自动持久化到 localStorage
- ✅ 高性能的选择器模式

### useTagOperations - CRUD 操作
```typescript
// 业务逻辑处理
const operations = useTagOperations();

// 创建分组（含验证 + Toast）
await operations.createGroup('新分组名称');

// 移动标签到其他分组
await operations.moveTagToGroup('tag-id', 'target-group-id');

// 批量删除标签
await operations.deleteMultipleTags([
  { groupId: 'group1', tagId: 'tag1' },
  { groupId: 'group1', tagId: 'tag2' }
]);
```

**特性:**
- ✅ 完整的输入验证
- ✅ 自动 Toast 提示
- ✅ 错误处理和恢复
- ✅ 支持批量操作
- ✅ TypeScript 类型安全

### useDragAndDrop - 拖拽逻辑
```typescript
// 拖拽交互管理
const { dragState, isDragging, getDragStyles } = useDragAndDrop({
  enableGroupReordering: true,
  enableTagReordering: true,
  enableCrossGroupMove: true,
  showDropIndicators: true,
});

// 获取拖拽样式
const dragClasses = getDragStyles('item-id', 'tag');
```

**特性:**
- ✅ 基于 Pragmatic Drag and Drop
- ✅ 支持配置化的拖拽规则
- ✅ 自动碰撞检测
- ✅ 视觉反馈和状态管理
- ✅ 触觉反馈支持（移动端）

### useModalManager - 模态框管理
```typescript
// 模态框状态管理
const {
  modalState,
  openCreateGroup,
  openEditTag,
  closeModal,
  getModalTitle,
  validateForm
} = useModalManager();

// 打开编辑标签模态框
openEditTag('group-id', 'tag-id');
```

**特性:**
- ✅ 统一的模态框状态管理
- ✅ 自动表单数据加载
- ✅ 内置表单验证
- ✅ 类型安全的 API

## 🚀 使用方法

### 1. 基础使用
```typescript
import { TagManagementPage } from '@/components/tag-management';

function App() {
  return <TagManagementPage />;
}
```

### 2. 高级定制
```typescript
import { 
  useTagStore, 
  useTagOperations, 
  useDragAndDrop 
} from '@/components/tag-management';

function CustomTagPage() {
  const tagGroups = useTagGroups();
  const operations = useTagOperations();
  const { dragState } = useDragAndDrop();
  
  // 自定义逻辑...
}
```

### 3. 独立使用组件
```typescript
import { TagGroup, TagItem } from '@/components/tag-management';

function CustomComponent() {
  return (
    <TagGroup
      id="group-1"
      title="我的标签"
      tags={[{ id: 'tag-1', name: '重要' }]}
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  );
}
```

## 🔧 配置选项

### 拖拽配置
```typescript
const dragConfig = {
  enableGroupReordering: true,   // 启用分组排序
  enableTagReordering: true,     // 启用标签排序
  enableCrossGroupMove: true,    // 启用跨分组移动
  showDropIndicators: true,      // 显示放置指示器
  hapticFeedback: false,         // 触觉反馈（移动端）
};
```

### Store 配置
```typescript
// 重置到初始数据
actions.resetToMockData();

// 批量设置数据
actions.setTagGroups(newTagGroups);
```

## 📊 性能优化

### 1. 选择器模式
```typescript
// ✅ 推荐：使用选择器避免不必要的重渲染
const groupsCount = useGroupsCount();
const tagsCount = useTagsCount('group-id');

// ❌ 避免：直接使用整个 store
const store = useTagStore(); // 会导致过度渲染
```

### 2. 批量操作
```typescript
// ✅ 推荐：使用批量操作
await operations.deleteMultipleTags(tagList);

// ❌ 避免：循环单个操作
for (const tag of tagList) {
  await operations.deleteTag(tag.groupId, tag.id);
}
```

### 3. 记忆化
所有 Hook 内部已使用 `useCallback` 和 `useMemo` 进行优化。

## 🧪 测试支持

### 1. 单元测试
```typescript
import { renderHook } from '@testing-library/react';
import { useTagOperations } from './hooks/useTagOperations';

test('should create group', async () => {
  const { result } = renderHook(() => useTagOperations());
  const groupId = await result.current.createGroup('测试分组');
  expect(groupId).toBeDefined();
});
```

### 2. 集成测试
```typescript
import { render, screen } from '@testing-library/react';
import { TagManagementPage } from './TagManagementPageRefactored';

test('should display tag groups', () => {
  render(<TagManagementPage />);
  expect(screen.getByText('标签管理')).toBeInTheDocument();
});
```

## 🔄 迁移指南

### 从旧版本迁移
```typescript
// 旧版本
import TagManagementPage from './TagManagementPage';

// 新版本
import { TagManagementPage } from '@/components/tag-management';
```

### API 变更
- ✅ 所有 CRUD 操作现在返回 Promise
- ✅ 错误处理统一通过 Toast 显示
- ✅ 拖拽逻辑从组件中分离到独立 Hook
- ✅ 模态框状态管理集中化

## 🛠️ 扩展性

### 添加新功能
1. **新的操作**: 在 `useTagOperations` 中添加
2. **新的拖拽规则**: 在 `useDragAndDrop` 中配置
3. **新的模态框**: 在 `useModalManager` 中扩展
4. **新的数据查询**: 在 `useTagStore` 中添加选择器

### 自定义 Hook
```typescript
// 自定义搜索 Hook
export const useTagSearch = () => {
  const queries = useTagQueries();
  const [searchTerm, setSearchTerm] = useState('');
  
  const results = useMemo(() => 
    queries.searchTags(searchTerm), 
    [searchTerm, queries]
  );
  
  return { searchTerm, setSearchTerm, results };
};
```

## 📋 最佳实践

1. **单一职责**: 每个 Hook 只负责一个特定功能
2. **类型安全**: 使用 TypeScript 确保类型安全
3. **错误处理**: 所有异步操作都包含错误处理
4. **用户反馈**: 使用 Toast 提供即时反馈
5. **性能优化**: 使用选择器模式避免不必要渲染
6. **可测试性**: 每个模块都可以独立测试

## 🔍 故障排除

### 常见问题

**Q: 拖拽不生效？**
A: 检查是否正确调用了 `useDragAndDrop` Hook，并确保组件使用了 pragmatic-drag-and-drop 的拖拽配置。

**Q: 状态不更新？**
A: 确保使用选择器 Hook（如 `useTagGroups`）而不是直接访问 store。

**Q: Toast 消息不显示？**
A: 确保应用根组件包含了 `sonner` 的 `Toaster` 组件。

---

通过这种分离式架构，标签管理系统现在具有更好的可维护性、可测试性和扩展性！
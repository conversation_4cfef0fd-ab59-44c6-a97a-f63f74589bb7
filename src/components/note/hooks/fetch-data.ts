import {getNotebook} from "@/api/notebook";
import {getWsId} from "@/tools/params.ts";
import {useCallback, useEffect} from "react";
import {useNoteStore} from "@/store/note-store.ts";
import {useNoteFocus} from "@/components/note/hooks/note-focus.ts";

export const useFetchData = () => {
    const wid = getWsId()
    const {setInsertContent} = useNoteStore((state) => ({
        setInsertContent: state.setInsertContent,
    }))
    const {focusToEnd} = useNoteFocus()
    const fetchData = useCallback(async () => {
        try {
            if (!wid) return
            const res = await getNotebook(wid)
            return res.data.content
        } catch (error) {
            console.log("获取记事本失败，", error)
            return ""
        }
    }, [getNotebook])
    // 处理消息
    const handleMessage = useCallback(async (event: MessageEvent) => {
        try {
            let data = {}
            const wsId = getWsId()
            if (!event.data.data || !event.data.data.wid) {
                return
            }
            if (wsId !== event.data.data.wid) {
                return
            }
            switch (event.data.type) {
                // 更新笔记状态
                case 'UPDATE_NOTES_STORE_REQUEST':
                    const noteState = useNoteStore.getState()
                    focusToEnd()
                    setInsertContent((noteState.content.trim().length > 0 ? "\n" : "") + event.data.data.content)
                    break
                default :
                    return
            }
            console.log(event.data.type, event, data)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 200,
                data: data
            }, '*')
        } catch (e) {
            console.log(event.data.type, event, e)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 500,
                message: e
            }, '*')
        }
    }, [setInsertContent])
    useEffect(() => {
        window.addEventListener('message', handleMessage)
        return () => {
            window.removeEventListener('message', handleMessage)
        }
    }, []);
    return {
        fetchData
    }
}
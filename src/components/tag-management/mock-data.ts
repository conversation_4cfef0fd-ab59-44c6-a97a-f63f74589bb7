import { Tag } from './TagGroup';

export interface TagGroupData {
  id: string;
  title: string;
  tags: Tag[];
}

export const mockTagGroups: TagGroupData[] = [
  {
    id: 'group-1',
    title: '技术栈',
    tags: [
      { id: 'tag-1-1', name: 'React' },
      { id: 'tag-1-2', name: 'TypeScript' },
      { id: 'tag-1-3', name: 'Node.js' },
      { id: 'tag-1-4', name: 'shadcn/ui' },
    ]
  },
  {
    id: 'group-2',
    title: '项目类型',
    tags: [
      { id: 'tag-2-1', name: '前端项目' },
      { id: 'tag-2-2', name: '后端项目' },
      { id: 'tag-2-3', name: '全栈项目' },
    ]
  },
  {
    id: 'group-3',
    title: '优先级',
    tags: [
      { id: 'tag-3-1', name: '高优先级' },
      { id: 'tag-3-2', name: '中优先级' },
      { id: 'tag-3-3', name: '低优先级' },
    ]
  },
  {
    id: 'group-4',
    title: '状态标签',
    tags: [
      { id: 'tag-4-1', name: '进行中' },
      { id: 'tag-4-2', name: '已完成' },
      { id: 'tag-4-3', name: '已暂停' },
      { id: 'tag-4-4', name: '已取消' },
    ]
  },
  {
    id: 'group-5',
    title: '空分组示例',
    tags: []
  }
];

export default mockTagGroups; 
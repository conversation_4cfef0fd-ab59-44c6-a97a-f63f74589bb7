import {useCallback, useEffect} from "react";
import {usePdfStore} from "@/store/pdf-store.ts";
import {deletePdf} from "@/api/pdf";
import {PdfViewer} from "@/components/pdf/components/highlight/PdfViewer.tsx";
import {getWsId} from "@/tools/params.ts";

export const useMenu = () => {
    const {setActiveAid, setTabItems, removePdfs, addPdfs} = usePdfStore((state) => ({
        setActiveAid: state.setActiveAid,
        setTabItems: state.setTabItems,
        removePdfs: state.removePdfs,
        addPdfs: state.addPdfs
    }))
    const onMenuClick = useCallback((aid: string) => {
        const state = usePdfStore.getState()
        if (state.activeAid === aid) {
            return
        }
        setActiveAid(aid)
        if (state.tabItems?.find((item) => item.key === aid)) {
            return
        }
        const items = [...(state.tabItems || []), {
            key: aid,
            label: state.pdfs.get(aid)!.filename,
            children: <PdfViewer aid={aid}/>
        }]
        setTabItems(items)
    }, [setActiveAid, setTabItems])
    const onMenuDelete = useCallback(async (aid: string, deleteNode: boolean) => {
        try {
            await deletePdf({
                aid,
                type: deleteNode ? 2 : 1
            })
            const state = usePdfStore.getState()
            const tabItems = state.tabItems || []
            const newTabItems = tabItems.filter((item) => item.key !== aid)
            // 删除附件
            removePdfs(aid)
            setTabItems(newTabItems)
            if (state.activeAid === aid) {
                const index = tabItems.findIndex((item) => item.key === aid)
                if (newTabItems.length > 0) {
                    setActiveAid(newTabItems[index === tabItems.length - 1 ? index - 1 : index].key)
                } else {
                    setActiveAid("")
                }
            }
        } catch (e) {
            console.log("删除附件失败", e)
        }
    }, [deletePdf, removePdfs, setTabItems, setActiveAid])
    // 处理消息
    const handleMessage = useCallback(async (event: MessageEvent) => {
        try {
            let data = {}
            const wsId = getWsId()
            if (!event.data.data || !event.data.data.wid) {
                return
            }
            if (wsId !== event.data.data.wid) {
                return
            }
            switch (event.data.type) {
                // 更新菜单状态
                case 'UPDATE_PDFS_STORE_REQUEST':
                    addPdfs(event.data.data.attach.reduce((map: any, item: any) => {
                        map.set(item.aid, {
                            aid: item.aid,
                            filename: item.fname,
                            url: item.url,
                        })
                        return map
                    }, new Map()))
                    break
                default :
                    return
            }
            console.log(event.data.type, event, data)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 200,
                data: data
            }, '*')
        } catch (e) {
            console.log(event.data.type, event, e)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 500,
                message: e
            }, '*')
        }
    }, [])
    useEffect(() => {
        window.addEventListener('message', handleMessage)
        return () => {
            window.removeEventListener('message', handleMessage)
        }
    }, []);
    return {
        onMenuClick,
        onMenuDelete
    }
}
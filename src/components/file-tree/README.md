# File Tree 组件

完整的文件树管理组件，支持新建文件夹、重命名等完整文件系统操作。

## 组件说明

### FileTreePanel
基于react-complex-tree实现的完整文件树组件，支持：
- 文件夹层次结构管理
- 新建文件夹/文件
- 文件重命名
- 拖拽排序
- 搜索过滤
- 完整的右键菜单

## 快速开始

### 安装依赖
```bash
npm install react-complex-tree lucide-react
```

### 基本使用

```tsx
import { FileTreePanel, FileTreeNode } from '@/components/file-tree';
import '@/components/file-tree/FileTreePanel.css';

const fileData: FileTreeNode[] = [
  {
    id: 'folder-1',
    title: '文档',
    isDirectory: true,
    path: '/文档',
    children: ['pdf-1']
  },
  {
    id: 'pdf-1',
    title: 'document.pdf',
    isDirectory: false,
    path: '/文档/document.pdf',
    size: 1024000,
    data: { aid: 'pdf-1' }
  }
];

function PdfFileManager() {
  const handleFileSelect = (node: FileTreeNode) => {
    console.log('Selected:', node.title, node.path);
  };

  const handleFileCreate = (parentPath: string, type: 'file' | 'folder') => {
    console.log('Create:', type, 'in', parentPath);
  };

  const handleFileDelete = (path: string) => {
    console.log('Delete:', path);
  };

  const handleFileRename = (oldPath: string, newPath: string) => {
    console.log('Rename:', oldPath, '→', newPath);
  };

  return (
    <FileTreePanel
      initialData={fileData}
      onFileSelect={handleFileSelect}
      onFileCreate={handleFileCreate}
      onFileDelete={handleFileDelete}
      onFileRename={handleFileRename}
      searchable={true}
      showHiddenFiles={false}
    />
  );
}
```

## API

### FileTreePanel Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `initialData` | `FileTreeNode[]` | 必需 | 文件树数据 |
| `onFileSelect` | `(node: FileTreeNode) => void` | - | 文件选择回调 |
| `onFileCreate` | `(path: string, type: 'file' \| 'folder') => void` | - | 文件/文件夹创建回调 |
| `onFileDelete` | `(path: string) => void` | - | 文件删除回调 |
| `onFileRename` | `(oldPath: string, newPath: string) => void` | - | 文件重命名回调 |
| `searchable` | `boolean` | `true` | 是否显示搜索框 |
| `showHiddenFiles` | `boolean` | `false` | 是否显示隐藏文件 |
| `className` | `string` | `''` | 自定义样式类名 |

### FileTreeNode 接口

```typescript
interface FileTreeNode {
  id: string;                    // 唯一标识
  title: string;                 // 显示名称
  isDirectory?: boolean;         // 是否为文件夹
  path?: string;                 // 文件路径
  size?: number;                 // 文件大小（字节）
  lastModified?: Date;           // 最后修改时间
  isHidden?: boolean;            // 是否为隐藏文件
  children?: string[];           // 子节点ID数组
  data?: any;                    // 自定义数据
  metadata?: {                   // 元数据
    tags?: string[];             // 标签
    color?: string;              // 颜色
    starred?: boolean;           // 是否收藏
  };
}
```

## 功能特性

- ✅ **完整文件系统**: 支持文件夹层次结构
- ✅ **新建操作**: 右键新建文件夹/文件
- ✅ **重命名功能**: 双击或右键重命名
- ✅ **拖拽排序**: 内置拖拽功能
- ✅ **搜索过滤**: 实时搜索文件/文件夹
- ✅ **隐藏文件**: 显示/隐藏隐藏文件
- ✅ **右键菜单**: 完整的上下文菜单
- ✅ **快捷键**: 键盘快捷键支持
- ✅ **主题支持**: 亮色/暗色主题

## 文件结构

```
src/components/file-tree/
├── FileTreePanel.tsx        # 主要的文件树组件
├── FileTreePanel.css        # 文件树样式
├── PdfFileTreeExample.tsx   # PDF文件管理示例
├── index.ts                 # 导出文件
└── README.md               # 文档
```

## 使用场景

FileTreePanel 适用于以下场景：

- ✅ **PDF文件管理**: 管理大量PDF文件和文件夹
- ✅ **文档分类**: 按项目、日期等创建文件夹分类
- ✅ **团队协作**: 多人共享的文件系统
- ✅ **知识管理**: 构建个人或团队的知识库
- ✅ **内容管理**: CMS系统中的文件管理模块

## 主要优势

- 🗂️ **完整文件系统**: 真正的文件夹层次结构
- ➕ **创建功能**: 一键新建文件夹和文件
- ✏️ **重命名支持**: 双击即可重命名
- 🔍 **强大搜索**: 实时搜索所有文件和文件夹
- 🎯 **专业级UI**: 基于成熟的react-complex-tree库
- 🎨 **主题支持**: 支持亮色和暗色主题

## 示例代码

### 完整的PDF文件管理器

```tsx
import { FileTreePanel, FileTreeNode } from '@/components/file-tree';
import '@/components/file-tree/FileTreePanel.css';

// 在你的PDF Menu组件中
export const PdfMenu = () => {
  const [fileData, setFileData] = useState<FileTreeNode[]>([
    {
      id: 'folder-docs',
      title: '我的文档',
      isDirectory: true,
      path: '/我的文档',
      children: ['pdf-1', 'pdf-2']
    },
    {
      id: 'pdf-1',
      title: '项目报告.pdf',
      isDirectory: false,
      path: '/我的文档/项目报告.pdf',
      size: 2048000,
      data: { aid: 'pdf-1', type: 'pdf' }
    }
  ]);

  const handleFileSelect = (node: FileTreeNode) => {
    if (!node.isDirectory && node.data?.aid) {
      // 打开PDF文件
      openPdf(node.data.aid);
    }
  };

  const handleFileCreate = (parentPath: string, type: 'file' | 'folder') => {
    if (type === 'folder') {
      // 创建新文件夹
      createFolder(parentPath);
    } else {
      // 触发文件上传
      triggerFileUpload();
    }
  };

  return (
    <FileTreePanel
      initialData={fileData}
      onFileSelect={handleFileSelect}
      onFileCreate={handleFileCreate}
      onFileDelete={handleFileDelete}
      onFileRename={handleFileRename}
      searchable={true}
    />
  );
};
```

### 参考示例

```tsx
// 查看完整的文件管理示例
import { PdfFileTreeExample } from '@/components/file-tree';

<PdfFileTreeExample />
```

主要区别：
1. 数据格式更简单（扁平数组 vs 复杂树结构）
2. API更简洁（3个主要props vs 10+个props）
3. 依赖更少（仅lucide-react vs react-complex-tree等）
4. 体积更小（~2KB vs ~12KB） 
import {<PERSON>le, Position, NodeResizer} from "@xyflow/react";
import {memo, useCallback, useEffect, useState} from "react";
import {nodeData} from "@/store/flow-store.ts";
import {NodeToolbar} from "@/components/flow/node/NodeToolbar.tsx";
import NodeTitle from "@/components/flow/node/NodeTitle.tsx";
import NodeContent from "@/components/flow/node/NodeContent.tsx";
import {useNodeResize} from "@/components/flow/hooks/node-resize";

export default memo(({data, id, selected}: {
    data: nodeData,
    id: string
    selected: boolean
}) => {
    const [editStates, setEditStates] = useState({
        title: 0,
        content: 0
    });
    
    const { handleNodeResize, handleNodeResizeRealtime } = useNodeResize();

    useEffect(() => {
        if (!selected) {
            setEditStates({
                title: 0,
                content: 0
            });
        }
    }, [selected]);

    const handleTitleEditMode = useCallback((mode: number) => {
        setEditStates({
            content: 0,
            title: mode
        });
    }, [setEditStates])

    const handleContentEditMode = useCallback((mode: number) => {
        setEditStates({
            title: 0,
            content: mode
        });
    }, [setEditStates])

    // 判断是否处于编辑状态
    const isEditing = editStates.title !== 0 || editStates.content !== 0;

    return (
        <>
            {/* 节点大小调整器 - 在选中时或编辑状态下显示 */}
            {/* && !isEditing */}
            {(selected ) && (
                <NodeResizer
                    minWidth={200}
                    minHeight={100}
                    isVisible={selected || isEditing}
                    onResize={(event, params) => {
                        // 拖拽过程中实时更新，无需调用API
                        handleNodeResizeRealtime(id, params.width, params.height);
                    }}
                    onResizeEnd={(event, params) => {
                        // 拖拽结束时调用API保存
                        handleNodeResize(id, params.width, params.height);
                    }}
                />
            )}
            
            <div 
                className={`bg-white h-full rounded-lg flex flex-col ${selected || isEditing ? "nowheel" : ""}`}>
                <div className="flex-shrink-0">
                    <NodeTitle
                        nid={id}
                        editMode={editStates.title}
                        setEditMode={handleTitleEditMode}
                        data={data}
                    />
                </div>
                <div className="flex-1 overflow-hidden"
                >
                    <NodeContent
                        nid={id}
                        content={data.content}
                        editMode={editStates.content}
                        setEditMode={handleContentEditMode}
                    />
                </div>
                <Handle
                    type="source"
                    position={Position.Right}
                />
                <Handle
                    type="target"
                    position={Position.Left}
                />
            </div>
            
            {(selected && !isEditing) && (
                <NodeToolbar nodeData={data} id={id} setEditMode={handleTitleEditMode}/>
            )}
        </>
    );
})
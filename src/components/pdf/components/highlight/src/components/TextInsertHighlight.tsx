import React, { CSSProperties, useState, useRef, useEffect, use } from "react";
import "@/components/pdf/components/highlight/src/style/TextHighlight.css";
import { LTWHP, usePdfHighlighterContext, ViewportHighlight } from "..";
import { modeType, usePdfStore } from "@/store/pdf-store";
// import type {ViewportHighlight, LTWHP} from "../types";

/**
 * The props type for {@link TextInsertHighlight}.
 *
 * @category Component Properties
 */
export interface TextInsertHighlightProps {
    /**
     * The highlight to render.
     */
    highlight: ViewportHighlight;

    /**
     * Whether the highlight is currently being scrolled to.
     */
    isScrolledTo: boolean;

    /**
     * Callback triggered when the highlight is clicked.
     *
     * @param event - The mouse event.
     */
    onMouseDown(event: React.MouseEvent): void;

    /**
     * Optional style props for the highlight.
     */
    style?: CSSProperties;

    /**
     * Callback triggered when the highlight position changes (after dragging).
     * 
     * @param boundingRect - The new bounding rectangle of the highlight.
     */
    onChange?: (boundingRect: LTWHP) => void;
    onTextChange?: (text: string) => void;
    /**
     * Callback triggered when the user starts editing (dragging) the highlight.
     */
    onEditStart?: () => void;

    /**
     * The bounds element for dragging.
     */
    bounds?: HTMLElement;
}

// 检查坐标是否合理，如果不合理则使用默认值
const isValidCoordinate = (value: number) => {
    return !isNaN(value) && isFinite(value) && value >= 0 && value < 10000;
};

/**
 * A component that renders a text insert highlight in a PDF document.
 * This is used for inserted text annotations that are not part of the original document.
 *
 * @category Component
 */
export const TextInsertHighlight = ({
    highlight,
    isScrolledTo,
    onMouseDown,
    style,
    onChange,
    onEditStart,
    bounds,
    onTextChange,
}: TextInsertHighlightProps) => {
    const { position } = highlight;
    const { boundingRect } = position;

    const [{
        left, top
    }, setPosition] = useState(() => {

        // 确保坐标值在合理范围内
        const left = isValidCoordinate(boundingRect.left) ? boundingRect.left : 0;
        const top = isValidCoordinate(boundingRect.top) ? boundingRect.top : 0;

        return { left, top }
    })

    // // 确保坐标值在合理范围内
    const _left = isValidCoordinate(boundingRect.left) ? boundingRect.left : 0;
    const _top = isValidCoordinate(boundingRect.top) ? boundingRect.top : 0;

    const { mode } = usePdfStore((state) => ({
        mode: state.mode
    }))

    useEffect(() => {
        setPosition(() => ({
            left: _left,
            top: _top
        }))
    }, [_left, _top])

    // 确保宽度和高度在合理范围内
    const width = isValidCoordinate(boundingRect.width) && boundingRect.width > 0 ?
        boundingRect.width : 150;
    const height = isValidCoordinate(boundingRect.height) && boundingRect.height > 0 ?
        boundingRect.height : 40;

    // 拖动状态
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
    const highlightRef = useRef<HTMLDivElement>(null);

    // 处理拖动开始
    const handleDragStart = (e: React.MouseEvent) => {
        // 如果没有onChange回调，不启用拖动
        if (!onChange) return;

        // 阻止事件冒泡，避免触发onMouseDown
        e.stopPropagation();
        e.preventDefault();

        // 通知开始编辑
        onEditStart && onEditStart();

        // 设置拖动状态
        setIsDragging(true);
        setDragStart({ x: e.clientX, y: e.clientY });
        setDragOffset({ x: 0, y: 0 });
    };

    // 处理拖动过程
    useEffect(() => {
        if (!isDragging) return;

        const handleDragMove = (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();

            setDragOffset({
                x: e.clientX - dragStart.x,
                y: e.clientY - dragStart.y
            });
        };

        const handleDragEnd = (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();

            // 先更新位置，再重置拖拽状态
            if (onChange && (dragOffset.x !== 0 || dragOffset.y !== 0)) {
                const newBoundingRect: LTWHP = {
                    left: left + dragOffset.x,
                    top: top + dragOffset.y,
                    width: width,
                    height: height,
                    pageNumber: boundingRect.pageNumber
                };

                // 调用onChange回调
                onChange(newBoundingRect);
            }

            // 重置拖拽状态
            setIsDragging(false);
            setPosition({
                left: left + dragOffset.x,
                top: top + dragOffset.y
            })
            setDragOffset({ x: 0, y: 0 });
        };

        document.addEventListener('mousemove', handleDragMove, { capture: true });
        document.addEventListener('mouseup', handleDragEnd, { capture: true });

        return () => {
            document.removeEventListener('mousemove', handleDragMove, { capture: true });
            document.removeEventListener('mouseup', handleDragEnd, { capture: true });
        };
    }, [isDragging, dragStart, setPosition, onChange, left, top, width, height, boundingRect.pageNumber, dragOffset]);


    const displayLeft = left + dragOffset.x;
    const displayTop = top + dragOffset.y;

    const scrollClass = `${isScrolledTo ? "TextInsertHighlight--scrolledTo" : ""}`
    const dragClass = `${isDragging ? "TextInsertHighlight--dragging" : ""} `
    const inTextInsert = mode === modeType.textInsert

    return (
        <div
            className={`TextInsertHighlight ${scrollClass} ${dragClass}`}
            style={{
                ...style,
                pointerEvents: inTextInsert ? "auto" : "none"
            }}
            ref={highlightRef}
            data-highlight-id={highlight.id}
        >
            <div className="TextHighlight__parts " >
                {/* 隐藏滚动条的样式 */}
                <style>{`
                    .TextInsertHighlight__part::-webkit-scrollbar { display: none; width: 0; height: 0; }
                `}</style>
                <div
                    style={{
                        left: displayLeft,
                        top: displayTop,
                        width,
                        height,
                        position: "absolute",
                        border: "2px solid #1890ff",
                        borderRadius: "4px",
                        padding: "8px",
                        boxShadow: isDragging ? "0 4px 8px rgba(0,0,0,0.3)" : "0 2px 8px rgba(0,0,0,0.15)",
                        fontSize: "14px",
                        fontFamily: "inherit",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "flex-start",
                        justifyContent: "flex-start",
                        zIndex: isDragging ? 2000 : 1000, // 拖拽时提高z-index
                        overflow: "auto",
                        textOverflow: "initial",
                        whiteSpace: "pre-wrap",
                        cursor: onChange ? "move" : "pointer",
                        userSelect: "none",
                        transition: isDragging ? "none" : "box-shadow 0.2s ease",
                        pointerEvents: inTextInsert ? "auto" : "none", // 确保可以接收鼠标事件
                        // 隐藏滚动条但不影响滚动
                        scrollbarWidth: 'none', // Firefox
                        msOverflowStyle: 'none', // IE 10+
                    }}
                    className="TextHighlight__part"
                    onMouseDown={onChange ? handleDragStart : undefined}
                >
                    <TextArea
                        value={highlight.content?.text || ""}
                        onChange={(text) => {
                            onTextChange?.(text)
                        }} />
                </div>
            </div>
        </div>
    );

};

const TextArea = ({
    value,
    onChange
}: {
    value: string
    onChange: (e: string) => void
}) => {
    const [text, setText] = useState(value)
    const { mode } = usePdfStore((state) => ({
        mode: state.mode,
    }))

    return <textarea
        className="w-[200px] h-[100px] rounded p-2 bg-white/50 resize pointer-events-auto"
        style={{
            backdropFilter: 'blur(2px)',
            pointerEvents: mode === modeType.textInsert ? "auto" : "none",
            border: 'none',
            outline: 'none'
        }}
        cols={30} rows={10}
        placeholder="插入文本"
        value={text}
        onChange={(e) => {
            setText(e.target.value)
        }}
        onBlur={(e) => {
            onChange?.(e.target.value)
        }}
        onClick={(e) => {
            e.stopPropagation()
        }}
        onMouseDown={(e) => {
            e.stopPropagation()
        }}
    // autoFocus
    />
}
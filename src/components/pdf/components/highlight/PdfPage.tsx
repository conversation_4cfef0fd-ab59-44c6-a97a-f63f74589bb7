import {Input, Slider} from "antd";
import {useCallback, useEffect, useState} from "react";
import {usePdfStore} from "@/store/pdf-store.ts";

export const PdfPage = ({aid, totalPages}: { aid: string, totalPages: number }) => {
    // 当前页码和总页数
    const [currentPage, setCurrentPage] = useState(1)
    // 页码输入框
    const [pageInput, setPageInput] = useState("1")
    // pdf
    const {highlighterUtils} = usePdfStore((state) => ({
        highlighterUtils: state.pdfs.get(aid)?.pdfHighlighterUtils
    }))

    // 跳转页面
    const goToPreviousPage = useCallback(() => {
        if (currentPage > 1) {
            const newPage = currentPage - 1
            setCurrentPage(newPage)
            setPageInput(newPage.toString())
            const viewer = highlighterUtils?.getViewer()
            if (viewer) {
                viewer.scrollPageIntoView({pageNumber: newPage})
            }
        }
    }, [currentPage, setCurrentPage, setPageInput, highlighterUtils])

    const goToNextPage = useCallback(() => {
        if (currentPage < totalPages) {
            const newPage = currentPage + 1
            setCurrentPage(newPage)
            setPageInput(newPage.toString())
            const viewer = highlighterUtils?.getViewer()
            if (viewer) {
                viewer.scrollPageIntoView({pageNumber: newPage})
            }
        }
    }, [currentPage, setCurrentPage, setPageInput, highlighterUtils])

    // 输入页码
    const handlePageInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setPageInput(e.target.value)
    }, [setPageInput])
    const handlePageInputBlur = useCallback(() => {
        const pageNumber = parseInt(pageInput)
        if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber)
            const viewer = highlighterUtils?.getViewer()
            if (viewer) {
                viewer.scrollPageIntoView({pageNumber})
            }
        } else {
            // 如果输入无效，重置为当前页码
            setPageInput(currentPage.toString())
        }
    }, [pageInput, setCurrentPage, setPageInput, highlighterUtils])
    const handlePageInputKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handlePageInputBlur()
        }
    }, [handlePageInputBlur])

    // 滑块控制页码
    const handleSliderChange = useCallback((value: number) => {
        setCurrentPage(value);
        setPageInput(value.toString())
        const viewer = highlighterUtils?.getViewer()
        if (viewer) {
            viewer.scrollPageIntoView({pageNumber: value})
        }
    }, [setCurrentPage, setPageInput, highlighterUtils])

    //滚动
    useEffect(() => {
        if (!highlighterUtils?.getViewer()) {
            return
        }
        const handlePageChange = () => {
            const viewer = highlighterUtils.getViewer()
            if (!viewer) return
            // 获取当前可见页面
            const visiblePages = viewer._getVisiblePages() as any;
            if (visiblePages && visiblePages.views && visiblePages.views.length > 0) {
                // 使用第一个可见页面作为当前页面
                const newPage = visiblePages.views[0].id;
                setCurrentPage(newPage);
                setPageInput(newPage.toString());
            }
        }
        highlighterUtils.getViewer()!.container.addEventListener("scroll", handlePageChange)
        return () => {
            highlighterUtils.getViewer()!.container.removeEventListener("scroll", handlePageChange)
        }
    }, [highlighterUtils]);
    return (
        <>
            <div
                className="absolute bottom-[5vh] left-1/2 -translate-x-1/2 flex justify-center items-center w-fit mx-auto rounded-[20px] overflow-hidden z-[9999] p-1 gap-2">
                {/*上一页*/}
                <button
                    onClick={goToPreviousPage}
                    disabled={currentPage <= 1}
                    className="bg-white/90 border-none cursor-pointer w-8 h-8 rounded-full flex items-center justify-center shadow-[0_2px_8px_rgba(0,0,0,0.15)] text-gray-600 disabled:text-gray-300 disabled:cursor-not-allowed hover:enabled:text-blue-500 hover:enabled:border hover:enabled:border-blue-500"
                >&lt;</button>
                {/*页码*/}
                <div
                    className="flex items-center bg-white/90 py-1 px-3 rounded-2xl shadow-[0_2px_8px_rgba(0,0,0,0.15)]">
                    <Input
                        value={pageInput}
                        onChange={handlePageInputChange}
                        onBlur={handlePageInputBlur}
                        onKeyPress={handlePageInputKeyPress}
                        className="!w-8 text-center border-none p-0 text-sm bg-transparent focus:shadow-none focus:outline-none focus:ring-2 focus:ring-blue-500"/>
                    <span className="text-gray-600 mx-1 text-[15px]">/</span>
                    <span className="text-gray-600 mx-1 text-[15px]">{totalPages}</span>
                </div>
                {/*下一页*/}
                <button
                    onClick={goToNextPage}
                    disabled={currentPage >= totalPages}
                    className="bg-white/90 border-none cursor-pointer w-8 h-8 rounded-full flex items-center justify-center shadow-[0_2px_8px_rgba(0,0,0,0.15)] text-gray-600 disabled:text-gray-300 disabled:cursor-not-allowed hover:enabled:text-blue-500 hover:enabled:border hover:enabled:border-blue-500"
                >&gt;</button>
            </div>
            {/*滑动条*/}
            <div
                className="absolute bottom-[1vh] left-1/2 -translate-x-1/2 w-full max-w-[500px] rounded-[20px] py-2.5 px-5 z-[10]">
                <Slider
                    min={1}
                    max={totalPages}
                    value={currentPage}
                    onChange={handleSliderChange}
                />
            </div>
        </>
    )
}
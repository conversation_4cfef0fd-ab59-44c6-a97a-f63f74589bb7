import {createFromIconfontCN} from "@ant-design/icons";

export const NODE_WIDTH = 100;
export const NODE_HEIGHT = 100;

export const NODE_WIDTH_MIN = 100;
export const NODE_HEIGHT_MIN = 100;


// export const iconfont_url = '//at.alicdn.com/t/c/font_4724111_f0t3bsz05v9.js'
// export const iconfont_url = '//at.alicdn.com/t/c/font_4858329_wezy2bi4hr.js'
export const iconfont_url = '//at.alicdn.com/t/c/font_4724111_bfghlky30z.js'


export const getPdfWorkerUrl = (version: string) => `//unpkg.com/pdfjs-dist@${version}/build/pdf.worker.min.mjs`

export const IconFontNode = createFromIconfontCN({
    scriptUrl: "//at.alicdn.com/t/c/font_4897988_7n9wsl6hxdl.js",
});

export const IconFontPdf = createFromIconfontCN({
    scriptUrl: iconfont_url,
});
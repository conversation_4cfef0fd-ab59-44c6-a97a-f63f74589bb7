import { useState, useCallback } from 'react';

export type ModalType = 'group' | 'tag';
export type ModalMode = 'create' | 'edit';

export interface ModalState {
  isOpen: boolean;
  type: ModalType;
  mode: ModalMode;
  editingId: string | null;
  currentGroupId: string | null;
}

export interface FormData {
  groupTitle: string;
  tagName: string;
}

export interface ModalActions {
  openCreateGroup: () => void;
  openEditGroup: (groupId: string) => void;
  openCreateTag: (groupId: string) => void;
  openEditTag: (groupId: string, tagId: string) => void;
  closeModal: () => void;
  
  // 表单数据管理
  setGroupTitle: (title: string) => void;
  setTagName: (name: string) => void;
  resetForm: () => void;
  
  // 便捷方法
  isGroupModal: boolean;
  isTagModal: boolean;
  isCreateMode: boolean;
  isEditMode: boolean;
}

const initialModalState: ModalState = {
  isOpen: false,
  type: 'group',
  mode: 'create',
  editingId: null,
  currentGroupId: null,
};

const initialFormData: FormData = {
  groupTitle: '',
  tagName: '',
};

export const useModalManager = () => {
  const [modalState, setModalState] = useState<ModalState>(initialModalState);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  
  // 基础模态框操作
  const openModal = useCallback((
    type: ModalType,
    mode: ModalMode,
    editingId?: string,
    groupId?: string,
    initialData?: { title?: string; name?: string }
  ) => {
    setModalState({
      isOpen: true,
      type,
      mode,
      editingId: editingId || null,
      currentGroupId: groupId || null,
    });
    
    // 如果是编辑模式且提供了初始数据，加载现有数据
    if (mode === 'edit' && initialData) {
      if (type === 'group' && initialData.title) {
        setFormData(prev => ({ ...prev, groupTitle: initialData.title || '' }));
      } else if (type === 'tag' && initialData.name) {
        setFormData(prev => ({ ...prev, tagName: initialData.name || '' }));
      }
    } else {
      // 创建模式重置表单
      setFormData(initialFormData);
    }
  }, []);
  
  const closeModal = useCallback(() => {
    setModalState(initialModalState);
    setFormData(initialFormData);
  }, []);
  
  // 具体的模态框打开方法
  const openCreateGroup = useCallback(() => {
    openModal('group', 'create');
  }, [openModal]);
  
  const openEditGroup = useCallback((groupId: string, currentTitle?: string) => {
    openModal('group', 'edit', groupId, undefined, { title: currentTitle });
  }, [openModal]);
  
  const openCreateTag = useCallback((groupId: string) => {
    openModal('tag', 'create', undefined, groupId);
  }, [openModal]);
  
  const openEditTag = useCallback((groupId: string, tagId: string, currentName?: string) => {
    openModal('tag', 'edit', tagId, groupId, { name: currentName });
  }, [openModal]);
  
  // 表单数据管理
  const setGroupTitle = useCallback((title: string) => {
    setFormData(prev => ({ ...prev, groupTitle: title }));
  }, []);
  
  const setTagName = useCallback((name: string) => {
    setFormData(prev => ({ ...prev, tagName: name }));
  }, []);
  
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
  }, []);
  
  // 计算属性
  const isGroupModal = modalState.type === 'group';
  const isTagModal = modalState.type === 'tag';
  const isCreateMode = modalState.mode === 'create';
  const isEditMode = modalState.mode === 'edit';
  
  // 获取当前模态框标题
  const getModalTitle = useCallback(() => {
    const action = isCreateMode ? '新建' : '编辑';
    const entity = isGroupModal ? '分组' : '标签';
    return `${action}${entity}`;
  }, [isCreateMode, isGroupModal]);
  
  // 获取当前表单字段名称
  const getFieldLabel = useCallback(() => {
    return isGroupModal ? '分组名称' : '标签名称';
  }, [isGroupModal]);
  
  // 获取当前表单字段值
  const getCurrentValue = useCallback(() => {
    return isGroupModal ? formData.groupTitle : formData.tagName;
  }, [isGroupModal, formData]);
  
  // 获取当前表单字段占位符
  const getPlaceholder = useCallback(() => {
    const entity = isGroupModal ? '分组' : '标签';
    return `请输入${entity}名称`;
  }, [isGroupModal]);
  
  // 验证表单
  const validateForm = useCallback(() => {
    const value = getCurrentValue().trim();
    if (!value) {
      return { isValid: false, message: `${getFieldLabel()}不能为空` };
    }
    if (value.length > (isGroupModal ? 50 : 30)) {
      const maxLength = isGroupModal ? 50 : 30;
      return { isValid: false, message: `${getFieldLabel()}不能超过${maxLength}个字符` };
    }
    return { isValid: true };
  }, [getCurrentValue, getFieldLabel, isGroupModal]);
  
  return {
    // 状态
    modalState,
    formData,
    
    // 计算属性
    isGroupModal,
    isTagModal,
    isCreateMode,
    isEditMode,
    
    // 操作方法
    openCreateGroup,
    openEditGroup,
    openCreateTag,
    openEditTag,
    closeModal,
    
    // 表单管理
    setGroupTitle,
    setTagName,
    resetForm,
    
    // 辅助方法
    getModalTitle,
    getFieldLabel,
    getCurrentValue,
    getPlaceholder,
    validateForm,
  };
};

// 辅助 Hook：用于表单输入处理 (已移除以避免循环依赖)
import React, { memo, useMemo, useState, useCallback, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Typography, Button } from "antd";
import "katex/dist/katex.min.css";
import "./style/markdown.css";
import { processContent } from "@/tools/parseMarkdown";
import { useSelectionHook } from "@/pages/workspace/hooks/selection";

interface MarkdownProps {
  content: string;
  rows?: number; // 默认显示行数
  ellipsis?: boolean; // 是否启用省略功能
}

const PLUGINS = {
  remarkPlugins: [remarkGfm, remarkMath],
  rehypePlugins: [rehypeKatex]
};

const COPY_TOOLTIPS: [string, string] = ['复制', '复制成功'];

// 优化代码块组件，使用 memo 减少不必要的重渲染
const CodeBlock = memo(({ inline, className, children, ...props }: any) => {
  if (inline) {
    return <code className={className} {...props}>{children}</code>;
  }

  const match = /language-(\w+)/.exec(className || "");
  if (!match) {
    return <code className={className} {...props}>{children}</code>;
  }

  const language = match[1];
  const code = String(children).replace(/\n$/, "");
  
  // 构建包含markdown格式的完整代码块
  const markdownCodeBlock = `\`\`\`${language}\n${code}\n\`\`\``;

  return (
    <div className="code-block-wrapper"
    >
      <div className="code-block-header">
        <span>{language}</span>
        <Typography.Paragraph
          copyable={{ 
            text: markdownCodeBlock,
            tooltips: COPY_TOOLTIPS
          }}
          style={{ margin: 0, display: 'inline' }}
        />
      </div>
      <SyntaxHighlighter style={oneDark} language={language} PreTag="div" {...props}>
        {code}
      </SyntaxHighlighter>
    </div>
  );
});

CodeBlock.displayName = "CodeBlock";

// 提取代码块分析逻辑为独立函数
const analyzeCodeBlocks = (lines: string[]) => {
  const codeBlockStarts: number[] = [];
  const codeBlockEnds: number[] = [];
  let inCodeBlock = false;
  let blockStartLine = -1;

  lines.forEach((line, index) => {
    const codeBlockMatch = line.match(/^```(\w+)?/);
    if (codeBlockMatch) {
      if (!inCodeBlock) {
        inCodeBlock = true;
        blockStartLine = index;
      } else {
        inCodeBlock = false;
        codeBlockStarts.push(blockStartLine);
        codeBlockEnds.push(index);
      }
    }
  });

  if (inCodeBlock) {
    codeBlockStarts.push(blockStartLine);
    codeBlockEnds.push(lines.length - 1);
  }

  return { codeBlockStarts, codeBlockEnds };
};

export const Markdown: React.FC<MarkdownProps> = memo(({ 
  content, 
  rows = 3, 
  ellipsis = true 
}) => {
  const [expanded, setExpanded] = useState(true);
  
  // 缓存处理后的内容
  const processedContent = useMemo(() => processContent(content), [content]);
  
  // 缓存处理行数
  const lineCount = useMemo(() => processedContent.split('\n').length, [processedContent]);
  
  // 判断是否有实际内容
  const hasContent = useMemo(() => processedContent.trim().length > 0, [processedContent]);
  
  // 使用 useCallback 优化事件处理函数
  const toggleExpand = useCallback(() => setExpanded(prev => !prev), []);
  
  const contentWithToggle = useMemo(() => {
    if (!ellipsis || lineCount <= rows || expanded) {
      return processedContent;
    }
    
    const lines = processedContent.split('\n');
    const { codeBlockStarts, codeBlockEnds } = analyzeCodeBlocks(lines);

    // 截取范围包含完整代码块
    for (let i = 0; i < codeBlockStarts.length; i++) {
      const start = codeBlockStarts[i];
      const end = codeBlockEnds[i];
      
      if (start < rows && end >= rows) {
        return lines.slice(0, end + 1).join('\n');
      }
    }
    return lines.slice(0, rows).join('\n');
  }, [processedContent, rows, ellipsis, expanded]);

  const shouldShowToggle = hasContent && ellipsis && lineCount > rows;

  const copyConfig = useMemo(() => ({
    text: content,
    tooltips: COPY_TOOLTIPS
  }), [content]);

  const renderControls = () => {
    if (!shouldShowToggle && !hasContent) return null;
    
    return (
      <>
        {shouldShowToggle && (
          <>
            {!expanded && <span className="markdown-ellipsis">... </span>}
            <Button 
              type="link" 
              size="small"
              onClick={toggleExpand}
              className="markdown-inline-toggle"
            >
              {expanded ? '收起' : '展开'}
            </Button>
          </>
        )}
        {hasContent && (
          <Typography.Paragraph
            copyable={copyConfig}
            style={{ margin: 0, display: 'inline-block', marginLeft: 8 }}
          />
        )}
      </>
    );
  };

  const [onMouseEvent] = useSelectionHook()

  return (
    <div className="markdown-container">
      <div className="markdown-content-wrapper"
      {
        ...onMouseEvent
      }
      >
        <ReactMarkdown
          className="markdown-body"
          {...PLUGINS}
          components={{ code: CodeBlock }}
        >
          {contentWithToggle}
        </ReactMarkdown>
        {renderControls()}
      </div>
    </div>
  );
});

Markdown.displayName = "Markdown";
// 添加RxDB更新插件
import {addRxPlugin, createRxDatabase, RxCollection} from 'rxdb';
// 使用 Dexie 存储替代 PouchDB
import {getRxStorageDexie} from 'rxdb/plugins/storage-dexie';
// 导入更新插件
import {RxDBUpdatePlugin} from 'rxdb/plugins/update';
// 导入验证器
import {getAjv, wrappedValidateAjvStorage} from 'rxdb/plugins/validate-ajv';
import {RxDBAttachmentsPlugin} from 'rxdb/plugins/attachments';
import ajvErrors from 'ajv-errors';

// 在开发模式下添加开发模式插件
if (import.meta.env.DEV) {
    import('rxdb/plugins/dev-mode').then(module => {
        addRxPlugin(module.RxDBDevModePlugin);
    });
}

// 导入所有集合模式
import {categorySchema} from './schemas/category';
import {noteSchema} from './schemas/note';
import {attachmentSchema} from './schemas/attachment';
import {nodeSchema} from './schemas/node';
import {edgeSchema} from './schemas/edge';
import {roundSchema} from './schemas/round';
import {markSchema} from './schemas/mark';
import {workspaceSchema} from './schemas/workspace';
import {sessionSchema} from "@/local/db/schemas/session.ts";
import {tagGroupSchema} from './schemas/tagGroup';
import {tagSchema} from './schemas/tag';
import {nodeTagSchema} from './schemas/nodeTag';

// 添加RxDB插件
addRxPlugin(RxDBUpdatePlugin);
addRxPlugin(RxDBAttachmentsPlugin);

// 数据库实例
let dbPromise: any = null;

/**
 * 添加时间戳钩子到集合
 */
function addTimestampHooks(collection: RxCollection) {
    // 插入前添加时间戳
    collection.preInsert((data: any) => {
        const now = Math.floor(Date.now() / 1000);

        const hasCreateAt = collection.schema.jsonSchema.properties.create_at !== undefined;
        const hasUpdateAt = collection.schema.jsonSchema.properties.update_at !== undefined;

        if (hasCreateAt && data.create_at === undefined) {
            data.create_at = now;
        }

        if (hasUpdateAt && data.update_at === undefined) {
            data.update_at = now;
        }

        // 为 attachments 集合设置默认值
        if (collection.name === 'attachments') {
            if (data.type === undefined) {
                data.type = 'file';
            }
            if (data.parent_id === undefined) {
                data.parent_id = 'root';
            }
            if (data.order === undefined) {
                data.order = 0;
            }
        }

        return data;
    }, false);

    // 更新前添加时间戳
    collection.preSave((data: any) => {
        const hasUpdateAt = collection.schema.jsonSchema.properties.update_at !== undefined;

        if (hasUpdateAt) {
            data.update_at = Math.floor(Date.now() / 1000);
        }

        return data;
    }, false);
}

// 配置 Ajv 实例
const ajv = getAjv();
ajv.opts.allErrors = true;
ajvErrors(ajv);

// 分类名称唯一性检查
const checkCateName = async (data: any) => {
    const res = await dbPromise.categories.findOne({
        selector: {
            cate_name: data.cate_name,
            id: {
                $ne: data.id
            }
        }
    }).exec()
    if (res) {
        throw new Error('分类名称已存在');
    }
    return data;
}

/**
 * 初始化数据库
 */
export async function getDatabase() {
    if (dbPromise) return dbPromise;

    // 创建带验证器的存储
    const storage = wrappedValidateAjvStorage({
        storage: getRxStorageDexie(),
    });

    // 创建数据库
    dbPromise = await createRxDatabase({
        name: 'notes_db',
        storage: storage,
        multiInstance: true,
        ignoreDuplicate: false,
    });

    // 添加集合
    const collections = await dbPromise.addCollections({
        workspaces: {
            schema: workspaceSchema
        },
        categories: {
            schema: categorySchema,
        },
        notes: {
            schema: noteSchema
        },
        nodes: {
            schema: nodeSchema
        },
        edges: {
            schema: edgeSchema
        },
        sessions: {
            schema: sessionSchema
        },
        rounds: {
            schema: roundSchema
        },
        attachments: {
            schema: attachmentSchema
        },
        marks: {
            schema: markSchema
        },
        tagGroups: {
            schema: tagGroupSchema
        },
        tags: {
            schema: tagSchema
        },
        nodeTags: {
            schema: nodeTagSchema
        }
    });

        // 为所有集合添加时间戳钩子
        Object.values(collections).forEach(collection => {
            if (collection) {
                addTimestampHooks(collection as RxCollection);
            }
        });

    // 分类名称唯一性检查
        dbPromise.categories.preInsert(checkCateName);
        dbPromise.categories.preSave(checkCateName);

        return dbPromise;
}

export default getDatabase;
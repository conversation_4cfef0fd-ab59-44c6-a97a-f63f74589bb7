import {useCallback} from "react";
import {saveNotebook} from "@/api/notebook";
import {getWsId} from "@/tools/params.ts";
import {useNoteStore} from "@/store/note-store.ts";

export const useNoteSave = () => {
    const {setContent} = useNoteStore((state) => ({
        setContent: state.setContent,
    }));
    // 保存笔记
    const saveNote = useCallback(async (content: string) => {
        try {
            const currentContent = useNoteStore.getState().content
            if (currentContent === content) {
                return
            }
            await saveNotebook({
                wid: getWsId(),
                content: content
            })
            setContent(content)
        } catch (e) {
            console.log("保存记事本失败，", e)
        }
    }, [saveNotebook])
    return {
        saveNote
    }
}
import { useCallback } from 'react';
import { FileTreeNode } from '../HeadlessFileTreePanel';
import { folderService } from '@/local';
import { usePdfStore } from '@/store/pdf-store';

interface UseDeleteOperationsProps {
  onRefresh?: () => void;
  getSelectedItems?: () => FileTreeNode[];
}

export const useDeleteOperations = ({ onRefresh, getSelectedItems }: UseDeleteOperationsProps = {}) => {
  const { removePdfs, setTabItems } = usePdfStore();

  // 删除单个文件/文件夹
  const deleteSingleItem = useCallback(async (node: FileTreeNode) => {
    try {
      if (node.isDirectory) {
        // 删除文件夹
        await folderService.delete({ id: node.id });
        console.log(`✅ 文件夹 "${node.title}" 删除成功`);
      } else {
        // 删除文件
        await folderService.delete({ id: node.id });
        
        // 从PDF store中移除
        removePdfs(node.id);
        
        // 从标签页中移除
        const state = usePdfStore.getState();
        const tabItems = state.tabItems || [];
        const newTabItems = tabItems.filter((item) => item.key !== node.id);
        setTabItems(newTabItems);
        
        console.log(`✅ 文件 "${node.title}" 删除成功`);
      }
    } catch (error) {
      console.error('删除失败:', error);
      throw error;
    }
  }, [removePdfs, setTabItems]);

  // 批量删除多个文件/文件夹
  const deleteMultipleItems = useCallback(async (items: FileTreeNode[]) => {
    try {
      // 并行删除所有项目
      const deletePromises = items.map(item => deleteSingleItem(item));
      await Promise.all(deletePromises);
      
      const itemNames = items.map(item => item.title).join(', ');
      console.log(`✅ 成功删除: ${itemNames}`);
    } catch (error) {
      console.error('批量删除失败:', error);
      throw error;
    }
  }, [deleteSingleItem]);

  // 处理删除操作（支持单选和多选）
  const handleDelete = useCallback(async (node?: FileTreeNode) => {
    try {
      let itemsToDelete: FileTreeNode[] = [];
      
      if (node) {
        // 如果指定了节点，检查是否在选中列表中
        if (getSelectedItems) {
          const selectedItems = getSelectedItems();
          if (selectedItems.some(item => item.id === node.id)) {
            // 如果当前节点在选中列表中，删除所有选中的项目
            itemsToDelete = selectedItems;
          } else {
            // 否则只删除当前节点
            itemsToDelete = [node];
          }
        } else {
          // 如果没有提供获取选中项目的方法，只删除当前节点
          itemsToDelete = [node];
        }
      } else if (getSelectedItems) {
        // 如果没有指定节点，删除所有选中的项目
        itemsToDelete = getSelectedItems();
      }
      
      // 过滤掉根节点
      itemsToDelete = itemsToDelete.filter(item => item.id !== 'root');
      
      if (itemsToDelete.length === 0) {
        console.log('没有可删除的项目');
        return;
      }
      
      // 执行删除操作
      if (itemsToDelete.length === 1) {
        await deleteSingleItem(itemsToDelete[0]);
      } else {
        await deleteMultipleItems(itemsToDelete);
      }
      
      // 触发刷新
      if (onRefresh) {
        onRefresh();
      }
      
    } catch (error) {
      console.error('删除操作失败:', error);
      // 可以在这里添加错误提示，但不使用弹窗
      // 比如通过 toast 通知或其他非阻塞的方式
    }
  }, [getSelectedItems, deleteSingleItem, deleteMultipleItems, onRefresh]);

  return {
    handleDelete,
    deleteSingleItem,
    deleteMultipleItems,
  };
}; 
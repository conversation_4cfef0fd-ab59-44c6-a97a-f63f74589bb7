import {Input, InputRef} from "antd";
import {useEffect, useRef} from "react";
import {TagIcon} from "@/components/icons";
import styled from "styled-components";

const TabWrapper = styled.div<{ $isSticky?: boolean; $isPinned?: boolean }>`
  ${(props) =>
    props.$isSticky &&
    props.$isPinned &&
    `
    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 0;
      right: 0;
      height: 10px;
      background: linear-gradient(var(--bg-category-tabs), transparent);
      pointer-events: none;
      z-index: 20;
    }
    box-shadow: 0 3px 5px -1px #000;
    background: var(--bg-category-tabs);
  `}
`;
interface Props {
  placeholder: string;
  value: string;
  randomColor: string;
  onChange: (value: string) => void;
  onConfirm: () => void;
}
export const CategoryInput = ({
  value,
  placeholder,
  randomColor,
  onChange,
  onConfirm,
}: Props) => {
  const inputRef = useRef<InputRef>(null);
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  return (
      <TabWrapper className="w-full px-5">
      <div
        className="h-10 px-2.5 flex items-center gap-2 rounded-[5px] hover:bg-[var(--bg-category-tabs-hover)]"
        style={{
          background: "var(--bg-category-tabs-hover)",
        }}
      >
        <TagIcon color={randomColor} />
        <Input
          className="w-[150px] h-5 !rounded-[0px] !border-none"
          placeholder={placeholder}
          ref={inputRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onConfirm}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === "Enter") {
              onConfirm();
            }
          }}
        />
      </div>
    </TabWrapper>
  );
};

import {useCallback} from "react";
import {NodeType, useFlowStore} from "@/store/flow-store.ts";
import {createNode, CreateNodeType} from "@/api/canvas";
import {getWsId} from "@/tools/params.ts";
import {useNodeLayout} from "@/components/flow/hooks/node-layout.ts";
import {getDefaultNodeSize, calculateNodeHeight} from "@/components/flow/constants/node-defaults";

const DEFAULT_COLOR = "#FFE28F"
const useNodeAdd = () => {
    const {addNodes, setCenter, addEdges} = useFlowStore((state) => ({
        addNodes: state.addNodes,
        setCenter: state.setCenter,
        addEdges: state.addEdges,
    }))
    const {calculateNodePosition} = useNodeLayout()
    // 新增节点
    const handleAddNode = useCallback(async (prop?: Partial<CreateNodeType> & { 
        setCenter?: boolean, 
        refInfo?: Record<any,any>, 
        offsetIndex?: number,
        customWidth?: number,
        customHeight?: number,
        autoHeight?: boolean
    }, strokeDasharray?: boolean) => {
        // 如果传入了具体的 x, y 坐标，使用传入的坐标；否则计算节点位置
        let x: number, y: number;
        if (prop?.x !== undefined && prop?.y !== undefined) {
            x = prop.x;
            y = prop.y;
        } else {
            const calculatedPosition = calculateNodePosition(prop?.pids);
            x = calculatedPosition.x;
            y = calculatedPosition.y + (prop?.offsetIndex ?? 0) * 40;
        }
        
        const defaultNode = {
            wid: getWsId(),
            content: "",
            pids: [],
            color: DEFAULT_COLOR.slice(1),
            x: x,
            y: y,
            type: "default" as CreateNodeType["type"],
        }

        try {
            const isCenter = prop?.setCenter ?? false
            delete prop?.setCenter
            const node = {...defaultNode, ...prop}
            const res = await createNode({
                ...node,
                style: {
                    strokeDasharray: strokeDasharray ,
                }
            })

            // 计算节点尺寸
            const defaultSize = getDefaultNodeSize();
            let nodeWidth = prop?.customWidth || defaultSize.width;
            let nodeHeight = prop?.customHeight || defaultSize.height;
            
            // 如果启用了自动高度计算
            if (prop?.autoHeight) {
                nodeHeight = calculateNodeHeight(node.content);
            }
            
            const newNode = {
                id: (res.data as any).nid,
                type: NodeType.markdownNode,
                position: {x: node.x, y: node.y},
                data: {
                    title: "",
                    content: node.content,
                    color: `#${node.color}`,
                },
                selected: true,
                width: nodeWidth,
                height: nodeHeight,
                measured: {
                    width: nodeWidth,
                    height: nodeHeight,
                },
            }
            addNodes([newNode])
            const newEdges = (res.data as any).edges || [];

            // const newEdges = prop?.pids?.map((pid) => ({
            //     id: `tmp-${newNode.id}-${pid}`,
            //     source: pid,
            //     target: newNode.id,
            // })) || [];
            if (newEdges.length > 0) {
                addEdges(newEdges)
            }
            if (isCenter) {
                setCenter(newNode.id)
            }
            return res
        } catch (e) {
            console.log(e)
            return Promise.reject(e)
        }
    }, [addNodes, setCenter, calculateNodePosition])
    return {
        handleAddNode
    }
}

export {useNodeAdd}
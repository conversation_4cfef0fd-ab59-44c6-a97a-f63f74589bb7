import {RxJsonSchema} from 'rxdb';

export interface TagDocType {
    id: string; // 标签ID
    group_id: string; // 所属标签组ID
    name: string; // 标签名称
    sort_order: number; // 排序顺序
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const tagSchema: RxJsonSchema<TagDocType> = {
    title: 'tag schema',
    version: 0,
    description: '标签模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        group_id: {
            type: 'string',
            maxLength: 100
        },
        name: {
            type: 'string',
            maxLength: 200
        },
        sort_order: {
            type: 'number',
            minimum: 0,
            default: 0
        },
        create_at: {
            type: 'number',
            minimum: 0
        },
        update_at: {
            type: 'number',
            minimum: 0
        }
    },
    required: ['id', 'group_id', 'name', 'sort_order'],
    indexes: ['group_id', 'sort_order']
}; 
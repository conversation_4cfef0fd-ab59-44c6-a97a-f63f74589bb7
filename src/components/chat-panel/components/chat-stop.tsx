import styled from "styled-components";
import { useChatStore } from "@/store/workerspace-store/chat-store";
const StopButton = styled.div`
  position: absolute;
  left: 50%;
  top: -32px;
  transform: translateX(-50%);
  border-radius: 5px;
  width: 50px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  background: #cdd2e9;
  font-size: 12px;
  cursor: pointer;
  color: #3d56ba;
`;

export const ChatStop = () => {
  const { sseClient, setSseClient } = useChatStore();
  return (
    <StopButton
      aria-label="停止生成"
      onClick={() => {
        sseClient?.abort();
        setSseClient(null);
      }}
    >
      Stop
    </StopButton>
  );
};

import {DragAndResize} from "@/components/custom-drag-and-resize";
import {useFetchData} from "@/components/note/hooks/fetch-data.ts";
import {useNoteFocus} from "@/components/note/hooks/note-focus.ts";
import {useNoteSave} from "@/components/note/hooks/note-save.ts";
import {PanelPosition, ResizableContainer,} from '@/pages/workspace/Panel';
import {useNoteStore} from "@/store/note-store.ts";
import {convertImageToBase64} from "@/tools/image.ts";
import {CloseOutlined} from "@ant-design/icons";
import {Button, Tooltip} from "antd";
import React, {FC, useEffect, useState} from "react";
import styled from "styled-components";
import Vditor from "vditor";
import "vditor/dist/index.css";
import "./style/vditor.css";

import { GlobalEditor } from "@/components/lexicalEditor/App";

const LexicalEditor = () => {

    return <div className="w-full h-full flex flex-col">
        <div
            className="w-full h- cursor-pointer drag-handle text-center leading-8 flex items-center justify-between px-4"
            style={{
                position: 'relative',
                paddingRight: 48,
            }}
        >
            <span style={{ flex: 1, textAlign: 'center' }}>记事本</span>
            <Tooltip title="关闭">
                <Button
                    type="text"
                    icon={<CloseOutlined />}
                    style={{ position: 'absolute', right: 28, top: 0, color: '#666', background: 'transparent' }}
                    onClick={() => {
                        // if (vd) {
                        //     saveNote(vd.getValue());
                        // }
                        window.dispatchEvent(new CustomEvent('notepad-close'));
                    }}
                    onMouseDown={e => e.stopPropagation()}
                />
            </Tooltip>
        </div>
        <div className="hide-scrollbar box-border" style={{ flex: 1, overflow: 'auto', position: 'relative' }}>
            <GlobalEditor />
        </div>
    </div>
}

const NotePanel = () => {
    const [vd, setVd] = useState<Vditor>();
    const {insertContent, setInsertContent} = useNoteStore((state) => ({
        insertContent: state.insertContent,
        setInsertContent: state.setInsertContent,
    }))
    const {saveNote} = useNoteSave()
    // 获取数据
    const {fetchData} = useFetchData()
    // 定位
    const {focusToEnd, scrollToCursor} = useNoteFocus()
    // 初始化
    useEffect(() => {
        const vditor = new Vditor("vditor-note", {
            height: "calc(100% - 2rem)",
            after: async () => {
                vditor.setValue(await fetchData())
                vditor.focus()
                focusToEnd()
                setVd(vditor)
            },
            blur: (value) => {
                saveNote(value)
            },
            upload: {
                accept: "image/*",
                url: "/upload/img",
                handler: async (files): Promise<any> => {
                    const base64Files = await convertImageToBase64(files)
                    let content = ""
                    base64Files.forEach((file) => {
                        content += `![${file.name}](${file.base64})\n`
                    })
                    setInsertContent(content)
                }
            },
            mode: "wysiwyg"
        });
        return () => {
            vd?.destroy();
            setVd(undefined);
        };
    }, []);
    // 插入内容
    useEffect(() => {
        if (vd && insertContent !== "") {
            vd.insertMD(insertContent)
            // 上传图片>1m渲染时间在3秒以上
            setTimeout(() => {
                scrollToCursor()
            }, 10)
            setInsertContent("")
        }
    }, [vd, insertContent]);

    // 监听 vditor-close 事件
    useEffect(() => {
        const handleVditorClose = () => {
            if (vd) {
                saveNote(vd.getValue());
                window.dispatchEvent(new CustomEvent('notepad-close'));
            }
        };
        window.addEventListener('vditor-close', handleVditorClose);
        return () => {
            window.removeEventListener('vditor-close', handleVditorClose);
        };
    }, [vd, saveNote]);

    return <div className="w-full h-full" onWheel={(e) => {
        e.stopPropagation()
        e.preventDefault()
    }}>
        <div className="w-full h-8 cursor-pointer drag-handle text-center leading-8 flex items-center justify-between px-4" style={{ position: 'relative', paddingRight: 48 }}>
            <span style={{ flex: 1, textAlign: 'center' }}>记事本</span>
            <Tooltip title="关闭">
                <Button
                    type="text"
                    icon={<CloseOutlined/>}
                    style={{position: 'absolute', right: 28, top: 0, color: '#666', background: 'transparent'}}
                    onClick={() => {
                        if (vd) {
                            saveNote(vd.getValue());
                        }
                        window.dispatchEvent(new CustomEvent('notepad-close'));
                    }}
                    onMouseDown={e => e.stopPropagation()}
                />
            </Tooltip>
        </div>
        <div id="vditor-note" className="vditor"/>
    </div>
};

// export default NotePanel;
// export default LexicalEditor

export const NotePanelMain: FC<{
    zIndex: number,
    panelPosition: PanelPosition,
    setPanelPosition: (position: PanelPosition) => void,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    handlePanelClick?: () => void,
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = ({
          panelPosition,
          zIndex = 10,
          otherPanels = [],
          ...rest
      }) => {

    return <DragAndResize
        panelPosition={panelPosition}
        className="note-panel-resizable"
        panelClassName="note-panel-resizable"
        zIndex={zIndex}
        // innerElement={<NotePanel/>}
        innerElement={<LexicalEditor/>}
        PanelWrapper={NotePanelWrapper}
        otherPanels={otherPanels}
        {...rest}
    />
}

// 为每个面板设置初始位置
const NotePanelWrapper = styled(ResizableContainer)`
    top: 20px;
    left: 20px;
    border-radius: 30px 30px 0 0;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    background-color: #E0E2F4;
`;
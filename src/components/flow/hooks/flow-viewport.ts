import {useReactFlow} from "@xyflow/react";
import {useFlowStore} from "@/store/flow-store.ts";
import {useCallback, useEffect} from "react";

const useViewport = () => {
    const {center, zoom} = useFlowStore((state) => ({
        center: state.center,
        zoom: state.viewport.zoom
    }))
    const {setCenter} = useReactFlow()

    // 将中心节点设置到视图中
    const onSetCenter = useCallback((center: string) => {
        const node = useFlowStore.getState().nodes.find((node) => node.id === center)
        if (!node) return
        const x = node.position.x + (node.measured?.width ?? 0) / 2
        const y = node.position.y + (node.measured?.height ?? 0) / 2
        setCenter(x, y, {duration: 300, zoom: zoom})
    }, [setCenter, zoom])
    useEffect(() => {
        if (!center) {
            return
        }
        onSetCenter(center)
    }, [center]);
}

export {useViewport}
import { memo, useEffect, useRef, useState } from "react";
import { Divider, Input, Space, Tooltip } from "antd";
import styled from "styled-components";
import { ReferencePicker } from "./reference-picker";
import { ModelSelector } from "./model-selector";
import { useChatConversation } from "../hooks/use-chat-conversation";
import type { ChatInputProps } from "../types";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatEditIcon, ChatReloadIcon, EnterIcon } from "@/components/icons";
import { CHAT_INPUT_DIV__TAG } from "..";

const TextAreaCustom = styled(Input.TextArea)`
  resize: none;
  border: none;
  outline: none;
  padding-left: 5px;
  background-color: transparent;
  border: none !important;
  box-shadow: none !important;

  &:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  &:hover {
    border: none !important;
    outline: none !important;
  }

  &.ant-input-focused {
    box-shadow: none !important;
    border-color: transparent !important;
  }
`;

// 底部容器样式，用于放置模型选择器和提交按钮
const FooterContainer = styled.div`   
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
`;

// 聊天输入组件，使用memo优化性能
export const ChatInput = memo(
  ({ question, currentRefs, rid, isFooter, inStream = false, children = null }: ChatInputProps) => {
    // 从全局状态获取聊天相关数据和方法
    const {
      footerRefs,
      chatList,
      currentChat,
      currentChatRid,
      sseClient,
      isFetchStream,
      setCurrentChat,
      updateCurrentChat,
      updateChatList,
      setCurrentChatRid,
      // setUserInput,
    } = useChatStore();

    // 获取开始对话的方法
    const { onStartConversation } = useChatConversation();

    // 本地状态：选中的AI模型
    const [selectedModel, setSelectedModel] = useState<string>("gpt-3.5-turbo");

    // 本地状态：新问题文本，初始化为传入的问题或页脚问题
    const [newQuestion, setNewQuestion] = useState<string>(question as string);
    const [showReloadIcon, setShowReloadIcon] = useState<boolean>(true);
    // 添加一个引用来处理点击外部区域
    const chatInputRef = useRef<HTMLDivElement>(null);
    const footerInputRef = useRef<HTMLDivElement>(null);
    const handleBtnRef = useRef<HTMLDivElement>(null);
    const TextAreaRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
      if (rid === currentChatRid) {
        return
      }
      const handleClickOutside = (event: MouseEvent) => {
        // 如果正在获取数据流，不处理点击事件，避免中断流程
        if (isFetchStream) {
          return;
        }
        
        const chatRootDom = document.getElementById(CHAT_INPUT_DIV__TAG)
        
        // 判断点击是否在整个chat面板内部
        if (!chatRootDom || !chatRootDom.contains(event?.target as Node)) {
          // 点击在chat面板外部，不处理
          return;
        }
        
        // 找到当前正在编辑的chat项的DOM元素
        const currentEditingChatItem = document.querySelector(`[data-chat-rid="${currentChatRid}"]`);
        
        // 如果当前有编辑项，并且点击在编辑项内部，则不处理
        if (currentEditingChatItem && currentEditingChatItem.contains(event?.target as Node)) {
          return;
        }
        
        // 点击在chat面板内部，但不在当前编辑项内部，取消选中
        setCurrentChatRid(-1);
        setCurrentChat(null);
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, [currentChatRid, rid, isFooter, currentChat, newQuestion, isFetchStream]);

    const handleModelChange = (value: string) => {
      if (isFetchStream) return
      setSelectedModel(value);
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 如果正在使用输入法（IME），则不处理Enter键
      // 避免在输入中文等时过早提交
      if (e.nativeEvent.isComposing || e.key === "Dead") {
        return;
      }
      // Mac系统 Command + Enter 或 Windows系统 Alt + Enter 换行
      if (e.key === "Enter" && (e.metaKey || e.altKey)) {
        e.preventDefault();
        // 在光标位置插入换行符
        const textarea = e.currentTarget;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const value = textarea.value;
        const newValue =
          value.substring(0, start) + "\n" + value.substring(end);
        setNewQuestion(newValue);
        // 设置光标位置到换行符后
        setTimeout(() => {
          textarea.selectionStart = textarea.selectionEnd = start + 1;
        }, 0);
      }
      // Enter键提交（不按其他修饰键）
      if (e.key === "Enter" && !e.shiftKey && !e.altKey && !e.metaKey) {
        e.preventDefault();
        onBlurUpdateChat(newQuestion);
        if (newQuestion) {
          if (isFetchStream) {
            // 先中断当前的 SSE 连接
            sseClient?.abort();
            // 等待一小段时间后再发起新的请求
            setTimeout(() => {
              onStartConversation(newQuestion, currentChatRid === -1 ? undefined : Number(currentChatRid));
            }, 100);
          } else {
            onStartConversation(newQuestion, currentChatRid === -1 ? undefined : Number(currentChatRid));
          }
          setNewQuestion("");
        }
      }
    };

    // 更新问题文本
    const onChangeQuestion = (value: string) => {
      if (isFetchStream) return
      setShowReloadIcon(false);
      setNewQuestion(value);
    };

    // 失去焦点时更新聊天内容
    const onBlurUpdateChat = (value: string) => {
      // 只在非编辑状态下更新聊天内容
      if (currentChat && showReloadIcon) {
        currentChat.question = value;
        updateChatList(currentChat);
        updateCurrentChat(currentChat);
      }
    };

    if (inStream) {
      return <>
        <div
          data-chat-rid={rid}
          style={{
            transition: "all 0.3s ease",
            position: "relative",
            border: `${isFooter ? "1px solid #979797" : ""}`,
          }}
          className={` rounded-[7px] flex-1 bg-white overflow-hidden  p-2.5 rounded-md text-[#7B86CB]}`}
        >
          <div
            className="relative rounded-md "
          >
            {/* 引用选择器组件 */}
            <ReferencePicker
              rid={rid}
              currentRefs={currentRefs}
              isFooter={isFooter}
            />
            <div
              className="flex justify-between items-center handle_btn  rounded-[7px] overflow-hidden mt-2.5"
            >
              <div className="text-sm">{question}</div>
            </div>
          </div>
        </div>
        {
          children
        }
      </>
    }

    const active = currentChatRid === rid

    const setActive = () => {
      if (isFetchStream) return
      if (rid) {
        const currentChat = chatList.find((chat) => chat.rid === rid);
        setCurrentChat(currentChat as any)
        setCurrentChatRid(rid);
      }
    }

    const reloadOrReask = (e: React.MouseEvent<HTMLDivElement>) => {
      if (isFetchStream) return
      e.stopPropagation()

      if (rid) {
        const currentChat = chatList.find((chat) => chat.rid === rid);
        setCurrentChat(currentChat as any)
        setCurrentChatRid(rid)
        onStartConversation(newQuestion as string, rid as number);
      }
    }

    return (
      <div
        data-chat-rid={rid}
        style={{
          transition: "all 0.3s ease",
          position: "relative",
          border: `${active ? "1px solid #3367d9" : ""}`,
        }}
        onMouseDown={(e) => {
          // 正在编辑的项，阻止mouseDown事件冒泡
          if (isFetchStream) return
          if (rid === currentChatRid) {
            e.stopPropagation()
          }
        }}
        className={`
            rounded-[7px] flex-1 bg-white overflow-hidden
            ${active || isFooter ? "p-1 border rounded-md overflow-hidden" : "p-2.5 rounded-md text-[#7B86CB]"}
          `}
      >

        <div
          className="relative rounded-md "
          onClick={setActive}
          onMouseDown={(e) => e?.stopPropagation()}
        >
          {/* 引用选择器组件 */}
          <ReferencePicker
            rid={rid}
            currentRefs={currentRefs}
            isFooter={isFooter}
            active={active}
          />
          {/* 底部  || 当前输入项目 */}
          {
            (active || isFooter) ?
              <TextAreaCustom
                disabled={isFetchStream}
                ref={TextAreaRef}
                value={newQuestion}
                className="w-full rounded-md !border-0 placeholder:text-[#939dc2] placeholder:text-[13px]  !bg-transparent !text-gray-600 !text-base"
                onChange={(e) => onChangeQuestion(e.target.value)}
                onBlur={(e) => onBlurUpdateChat(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`${isFooter ? "请继续你的提问" : "输入不能为空"}`}
                autoSize={{ minRows: 1, maxRows: 5 }}
              />
              :
              <div className="text-sm">{question}</div>
          }
        </div>

        {/* 非底部输出，1. 在获取焦点需要展示，取消和重新发送  2. 未获取焦点，需要展示重新加载icon*/}
        {!isFooter && (
          <div
            className="flex justify-between items-center handle_btn  rounded-[7px] overflow-hidden mt-2.5"
            ref={handleBtnRef}
          >
            {
              active ?
                <>
                  {/* 获取焦点时: 展示取消和重新发送 */}
                  <div />
                  <Space size={0}>
                    <div
                      className="cursor-pointer text-gray-400 hover:text-gray-600"
                      onClick={() => {
                        setCurrentChatRid(-1);
                        setNewQuestion(question as string)
                      }}
                    >
                      取消
                    </div>
                    <Divider type="vertical" className="border-gray-400 w-full" />
                    <div
                      className="cursor-pointer text-gray-400  hover:text-gray-600"
                      onClick={reloadOrReask}
                    >
                      重新发送
                    </div>
                  </Space>
                </>
                :
                <>
                  <Tooltip title="重新加载" className="h-full ">
                    <div
                      onClick={reloadOrReask}
                    >
                      <ChatReloadIcon size={13} color="#3E53A8" />
                    </div>
                  </Tooltip>
                  <div
                    className="w-[17px] h-[17px] flex justify-center items-center bg-[#3E53A8] rounded-full"
                    onClick={setActive}
                  >
                    <ChatEditIcon color="#fff" size={10} />
                  </div>
                </>
            }
          </div>
        )}

        {isFooter && (
          <FooterContainer ref={footerInputRef}>
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={handleModelChange}
            />
            <div
              className="flex items-center gap-[3px]"
              onClick={() => {
                if (isFetchStream) return
                if (newQuestion || footerRefs.length > 0) {
                  if (isFetchStream) {
                    // 先中断当前的 SSE 连接
                    sseClient?.abort();
                    // 等待一小段时间后再发起新的请求
                    setTimeout(() => {
                      onStartConversation(newQuestion);
                    }, 100);
                  } else {
                    onStartConversation(newQuestion);
                  }
                  setNewQuestion("");
                }
              }}
            >
              <div
                className={`
                  ${((newQuestion && newQuestion.trim().length > 0) || !!footerRefs.length)
                    ? "text-[#3E53A8] cursor-pointer"
                    : "text-gray-400 cursor-not-allowed"
                  } text-[13px]`}
              // className={`
              //   text-[#3E53A8] cursor-pointer text-[13px]
              //   `}
              >
                Submit
              </div>
              <EnterIcon
                disabled={!newQuestion}
                size={10}
                color={newQuestion ? "#3E53A8" : "text-gray-400"}
              />
            </div>
          </FooterContainer>
        )}
      </div>
    );
  }
);

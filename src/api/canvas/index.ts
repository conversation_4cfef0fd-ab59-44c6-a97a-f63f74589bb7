import {httpRequest} from '../index'
import {CreateLinkType, CreateNodeType, DeleteLinkType, UpdateNodesPositionType, UpdateNodeSizeType, UpdateNodeType} from './types.d'

export const getNodes = async (path: string) => httpRequest.get(path)

// 创建新节点
export const createNode = (data: CreateNodeType) =>
    httpRequest.post<CreateNodeType>('/node', data)

// 关联节点
export const createLink = (data: CreateLinkType) =>
    httpRequest.post('/node/link', data)

// 删除节点 - 不限制节点数量
export const removeNode = (nids: Array<string | number>) => {
    // 如果节点数量过多，分批删除
    const batchSize = 30; // 每批最多30个节点，符合后端API限制
    const batches = [];
    
    for (let i = 0; i < nids.length; i += batchSize) {
        const batch = nids.slice(i, i + batchSize);
        batches.push(batch);
    }
    
    // 如果只有一批，直接删除
    if (batches.length === 1) {
        return httpRequest.delete('/node', { data: { nids } });
    }
    
    // 如果有多批，依次删除
    return Promise.all(batches.map(batch => 
        httpRequest.delete('/node', { data: { nids: batch } })
    )).then(responses => {
        // 返回最后一个响应
        return responses[responses.length - 1];
    });
}

//删除连线
export const deleteLink = (data: DeleteLinkType) =>
    httpRequest.delete('/node/link', { data })

//节点列表
export const getNodesList = (wid: string | number) => httpRequest.get(`/node/list`, {params: {wid}})

//更新节点
export const updateNode = (data: UpdateNodeType) =>
    httpRequest.patch('/node', data)
export const updateNodesPosition = (data: { positions: UpdateNodesPositionType[] }) =>
    httpRequest.patch('/node/pos', data)

// 更新节点尺寸
export const updateNodeSize = (data: UpdateNodeSizeType) =>
    httpRequest.patch('/node/size', data)

// 更新边标签
export const updateEdgeLabel = (data: { eid: string; label?: string } & Record<string, any>): Promise<{ data: { eid: string; created?: boolean } }> =>
    httpRequest.patch('/node/edge-label', data)

export const updateEdgeStyle = (data: { eid: string; style: Record<string, any> }): Promise<{ data: { eid: string; created?: boolean } }> =>
    httpRequest.patch('/node/edge-style', data)

export const updateEdgeData = (data: { eid: string; data: Record<string, any> }): Promise<{ data: { eid: string; created?: boolean } }> =>
    httpRequest.patch('/node/edge-data', data)
    
export * from './types.d'
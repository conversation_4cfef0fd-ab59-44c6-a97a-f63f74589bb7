import { useState, useCallback, useEffect } from 'react';
import { usePdfStore } from '@/store/pdf-store.ts';
import { folderService, getDatabase } from '@/local';
import { WorkspaceService } from '@/local/services/workspace-service';
import { getWsId } from '@/tools/params.ts';

// 创建 WorkspaceService 实例
const workspaceService = new WorkspaceService();

export interface FileTreeItem {
  id: string;
  file_name: string;
  type: 'file' | 'folder';
  parent_id: string;
  file_path?: string;
  mime_type?: string;
  [key: string]: any;
}

export function useFileManager() {
  const [folders, setFolders] = useState<FileTreeItem[]>([]);
  const [files, setFiles] = useState<FileTreeItem[]>([]);
  const [dbReady, setDbReady] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [currentWorkspaceId] = useState(getWsId() || 'default_workspace');
  const [targetFolderId, setTargetFolderId] = useState<string>('root');

  const { addPdfs } = usePdfStore(state => ({
    addPdfs: state.addPdfs
  }));

  // 增量更新方法 - 添加文件
  const addFile = useCallback((newFile: FileTreeItem) => {
    setFiles(prevFiles => [...prevFiles, newFile]);
  }, []);

  // 增量更新方法 - 批量添加文件
  const addFiles = useCallback((newFiles: FileTreeItem[]) => {
    setFiles(prevFiles => [...prevFiles, ...newFiles]);
  }, []);

  // 增量更新方法 - 删除文件
  const removeFile = useCallback((fileId: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.id !== fileId));
  }, []);

  // 增量更新方法 - 添加文件夹
  const addFolder = useCallback((newFolder: FileTreeItem) => {
    setFolders(prevFolders => [...prevFolders, newFolder]);
  }, []);

  // 增量更新方法 - 删除文件夹
  const removeFolder = useCallback((folderId: string) => {
    setFolders(prevFolders => prevFolders.filter(folder => folder.id !== folderId));
  }, []);

      // 加载所有数据
    const loadData = useCallback(async () => {
      if (!dbReady || !currentWorkspaceId) return;
      
      setDataLoading(true);
      try {
        // 1. 加载所有文件夹和文件 - 使用getTreeData获取完整数据
        const allItems = await folderService.getTreeData(currentWorkspaceId);
        
        // 分离文件夹和文件
        const folderList = allItems.filter(item => item.type === 'folder');
        const fileList = allItems.filter(item => item.type === 'file');
        
        setFolders(folderList);
        setFiles(fileList);
      
              // 3. 从 IndexedDB 中加载文件内容并创建 URL
        if (fileList.length > 0) {
          const pdfFiles = fileList
            .filter((file: any) => file.mime_type === 'application/pdf')
            .map((file: any) => file.id);
          
          if (pdfFiles.length > 0) {
            // 获取当前存储中的PDF
            const currentPdfs = usePdfStore.getState().pdfs;
            
            // 使用 workspaceService 批量获取文件内容，但只处理尚未加载的文件
            const newPdfFiles = pdfFiles.filter((id: string) => !currentPdfs.has(id));
            let fileUrls = new Map<string, string>();
            
            if (newPdfFiles.length > 0) {
              fileUrls = await workspaceService.getFilesContent(newPdfFiles);
            }
            
            // 创建 PDF store 数据（只添加新文件）
            const pdfStoreData = new Map();
            
            fileList.forEach((file: any) => {
              if (file.mime_type === 'application/pdf') {
                // 检查文件是否已在 store 中
                if (!currentPdfs.has(file.id)) {
                const url = fileUrls.get(file.id) || file.file_path;
                if (url) {
                  pdfStoreData.set(file.id, {
                    aid: file.id,
                    filename: file.file_name,
                    url: url
                  });
                  }
                }
              }
            });
          
          if (pdfStoreData.size > 0) {
            addPdfs(pdfStoreData);
          }
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setDataLoading(false);
    }
  }, [dbReady, currentWorkspaceId, addPdfs]);

  // 初始化数据库
  const initDatabase = useCallback(async () => {
    try {
      await getDatabase();
      setDbReady(true);
      await loadData();
    } catch (error) {
      console.error('数据库初始化失败:', error);
      setDataLoading(false);
    }
  }, [loadData]);

  // 加载单个文件内容
  const loadFileContent = useCallback(async (fileId: string) => {
    try {
      const url = await workspaceService.getFileContent(fileId);
      
      // 获取文件信息
      const db = await getDatabase();
      const fileDoc = await db.attachments.findOne({
        selector: { id: fileId }
      }).exec();
      
      if (!fileDoc) {
        throw new Error('文件记录不存在');
      }
      
      // 添加到 PDF store
      const pdfData = new Map();
      pdfData.set(fileId, {
        aid: fileId,
        filename: fileDoc.file_name,
        url: url
      });
      addPdfs(pdfData);
      
      return { success: true, url };
    } catch (error) {
      console.error('加载文件内容失败:', error);
      return { success: false, error };
    }
  }, [addPdfs]);

  // 组件挂载时初始化
  useEffect(() => {
    initDatabase();
  }, [initDatabase]);

  return {
    folders,
    files,
    setFolders,
    setFiles,
    addFile,
    addFiles,
    removeFile,
    addFolder,
    removeFolder,
    dbReady,
    dataLoading,
    setDataLoading,
    currentWorkspaceId,
    targetFolderId,
    setTargetFolderId,
    loadData,
    loadFileContent
  };
} 
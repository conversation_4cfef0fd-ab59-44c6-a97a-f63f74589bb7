import { RefObject, useState } from "react";
import { Input, Dropdown } from "antd";
import type { InputRef } from "antd/es/input";
import { ConfirmModalWrapper } from "@/components/pop-window/confirm-window";
import {
  EllipsisOutlined,
  EditOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import styled from "styled-components";
import { useSessionActions } from "../hooks/use-session-actions";
import type { SessionType } from "../types";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { getSessionDetail } from "@/api/chat";
const HistorySessionItem = styled.div`
  position: relative;
  border-radius: 8px;
  margin: 0 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid transparent;
  
  &.active {
    background: #ffffff;
    border: 1px solid #4451af;
    box-shadow: 0 2px 8px rgba(68, 81, 175, 0.1);
    outline: none;
    
    ::before {
      content: "";
      position: absolute;
      top: 50%;
      left: -1px;
      transform: translateY(-50%);
      width: 3px;
      height: 60%;
      border-radius: 0 2px 2px 0;
      background: #4451af;
    }
  }
  
  &:hover {
    background: #ffffff;
    border: 1px solid #e6e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  &:hover :nth-child(2) {
    display: block !important;
  }
`;

interface SessionItemProps {
  session: SessionType;
  isEdit: boolean;
  isActive: boolean;
  editRef: RefObject<InputRef | null>;
  newTitle: string;
  onSetActive: (id: string) => void;
  onSetEdit: (id: string) => void;
  onSetTitle: (title: string) => void;
}

export const SessionItem = ({
  session,
  isEdit,
  isActive,
  editRef,
  newTitle,
  onSetActive,
  onSetEdit,
  onSetTitle,
}: SessionItemProps) => {
  const { handleModifyTitle, handleDelete } = useSessionActions();
  const {
    session_detail_list,
    updateSession,
    setChatList,
    setSessionId,
    addSessionDetail,
  } = useChatStore();
  const [visible, setVisible] = useState(false);
  const onModifySessionTitle = async () => {
    if (session.title !== newTitle) {
      const res = await handleModifyTitle(session.sid, newTitle);
      if ((res as any).code === 0) {
        updateSession({
          ...session,
          title: newTitle,
        });
        onSetEdit("");
        editRef.current?.blur();
      }
    }
    onSetEdit("");
    editRef.current?.blur();
  };
  const onToggleSessionDetail = async (sid: string) => {
    const currentSessionDetail = session_detail_list.find(
      (item) => item.sid === sid
    );
    if (!currentSessionDetail) {
      try {
        const res = await getSessionDetail({
          sid: sid,
          rid: 0,
          page_size: 10,
        });
        if ((res as any).code === 0) {
          setChatList(res.data.chat ? res.data.chat : []);
          addSessionDetail({
            sid: sid,
            chatList: res.data.chat ? res.data.chat : [],
          });
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      setChatList(currentSessionDetail.chatList);
    }
  };
  if (isEdit) {
    return (
      <Input
        ref={editRef}
        value={newTitle}
        onChange={(e) => onSetTitle(e.target.value)}
        onBlur={onModifySessionTitle}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            if (session.title !== newTitle) {
              onModifySessionTitle();
            } else {
              onSetEdit("");
            }
          }
        }}
        className="w-full"
      />
    );
  }

  return (
    <HistorySessionItem
      tabIndex={0}
      className={`flex justify-between cursor-pointer pl-5 pr-2 rounded-md py-1 ${
        isActive ? "active" : ""
      }`}
      onClick={(e) => {
        e.stopPropagation();
        onSetActive(session.sid);
        setSessionId(session.sid);
        onToggleSessionDetail(session.sid);
      }}
      onDoubleClick={() => {
        onSetEdit(session.sid);
        onSetTitle(session.title);
      }}
    >
      <div className="w-[80%] truncate">{session.title}</div>
      <Dropdown
        className={isActive ? "block" : "hidden"}
        placement="bottomRight"
        dropdownRender={() => (
          <div className="!p-2.5 !rounded-md bg-[#D6D8E8] text-[#4754AF]">
            {[
              { key: "rename", label: "重命名", icon: <EditOutlined /> },
              { key: "delete", label: "删除", icon: <DeleteOutlined /> },
            ].map((item) => (
              <div
                key={item.key}
                className="flex gap-5 p-[5px] cursor-pointer rounded-md text-left hover:bg-[#CBCEE0]"
                onClick={(e) => {
                  e.stopPropagation();
                  if (item.key === "rename") {
                    onSetEdit(session.sid);
                    onSetTitle(session.title);
                    editRef.current?.focus();
                  } else {
                    setVisible(true);
                  }
                }}
              >
                {item.icon}
                <div>{item.label}</div>
              </div>
            ))}
          </div>
        )}
      >
        <EllipsisOutlined
          style={{
            fontWeight: 700,
            color: "#959DCD",
            cursor: "pointer",
          }}
          onClick={(e) => e.stopPropagation()}
        />
      </Dropdown>
      {visible && (
        <ConfirmModalWrapper
          confirmOpen={visible}
          onClose={() => setVisible(false)}
          title="确认删除会话"
          handleType="delete"
          onConfirmHandle={() => {
            handleDelete(session.sid);
          }}
          component={null}
        />
      )}
    </HistorySessionItem>
  );
};

/* 文件树面板容器 */
.ht-file-tree-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  overflow: hidden;
}

/* 搜索容器 */
.rct-search-container {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #e9ecef;
  background-color: #fff;
}

.rct-search-icon {
  margin-right: 8px;
  color: #6c757d;
}

.rct-search-input {
  border: none;
  outline: none;
  width: 100%;
  font-size: 14px;
  background-color: transparent;
}

/* 树容器 */
.ht-tree-container {
  flex: 1;
  overflow: auto;
  padding: 8px 0;
}

/* 树项 */
.ht-tree-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  margin: 1px 0;
}

.ht-tree-item:hover {
  background-color: #e9ecef;
}

.ht-tree-item-selected {
  background-color: #e2e6ea;
}

.ht-tree-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.ht-tree-item-arrow {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.ht-tree-item-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.ht-tree-item-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

/* 新建文件夹输入框样式 */
.ht-tree-item-new-folder {
  background-color: #f0f8ff;
  border: 1px solid #cce5ff;
}

.ht-tree-item-input {
  width: 100%;
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 14px;
  padding: 2px 0;
  border-bottom: 1px solid #007bff;
}

/* 重命名输入框样式 */
.ht-tree-item-title .ht-tree-item-input {
  background-color: #fff;
  border: 1px solid #2196f3;
  border-radius: 2px;
  padding: 1px 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ht-tree-item-star {
  margin-left: 4px;
  font-size: 10px;
}

.ht-tree-item-hidden {
  opacity: 0.5;
}

/* 上下文菜单 */
.rct-context-menu {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  overflow: hidden;
}

.rct-context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.rct-context-menu-item:hover {
  background-color: #f8f9fa;
}

.rct-context-menu-item svg {
  margin-right: 8px;
}

.rct-context-menu-separator {
  margin: 4px 0;
  border: none;
  border-top: 1px solid #e9ecef;
}

.rct-context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
} 

/* 拖拽相关样式 */
.ht-tree-item-drop-target {
  background-color: #e3f2fd !important;
  border: 2px dashed #2196f3 !important;
  border-radius: 4px;
}

.ht-tree-item-dragging {
  opacity: 0.5;
  background-color: #f5f5f5;
}

/* 多选拖拽时的样式 */
.ht-tree-item-selected.ht-tree-item-dragging {
  opacity: 0.3;
  background-color: #e3f2fd;
  border: 1px dashed #2196f3;
}

.ht-tree-item[draggable="true"] {
  cursor: grab;
}

.ht-tree-item[draggable="true"]:active {
  cursor: grabbing;
}

/* 拖拽提示样式 */
.ht-drag-indicator {
  position: fixed;
  top: 4px;
  right: 4px;
  background-color: #2196f3;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-size: 14px;
  font-weight: 500;
}

/* 拖拽目标文件夹高亮 */
.ht-tree-item-drop-target .ht-tree-item-icon {
  color: #2196f3 !important;
}

.ht-tree-item-drop-target .ht-tree-item-title {
  color: #2196f3 !important;
  font-weight: 600;
} 
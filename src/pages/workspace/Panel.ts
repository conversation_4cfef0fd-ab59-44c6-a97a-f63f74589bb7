import {ResizableBox, ResizeHandle} from "react-resizable";
import styled from "styled-components";

// 添加一个接口定义面板位置和大小
export interface PanelPosition {
    x: number;
    y: number;
    width: number;
    height: number;
    isSnapped: boolean; // 是否已经贴边
    originalSize?: { width: number; height: number }; // 贴边前的原始大小
    resizeHandles?: ResizeHandle[];
}

// 添加一个接口定义贴边预览信息
export interface SnapPreview {
    visible: boolean;
    edge: "left" | "right" | "top" | "bottom" | "panel-left" | "panel-right" | null;
    previewPosition: {
        // 预览时显示的位置和大小
        x: number;
        y: number;
        width: number;
        height: number;
    };
    snapPosition: {
        // 实际应用时的位置和大小
        x: number;
        y: number;
        width: number;
        height: number;
    };
    opacity: number;
}

// 创建一个可调整大小的容器组件
export const ResizableContainer = styled.div`
    position: absolute;
    z-index: 10;
    background: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 30px;
    overflow: hidden;

    /* 添加调整大小时的样式 */

    .react-resizable-handle {
        position: absolute;
        background-repeat: no-repeat;
        background-origin: content-box;
        box-sizing: border-box;
        background-position: center;
        z-index: 1000;
    }

    /* 右下角调整手柄 */

    .react-resizable-handle-se {
        bottom: 0;
        right: 0;
        width: 35px;
        height: 35px;
        cursor: se-resize;

        // 视觉提示

        &::before {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 30px; // 与手柄区域相同大小
            height: 30px;
            border-right: 5px solid transparent; // 初始状态透明
            border-bottom: 5px solid transparent; // 初始状态透明
            border-top: none; // 不显示上边框
            border-left: none; // 不显示左边框
            border-bottom-right-radius: 30px; // 与容器一致的圆角
            transition: all 0.2s ease; // 平滑过渡效果
        }

        &:hover::before {
            border-right-color: #1890ff; // 深紫色
            border-bottom-color: #1890ff; // 深紫色
        }

        &:active::before {
            border-right-color: #1890ff; // 更深的紫色
            border-bottom-color: #1890ff; // 更深的紫色
        }

    }

    /* 左下角调整手柄 */

    .react-resizable-handle-sw {
        bottom: 0;
        left: 0;
        width: 35px;
        height: 35px;
        cursor: sw-resize;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDAgTCAxLjggMCBMIDEuOCA0IEwgMS44IDQuMiBMIDYgNC4yIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+");
        transform: rotate(0deg) !important; /* 覆盖默认的90度旋转 */

        // 视觉提示

        &::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px; // 与手柄区域相同大小
            height: 30px;
            border-left: 5px solid transparent; // 初始状态透明
            border-bottom: 5px solid transparent; // 初始状态透明
            border-top: none; // 不显示上边框
            border-right: none; // 不显示右边框
            border-bottom-left-radius: 30px; // 与 PdfPanelContainer 相同的圆角
            transition: all 0.2s ease; // 平滑过渡效果
        }

        &:hover::before {
            border-left-color: #1890ff; // 深紫色
            border-bottom-color: #1890ff; // 深紫色
        }

        &:active::before {
            border-left-color: #1890ff; // 更深的紫色
            border-bottom-color: #1890ff; // 更深的紫色
        }
    }

    /* 右上角调整手柄 */

    .react-resizable-handle-ne {
        top: -5px;
        right: -5px;
        width: 35px;
        height: 35px;
        cursor: ne-resize;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDAgTCAxLjggMCBMIDEuOCA0IEwgMS44IDQuMiBMIDYgNC4yIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+");
        transform: rotate(180deg) !important; /* 覆盖默认的270度旋转 */
        z-index: 1000;

        // 视觉提示 - 右侧和顶部边框

        &::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            width: 30px;
            height: 30px;
            background-color: transparent;
            border: none;
            border-right: 5px solid transparent;
            border-top: 5px solid transparent;
            border-top-right-radius: 30px;
            transition: all 0.2s ease;
            transform: rotate(180deg);
        }

        &:hover::before {
            border-right-color: #1890ff;
            border-top-color: #1890ff;
        }

        &:active::before {
            border-right-color: #0c64b6;
            border-top-color: #0c64b6;
        }
    }

    /* 左上角调整手柄 */

    .react-resizable-handle-nw {
        top: 0px;
        left: -5px;
        width: 35px;
        height: 35px;
        cursor: nw-resize;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDAgTCAxLjggMCBMIDEuOCA0IEwgMS44IDQuMiBMIDYgNC4yIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+");
        transform: rotate(90deg) !important; /* 覆盖默认的180度旋转 */
        z-index: 1000;

        // 视觉提示 - 左侧和顶部边框

        &::before {
            content: '';
            position: absolute;
            left: -30;
            top: 0;
            width: 30px;
            height: 30px;
            background-color: transparent;
            border: none;
            border-left: 5px solid transparent;
            border-top: 5px solid transparent;
            border-top-left-radius: 30px;
            transition: all 0.2s ease;
            transform: rotate(270deg);
        }

        &:hover::before {
            border-left-color: #1890ff;
            border-top-color: #1890ff;
        }

        &:active::before {
            border-left-color: #0c64b6;
            border-top-color: #0c64b6;
        }
    }
`;

// 创建自定义边缘调整手柄组件
export const CustomResizableBox = styled(ResizableBox)`
    border-radius: 30px;
    overflow: hidden;

    .custom-handle-e {
        position: absolute;
        right: -4px;
        top: 0;
        width: 8px;
        height: calc(100% - 35px); // 减去右下角手柄的高度
        cursor: e-resize;
        background-color: transparent; // 默认透明
        z-index: 10;
        transition: all 0.2s ease; // 平滑过渡效果

        /* 悬停时显示 */

        &:hover {
            background-color: #1890ff; // 使用蓝色，降低透明度
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05); // 添加微妙的阴影
        }

        /* 激活（点击）时显示 */

        &:active {
            background-color: rgba(24, 144, 255, 0.25); // 点击时更深的蓝色
            box-shadow: -2px 0 6px rgba(0, 0, 0, 0.08); // 点击时阴影更明显
        }
    }

    .custom-handle-w {
        position: absolute;
        left: -4px;
        top: 0;
        width: 8px;
        height: calc(100% - 35px); // 减去左下角手柄的高度
        cursor: w-resize;
        background-color: transparent; // 默认透明
        z-index: 10;
        transition: all 0.2s ease; // 平滑过渡效果

        /* 悬停时显示 */

        &:hover {
            background-color: #1890ff; // 使用蓝色，降低透明度
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05); // 添加微妙的阴影，注意阴影方向改变
        }

        /* 激活（点击）时显示 */

        &:active {
            background-color: rgba(24, 144, 255, 0.25); // 点击时更深的蓝色
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.08); // 点击时阴影更明显，注意阴影方向改变
        }
    }


    .custom-handle-n {
        position: absolute;
        top: 0;
        left: 35px;
        height: 4px;
        width: calc(100% - 70px);
        cursor: n-resize;
        background-color: transparent;
        z-index: 1000;
        transition: all 0.2s ease; // 平滑过渡效果

        /* 悬停时显示 */

        &:hover {
            background-color: #1890ff; // 使用蓝色，降低透明度
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); // 添加微妙的阴影，注意阴影方向向下
        }

        /* 激活（点击）时显示 */

        &:active {
            background-color: rgba(24, 144, 255, 0.25); // 点击时更深的蓝色
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08); // 点击时阴影更明显，注意阴影方向向下
        }
    }

    .custom-handle-s {
        position: absolute;
        bottom: 0;
        left: 35px;
        height: 4px;
        width: calc(100% - 70px);
        cursor: s-resize;
        background-color: transparent;
        z-index: 10;
        transition: all 0.2s ease; // 平滑过渡效果

        /* 悬停时显示 */

        &:hover {
            background-color: #1890ff; // 使用蓝色，降低透明度
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05); // 添加微妙的阴影，注意阴影方向向上
        }

        /* 激活（点击）时显示 */

        &:active {
            background-color: rgba(24, 144, 255, 0.25); // 点击时更深的蓝色
            box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.08); // 点击时阴影更明显，注意阴影方向向上
        }
    }
`;

// 修改检测是否接近屏幕边缘的函数
export const checkEdgeSnap = (
    x: number,
    y: number,
    width: number,
    height: number
) => {
    const threshold = 20; // 贴边阈值（像素）
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const previewWidth = 20; // 预览边的宽度

    // 检测左边缘
    if (x < threshold) {
        return {
            edge: "left",
            previewPosition: {
                // 预览时只显示一条边
                x: 0,
                y: 0,
                width: previewWidth,
                height: windowHeight,
            },
            snapPosition: {
                // 实际应用时是半屏大小
                x: 0,
                y: 0,
                width: windowWidth / 3,
                height: windowHeight,
            },
        };
    }

    // 检测右边缘
    if (x + width > windowWidth - threshold) {
        return {
            edge: "right",
            previewPosition: {
                // 预览时只显示一条边
                x: windowWidth - previewWidth,
                y: 0,
                width: previewWidth,
                height: windowHeight,
            },
            snapPosition: {
                // 实际应用时是半屏大小
                x: windowWidth * 2 / 3,
                y: 0,
                width: windowWidth / 3,
                height: windowHeight,
            },
        };
    }

    return null;
};

// 基于鼠标位置检测贴边的函数
export const checkEdgeSnapByMousePosition = (
    mouseX: number,
    mouseY: number,
    panelWidth: number,
    panelHeight: number,
    leftAdjacentPanels: Array<{ x: number; y: number; width: number; height: number; isOpen: boolean; type: string }>,
    rightAdjacentPanels: Array<{ x: number; y: number; width: number; height: number; isOpen: boolean; type: string }>,
    dragDirection?: 'left' | 'right' | null
) => {
    const threshold = 10; // 贴边阈值（像素）
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const previewWidth = 20; // 预览边的宽度
    const menuWidth = 64

    // 检测面板的右边缘
    for (const panel of leftAdjacentPanels) {
        const panelLeft = panel.x + menuWidth;
        const panelRight = panelLeft + panel.width;
        if (Math.abs(mouseX - panelRight) < threshold) {
            return {
                edge: "panel-left",
                previewPosition: {
                    x: panelRight - previewWidth / 2,
                    y: 0,
                    width: previewWidth,
                    height: windowHeight,
                },
                snapPosition: {
                    // 贴合到面板右侧
                    x: panelRight - menuWidth,
                    y: 0,
                    width: panelLeft - panelRight > 0 ? Math.min(panelLeft - panelRight, panelWidth) : panelWidth,
                    height: windowHeight,
                },
            };
        }
    }

    for (const panel of rightAdjacentPanels) {
        const panelLeft = panel.x + menuWidth;
        // 检测面板的左边缘
        if (Math.abs(mouseX - panelLeft) < threshold) {
            return {
                edge: "panel-right",
                previewPosition: {
                    x: panelLeft - previewWidth / 2,
                    y: 0,
                    width: previewWidth,
                    height: windowHeight,
                },
                snapPosition: {
                    // 贴合到面板左侧
                    x: Math.max(0, panelLeft - panelWidth - menuWidth),
                    y: 0,
                    width: panelWidth,
                    height: windowHeight,
                },
            };
        }
    }
    return null;
};

export type WarpperType = ReturnType<typeof ResizableContainer>
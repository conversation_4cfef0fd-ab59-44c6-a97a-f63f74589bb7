import { useState, useEffect } from "react";
import styled from "styled-components";
import { Form, Input, Button, message, Checkbox, Modal, Spin } from "antd";
import { useNavigate } from "react-router-dom";
import { login } from "@/api/user";
import weixinIcon from "@/assets/images/weixin.png";
import { IconFont } from "@/components/IconFont";
// import { getQRCode } from "@/api/user/qrcode";

const LoginContainer = styled.div`
  width: 955px;
  height: 478px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  overflow: hidden;
`;
const Detail = styled.div`
  width: 560px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient(
    -45deg,
    #ff9a9e 0%,
    #fad0c4 25%,
    #a1c4fd 50%,
    #c2e9fb 75%,
    #ff9a9e 100%
  );
  background-size: 400% 400%;
  color: var(--text-primary);
  animation: gradient 15s ease infinite;
  overflow: hidden;
  position: relative;

  &::before,
  &::after {
    content: "";
    position: absolute;
    left: -50%;
    top: -50%;
    right: -50%;
    bottom: -50%;
    background: repeating-linear-gradient(
      -45deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.2) 15%,
      rgba(255, 255, 255, 0.05) 30%,
      rgba(255, 255, 255, 0.15) 45%,
      rgba(255, 255, 255, 0.05) 60%,
      rgba(255, 255, 255, 0.2) 75%,
      rgba(255, 255, 255, 0.1) 90%
    );
    background-size: 200% 200%;
    animation: inkFlow 8s ease-in-out infinite;
  }

  &::after {
    background: repeating-linear-gradient(
      -45deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.15) 20%,
      rgba(255, 255, 255, 0.05) 40%,
      rgba(255, 255, 255, 0.2) 60%,
      rgba(255, 255, 255, 0.05) 80%
    );
    animation: inkFlow 12s ease-in-out infinite;
    opacity: 0.7;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes inkFlow {
    0% {
      transform: translate(-30%, -30%) scale(1.2);
      filter: blur(5px);
    }
    50% {
      transform: translate(0%, 0%) scale(1);
      filter: blur(8px);
    }
    100% {
      transform: translate(-30%, -30%) scale(1.2);
      filter: blur(5px);
    }
  }
`;
const LoginPanel = styled.div`
  flex: 1;
  background: var(--text-secondary);
  display: flex;
  justify-content: center;
  align-items: center;
`;
const Content = styled.div`
  width: 260px;
  height: 312px;
`;

const QRCodeContainer = styled.div`
  width: 200px;
  height: 200px;
  margin: 20px auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`;

const QRCodeWrapper = styled.div`
  width: 180px;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const QRCodePlaceholder = styled.div`
  width: 150px;
  height: 150px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
`;

const TextLink = styled.span`
  color: #1890ff;
  cursor: pointer;
  margin-top: 16px;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
`;

export const Login = () => {
  const [loginType, setLoginType] = useState<"account" | "qrcode">("account");
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [agreed, setAgreed] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState("");
  const [qrCodeExpired, setQrCodeExpired] = useState(false);
  const [loading, setLoading] = useState(false);

  const onLogin = async () => {
    try {
      // 先校验用户信息
      const values = await form.validateFields();

      // 用户信息校验通过后，检查是否勾选协议
      if (!agreed) {
        Modal.confirm({
          title: "提示",
          content: "您尚未同意服务协议和隐私协议，是否现在同意并继续登录？",
          onOk: async () => {
            setAgreed(true);
            // 用户选择同意后，发送登录请求
            const { data } = await login(values);
            if (data) {
              localStorage.setItem("tokenInfo", JSON.stringify({ ...data }));
              navigate("/home");
            } else {
              message.error("登录失败");
            }
          },
          onCancel: () => {
            message.info("请先同意服务协议和隐私协议");
          },
        });
        return;
      }

      // 如果已经勾选协议，直接发送登录请求
      const { data } = await login(values);
      if (data) {
        localStorage.setItem("tokenInfo", JSON.stringify({ ...data }));
        navigate("/home");
      } else {
        message.error("登录失败");
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 获取二维码
  const fetchQRCode = async () => {
    setLoading(true);
    try {
      // const { data } = await getQRCode();
      // setQrCodeUrl(data.url);
      setQrCodeExpired(false);
    } catch (error) {
      message.error("获取二维码失败");
    } finally {
      setLoading(false);
    }
  };

  // 检查二维码扫描状态
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (qrCodeUrl && loginType === "qrcode" && !qrCodeExpired) {
      timer = setInterval(async () => {
        try {
          // const { data } = await checkQRCodeStatus(); // 需要实现这个API
          // if (data.scanned) {
          //   clearInterval(timer);
          //   handleQRCodeSuccess();
          // }
        } catch (error) {
          console.error("检查扫码状态失败:", error);
        }
      }, 2000); // 每2秒检查一次
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [qrCodeUrl, loginType, qrCodeExpired]);

  // 二维码过期检查
  useEffect(() => {
    let expireTimer: NodeJS.Timeout;

    if (qrCodeUrl && loginType === "qrcode") {
      expireTimer = setTimeout(() => {
        setQrCodeExpired(true);
        message.info("二维码已过期，请重新获取");
      }, 5 * 60 * 1000); // 5分钟后过期
    }

    return () => {
      if (expireTimer) {
        clearTimeout(expireTimer);
      }
    };
  }, [qrCodeUrl, loginType]);

  // 处理扫码成功
  const handleQRCodeSuccess = async () => {
    try {
      // 调用接口确认扫码成功
      // const { success, token } = await confirmQRCodeLogin(); // 假设返回成功状态和token
      // if (!success) {
      //   message.error("登录失败");
      //   return;
      // }

      // 检查是否勾选协议
      if (!agreed) {
        Modal.confirm({
          title: "提示",
          content: "扫码成功，请先同意服务协议和隐私协议",
          onOk: () => {
            setAgreed(true);
            // 用户同意后，存储token并跳转页面
            // localStorage.setItem("tokenInfo", JSON.stringify({ token }));
            navigate("/home");
          },
          onCancel: () => {
            message.info("登录已取消");
            // 用户取消后，调用接口取消登录
            // cancelQRCodeLogin(); // 假设有一个取消登录的API
          },
        });
      } else {
        // 已经勾选协议，直接存储token并跳转
        // localStorage.setItem("tokenInfo", JSON.stringify({ token }));
        navigate("/home");
      }
    } catch (error) {
      message.error("登录失败");
    }
  };

  // 切换登录方式时获取新的二维码
  useEffect(() => {
    if (loginType === "qrcode") {
      fetchQRCode();
    }
  }, [loginType]);

  useEffect(() => {
    const tokenInfo = localStorage.getItem("tokenInfo");
    if (!tokenInfo) return;

    try {
      const parsedToken = JSON.parse(tokenInfo);
      if (parsedToken.expire * 1000 > Date.now()) {
        navigate("/home");
      }
    } catch (error) {
      // token 解析失败，可能是无效的 JSON
      localStorage.removeItem("tokenInfo"); // 清除无效的 token
    }
  }, []);

  return (
    <LoginContainer>
      <Detail>
        <div className="text-2xl text-center">AI Thesis Assistant</div>
        <div className="text-[10px] p-10">
          Our AI paper writing assistant is an efficient and intelligent tool
          designed specifically for academic researchers, students, and
          professionals. It integrates advanced natural language processing
          technology and deep learning algorithms, aiming to provide users with
          comprehensive paper writing support from topic selection suggestions,
          outline construction, content writing to literature citation. This
          innovative product can understand complex academic contexts and
          intelligently generate logically clear and well supported paragraph
          content based on user input keywords or topics, greatly saving time in
          data collection and organization. Not only that, the AI paper writing
          assistant also has powerful literature analysis capabilities, which
          can quickly retrieve and integrate relevant academic resources,
          ensuring that the citation of papers is both accurate and
          comprehensive, in line with academic standards. Users can easily
          adjust the structure of articles, modify language styles, and even
          receive optimization suggestions for specific parts through a simple
          interactive interface, making the paper more concise and powerful.
          Whether you are a beginner facing writing difficulties or a senior
          scholar pursuing higher quality academic output, this AI product can
          become your indispensable assistant, making the process of writing
          papers more efficient and smooth, and helping you achieve outstanding
          achievements on the academic path.
        </div>
      </Detail>
      <LoginPanel>
        <Content>
          <div className="text-[var(--text-primary)] text-2xl mb-6">
            welcome
          </div>

          {loginType === "account" ? (
            <>
              <Form form={form} className="w-full">
                <Form.Item
                  name="name"
                  rules={[{ required: true, message: "请输入邮箱" }]}
                >
                  <Input
                    className="h-[40px] border-[var(--text-primary)] pl-2.5"
                    placeholder="请输入手机号或者邮箱"
                  />
                </Form.Item>
                <Form.Item
                  name="pwd"
                  rules={[{ required: true, message: "请输入密码" }]}
                >
                  <Input.Password
                    className="h-[40px] border-[var(--text-primary)] pl-2.5"
                    placeholder="请输入密码"
                  />
                </Form.Item>
              </Form>
              <Button
                type="primary"
                block
                className="h-[40px] bg-[var(--text-primary)]"
                onClick={onLogin}
              >
                登录
              </Button>
              <div className="mt-3 text-center flex justify-center items-center gap-4">
                <div>
                  <span className="text-gray-500">还没有账号？</span>
                  <span
                    className="text-[var(--text-primary)] ml-1 cursor-pointer"
                    onClick={() => navigate("/register")}
                  >
                    立即注册
                  </span>
                </div>
                <img
                  src={weixinIcon}
                  alt="微信登录"
                  onClick={() => setLoginType("qrcode")}
                  // style={{ opacity: loginType === "account" ? 1 : 0.6 }}
                  className="w-7 h-7 cursor-pointer hover:scale-110 transition-transform"
                />
              </div>
            </>
          ) : (
            <QRCodeContainer>
              <QRCodeWrapper>
                <QRCodePlaceholder>
                  {qrCodeUrl && !qrCodeExpired ? (
                    <img
                      src={qrCodeUrl}
                      alt="登录二维码"
                      style={{ width: "100%", height: "100%" }}
                    />
                  ) : (
                    <div
                      className="flex flex-col items-center justify-center cursor-pointer"
                      onClick={fetchQRCode}
                    >
                      {loading ? (
                        <Spin size="large" />
                      ) : (
                        <>
                          <IconFont
                            type="icon-qrcode"
                            style={{ fontSize: "64px", color: "#bfbfbf" }}
                          />
                          <span className="mt-2 text-gray-400">
                            {qrCodeExpired
                              ? "二维码已失效，点击刷新"
                              : "点击获取二维码"}
                          </span>
                        </>
                      )}
                    </div>
                  )}
                </QRCodePlaceholder>
                <TextLink onClick={() => setLoginType("account")}>
                  使用账号密码登录
                </TextLink>
              </QRCodeWrapper>
            </QRCodeContainer>
          )}

          <div className="text-[10px] leading-[10px] mt-4">
            <Checkbox
              className="text-[10px] mr-2.5"
              checked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
            />
            <span>我已阅读并同意</span>
            <a className="text-[var(--text-primary)]" href="/">
              服务协议
            </a>
            <span>和</span>
            <a className="text-[var(--text-primary)]" href="/">
              隐私协议
            </a>
          </div>
        </Content>
      </LoginPanel>
    </LoginContainer>
  );
};

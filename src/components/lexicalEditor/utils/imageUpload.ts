import type { LexicalEditor } from 'lexical';
import { INSERT_IMAGE_COMMAND } from '../plugins/ImagesPlugin';

export interface ImageUploadOptions {
  accept?: string;
  multiple?: boolean;
  onSuccess?: (file: File, src: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

/**
 * 通用的图片上传函数
 * @param editor Lexical 编辑器实例
 * @param options 上传选项
 */
export const uploadImage = (
  editor: LexicalEditor,
  options: ImageUploadOptions = {}
): void => {
  const {
    accept = 'image/*',
    multiple = false,
    onSuccess,
    onError,
    onCancel
  } = options;

  // 创建一个隐藏的文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = accept;
  fileInput.multiple = multiple;
  fileInput.style.display = 'none';
  
  // 处理文件选择
  fileInput.onchange = (event) => {
    const files = (event.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      const file = files[0];
      
      // 使用 FileReader 读取文件
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (result) {
          try {
            // 插入图片到编辑器
            editor.dispatchCommand(INSERT_IMAGE_COMMAND, {
              altText: file.name,
              src: result,
            });
            
            // 调用成功回调
            onSuccess?.(file, result);
          } catch (error) {
            onError?.(`插入图片失败: ${error}`);
          }
        }
      };
      
      reader.onerror = () => {
        onError?.('读取文件失败');
      };
      
      reader.readAsDataURL(file);
    } else {
      // 用户取消了文件选择
      onCancel?.();
    }
    
    // 清理文件输入元素
    document.body.removeChild(fileInput);
  };
  
  // 处理取消事件
  fileInput.oncancel = () => {
    onCancel?.();
    document.body.removeChild(fileInput);
  };
  
  // 添加到 DOM 并触发点击
  document.body.appendChild(fileInput);
  fileInput.click();
};

/**
 * 批量上传图片函数
 * @param editor Lexical 编辑器实例
 * @param options 上传选项
 */
export const uploadMultipleImages = (
  editor: LexicalEditor,
  options: Omit<ImageUploadOptions, 'multiple'> = {}
): void => {
  uploadImage(editor, { ...options, multiple: true });
};

/**
 * 上传特定格式的图片
 * @param editor Lexical 编辑器实例
 * @param formats 允许的图片格式，如 'image/jpeg,image/png'
 * @param options 其他上传选项
 */
export const uploadImagesByFormat = (
  editor: LexicalEditor,
  formats: string,
  options: Omit<ImageUploadOptions, 'accept'> = {}
): void => {
  uploadImage(editor, { ...options, accept: formats });
}; 
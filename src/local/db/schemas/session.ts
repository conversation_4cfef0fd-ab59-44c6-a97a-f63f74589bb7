import {RxJsonSchema} from 'rxdb';

export interface SessionDocType {
    id: string; // 会话ID
    wid: string; // 工作区ID
    title: string; // 标题
    last_at: number; // 最后更新时间
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const sessionSchema: RxJsonSchema<SessionDocType> = {
    title: 'session schema',
    version: 0,
    description: '会话模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        title: {
            type: 'string',
            maxLength: 32,

        },
        last_at: {
            type: 'integer',
            minimum: 0
        },
        create_at: {
            type: 'integer',
            minimum: 0,
        },
        update_at: {
            type: 'integer',
            minimum: 0,
        }
    },
    required: ['id', 'wid', 'title'],
    indexes: ['wid']
};
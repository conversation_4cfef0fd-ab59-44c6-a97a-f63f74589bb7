import { FC } from "react";
import { Divider } from "antd";
import { Chat } from "./components/chat";
import { HistorySession } from "./components/history-session";
import { Toolbar } from "./components/chat-toolbar";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { useChatData } from "./hooks/use-chat-data";
import styled from "styled-components";

// 添加样式，使工具栏可拖拽
const DraggableToolbar = styled.div`
  cursor: move;
`;

export const ChatPanel: FC = () => {
  const { openHistory } = useChatStore();
  useChatData();

  return (
    <div className="h-full bg-[#E0E2F3] scrollbar-custom">
      {/* 顶部工具栏 - 添加drag-handle类使其可拖拽 */}
      <DraggableToolbar className="drag-handle">
        <Toolbar />
      </DraggableToolbar>

      {/* 主要内容区域 */}
      <div className="flex-1 flex h-[calc(100%-60px)] overflow-x-scroll overflow-y-hidden">
        {/* 聊天主区域 */}
        <Chat />

        {/* 历史会话区域 */}
        <div
          className={`flex transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] ${
            openHistory ? "w-[300px]" : "w-0"
          }`}
        >
          <div
            className="flex transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]"
            style={{
              opacity: openHistory ? 1 : 0,
              transform: openHistory ? "translateX(0)" : "translateX(20px)",
              width: openHistory ? "100%" : 0,
              overflow: "hidden",
            }}
          >
            <Divider
              type="vertical"
              style={{
                height: "100%",
                borderInlineStartWidth: "3px",
                borderInlineStartColor: "white",
              }}
              className="m-0 shrink-0"
            />
            <HistorySession />
          </div>
        </div>
      </div>
    </div>
  );
};

import {RxJsonSchema} from 'rxdb';

export interface NodeTagDocType {
    id: string; // 关联记录ID
    node_id: string; // 节点ID
    tag_id: string; // 标签ID
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const nodeTagSchema: RxJsonSchema<NodeTagDocType> = {
    title: 'nodeTag schema',
    version: 0,
    description: '节点标签关联模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        node_id: {
            type: 'string',
            maxLength: 100
        },
        tag_id: {
            type: 'string',
            maxLength: 100
        },
        create_at: {
            type: 'number',
            minimum: 0
        },
        update_at: {
            type: 'number',
            minimum: 0
        }
    },
    required: ['id', 'node_id', 'tag_id'],
    indexes: ['node_id', 'tag_id', ['node_id', 'tag_id']]
};
import { <PERSON>, <PERSON>lt<PERSON>, But<PERSON> } from "antd";
import styled from "styled-components";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { PlusMini, ChatSideIcon } from "@/components/icons";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { CloseOutlined } from "@ant-design/icons";
import { useMemo } from "react";

const ToolbarContainer = styled.div`
  flex: 1;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 30px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding-left: 20px;
`;

// 文本溢出隐藏组件
const TruncatedText = styled.div`
  max-width: 150px; /* 可以根据需要调整宽度 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
`;

const CloseButton = styled(Button)`
  border: none;
  background: transparent;
  color: #666;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  &:hover {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
  }
`;

export const Toolbar = () => {
  const { sessionId, setOpenHistory, cleanSession, chatList = [], sessions, session_detail_list } = useChatStore();
  const toggleChatPanel = usePanelOpenStore.getState().toggleChatPanel;

  // 处理关闭按钮点击事件
  const handleClose = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发拖拽区域的事件
    e.stopPropagation();
    // 阻止默认行为
    e.preventDefault();
    // 直接关闭面板
    toggleChatPanel(false);
  };

  const sessionTitle =  useMemo(() => {
    const currentSession = sessions.find(session => session.sid === sessionId)
    return currentSession?.title || ''
  }, [sessions, sessionId] )

  return (
    <ToolbarContainer>
      {/* <div>{chatList?.[0]?. question}</div> */}
      <TruncatedText title={sessionTitle}>
        {sessionTitle}
      </TruncatedText>
      <Space>
        <Tooltip title="新建聊天">
          <div
            onClick={() => {
              if (sessionId !== 0) {
                cleanSession();
              }
            }}
          >
            <PlusMini color="#3D56BA" size={15} />
          </div>
        </Tooltip>
        <Tooltip title="聊天记录">
          <div onClick={setOpenHistory}>
            <ChatSideIcon color="#3D56BA" size={15} />
          </div>
        </Tooltip>
        <Tooltip title="关闭">
          <CloseButton 
            icon={<CloseOutlined />} 
            onClick={handleClose}
            onMouseDown={(e) => {
              // 阻止鼠标按下事件冒泡，防止触发拖拽开始
              e.stopPropagation();
            }}
          />
        </Tooltip>
      </Space>
    </ToolbarContainer>
  );
};

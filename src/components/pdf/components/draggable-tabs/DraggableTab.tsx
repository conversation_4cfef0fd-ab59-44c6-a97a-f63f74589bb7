import React, {useEffect, useRef} from 'react';
import {useDrag, useDrop} from 'react-dnd';
import {CloseOutlined} from '@ant-design/icons';
import {DragItem, DragTypes, DropResult, TabItem} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import {getEmptyImage} from "react-dnd-html5-backend";
import {usePdfStore} from "@/store/pdf-store.ts";

interface DraggableTabProps {
    tab: TabItem;
    index: number;
    windowId: string;
    isActive: boolean;
    onTabClick: (key: string) => void;
    onTabClose: (key: string) => void;
    onTabMove: (dragItem: TabItem, hoverItem: TabItem, windowId: string) => void;
    onTabDragEnd: (item: DragItem, result: DropResult | null, monitor: any) => void;
    containerRef?: React.RefObject<HTMLElement | null>; // 标签容器的引用
}

export const DraggableTab: React.FC<DraggableTabProps> = ({
                                                              tab,
                                                              index,
                                                              windowId,
                                                              isActive,
                                                              onTabClick,
                                                              onTabClose,
                                                              onTabMove,
                                                              onTabDragEnd,
                                                              containerRef,
                                                          }) => {
    const ref = useRef<HTMLDivElement>(null);
    const {checkIfOutsideContainer} = useDragTab()
    const {setDragHoverContainer} = usePdfStore((state) => ({
        setDragHoverContainer: state.setDragHoverContainer
    }))
    const [{isDragging}, drag, dragPreview] = useDrag({
        type: DragTypes.TAB,
        item: (): DragItem => {
            const sourceRect = ref.current?.getBoundingClientRect();
            return {
                type: DragTypes.TAB,
                id: tab.key,
                index,
                tabItem: tab,
                width: sourceRect?.width,
                height: sourceRect?.height,
                containerRect: containerRef?.current?.getBoundingClientRect() || null,
                windowId: windowId,
            };
        },
        collect: (monitor) => {
            const isDragging = monitor.isDragging();
            return {isDragging};
        },
        end: (item, monitor) => {
            const result = monitor.getDropResult<DropResult>();
            console.log("DraggableTab end", result, checkIfOutsideContainer(item, monitor.getSourceClientOffset()))
            onTabDragEnd(item, result, monitor);
        },
    });

    const [, drop] = useDrop({
        accept: DragTypes.TAB,
        hover: (item: DragItem, monitor) => {
            if (!ref.current) {
                return;
            }

            // 如果拖拽的是同一个元素，不做处理
            if (item.tabItem.key === tab.key) {
                return;
            }
            // 获取边界矩形
            const hoverBoundingRect = ref.current.getBoundingClientRect();

            // 获取中点
            const hoverMiddleX = hoverBoundingRect.x + hoverBoundingRect.width / 2;

            // 获取鼠标位置
            const clientOffset = monitor.getSourceClientOffset();

            if (!clientOffset) {
                return;
            }

            // 只有当鼠标越过一半时才执行移动
            if (clientOffset.x < hoverMiddleX && (clientOffset.x + (item.width ?? 0)) < hoverMiddleX) {
                return;
            }

            if (clientOffset.x > hoverMiddleX) {
                return;
            }
            setDragHoverContainer(containerRef?.current?.getBoundingClientRect() || null)
            console.log("DraggableTab hovering", item.tabItem, tab)
            // 执行移动
            onTabMove(item.tabItem, tab, windowId);
        },
    });

    // 连接拖拽和放置
    drag(drop(ref));

    useEffect(() => {
        dragPreview(getEmptyImage(), {captureDraggingState: true});
    }, [dragPreview])

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        onTabClick(tab.key);
    };

    const handleClose = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onTabClose(tab.key);
    };

    return (
        <div
            ref={ref}
            className={`
        group relative flex items-center px-3 py-2 cursor-pointer select-none
        border-r border-gray-200 min-w-0 max-w-48
        transition-all duration-200 ease-in-out
        ${isActive
                ? 'bg-white border-b-2 border-b-blue-500 text-blue-600'
                : 'bg-gray-50 hover:bg-gray-100 text-gray-700'
            }
        ${isDragging ? 'opacity-50' : ''}
      `}
            onClick={handleClick}
        >
            {/* 标签内容 */}
            <div className="flex items-center min-w-0 flex-1">
        <span className="truncate text-sm font-medium">
          {tab.label}
        </span>
            </div>

            {/* 关闭按钮 */}
            {tab.closable !== false && (
                <button
                    className={`
            ml-2 p-1 rounded-full opacity-0 group-hover:opacity-100
            hover:bg-gray-200 transition-opacity duration-200
            ${isActive ? 'opacity-70 hover:opacity-100' : ''}
          `}
                    onClick={handleClose}
                >
                    <CloseOutlined className="text-xs"/>
                </button>
            )}
        </div>
    );
};

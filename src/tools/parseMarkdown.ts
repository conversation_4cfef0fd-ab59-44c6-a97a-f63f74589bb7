// 添加一个预处理函数来处理数学公式
export const processContent = (content: string): string => {
  // 1. 先处理块级数学公式 \[ ... \]
  let processed = content.replace(/\\\[([\s\S]*?)\\\]/g, (match, formula) => {
    // 保留公式内部的原始内容，只处理外部标记
    return "$$" + formula + "$$";
  });

  // 2. 处理行内数学公式 \( ... \)
  processed = processed.replace(/\\\(([\s\S]*?)\\\)/g, (match, formula) => {
    return "$" + formula + "$";
  });

  // 3. 确保反斜杠在数学环境中正确显示
  // 这里我们不处理所有反斜杠，只处理需要额外转义的情况
  return processed;
};
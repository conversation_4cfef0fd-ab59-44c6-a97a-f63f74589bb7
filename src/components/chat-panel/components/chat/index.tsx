import { FC, memo, useRef } from "react";
import { ChatInput } from "../chat-input";
import { ChatList } from "../chat-list";
import { ChatStop } from "../chat-stop";
import { useReferenceManager } from "../../hooks/use-reference-manager";
import { useChatStore } from "@/store/workerspace-store/chat-store";

export const Chat: FC = memo(() => {
  // Refs
  const footerInputRef = useRef<HTMLDivElement>(null);

  // Store
  const { footerRefs, isFetchStream, setCurrentChat, setCurrentChatRid } =
    useChatStore();

  // 使用 Reference 管理 Hook
  useReferenceManager();

  return (
    <div className="w-full h-full p-2.5 rounded-2xl flex-1 flex flex-col relative">
      {/* 聊天记录区域 */}
      <ChatList />

      {/* 底部输入区 */}
      <div
        ref={footerInputRef}
        className="relative"
        onClick={() => {
          // 如果正在获取数据流，不重置聊天状态，避免影响流处理
          if (isFetchStream) {
            return;
          }
          setCurrentChat(null);
          setCurrentChatRid(-1);
        }}
      >
        {isFetchStream && <ChatStop />}
        <ChatInput rid={-1} footerRefs={footerRefs} isFooter />
      </div>
    </div>
  );
});

export type CreateProjectType = {
  cid: string | number
  title: string
  attachs?: Array<{
    fname: string
    content: Blob
  }>
}
export type UpdateProjectType = {
  wid: string | number
  title?: string
  cid: string | number
}
export type DeleteProjectType = {
  wid: string | number
}
export type ProjectListType = {
  cid: string | number
  page?: number
  page_size?: number
  keyword?: string
  from?: number // 0: 正常, 1: 回收站
}



export type RestoreProjectType = {
  wids: Array<string | number>
}



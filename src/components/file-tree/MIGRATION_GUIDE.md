# 从 react-complex-tree 迁移到 headless-tree

本文档提供了将文件树组件从 `react-complex-tree` 迁移到 `headless-tree` 的详细指南。`headless-tree` 是 `react-complex-tree` 的官方后继者，提供了更好的性能、更小的包体积和更灵活的定制能力。

## 迁移步骤概述

1. 安装 headless-tree 依赖
2. 创建新的 HeadlessFileTreePanel 组件
3. 更新样式文件
4. 在现有代码中替换组件引用
5. 测试和优化

## 1. 安装依赖

```bash
npm install @headless-tree/core @headless-tree/react --legacy-peer-deps
```

## 2. 组件迁移

### 主要区别

- `react-complex-tree` 提供了完整的 UI 组件，而 `headless-tree` 只提供核心功能，UI 需要自己实现
- `headless-tree` 使用 hooks API，而不是组件 API
- `headless-tree` 采用模块化设计，功能以插件形式提供

### 迁移 API 对照表

| react-complex-tree | headless-tree |
|-------------------|---------------|
| `<UncontrolledTreeEnvironment>` | `useTree()` hook |
| `<Tree>` | 自行渲染树结构 |
| `dataProvider` | `dataLoader` 选项 |
| `viewState` | `initialState` 选项 |
| `canDragAndDrop` | `dragAndDropFeature` |
| `canDropOnFolder` | 通过 `dragAndDropFeature` 配置 |
| `canReorderItems` | 通过 `dragAndDropFeature` 配置 |
| `canSearch` | `searchFeature` |

## 3. 样式迁移

`headless-tree` 不提供默认样式，需要自己实现。我们创建了 `HeadlessFileTreePanel.css` 文件，提供了与原有组件类似的样式。

主要样式类对照：

| react-complex-tree | headless-tree |
|-------------------|---------------|
| `rct-tree-item-li` | `ht-tree-item` |
| `rct-tree-item-title-container` | `ht-tree-item-content` |
| `rct-tree-item-button` | 不需要，直接使用 div |
| `rct-tree-item-arrow` | `ht-tree-item-arrow` |
| `rct-tree-item-icon` | `ht-tree-item-icon` |
| `rct-tree-item-title` | `ht-tree-item-title` |

## 4. 使用示例

### 原来的用法 (react-complex-tree)

```tsx
<FileTreePanel
  initialData={fileTreeData}
  onFileSelect={handleFileSelect}
  onFileCreate={handleFileCreate}
  onFileDelete={handleFileDelete}
  onFileRename={handleFileRename}
  showHiddenFiles={false}
  searchable={true}
/>
```

### 新的用法 (headless-tree)

```tsx
<HeadlessFileTreePanel
  initialData={fileTreeData}
  onFileSelect={handleFileSelect}
  onFileCreate={handleFileCreate}
  onFileDelete={handleFileDelete}
  onFileRename={handleFileRename}
  showHiddenFiles={false}
  searchable={true}
/>
```

## 5. 高级功能迁移

### 拖放功能

在 `headless-tree` 中，拖放功能通过 `dragAndDropFeature` 提供：

```tsx
import { dragAndDropFeature } from "@headless-tree/core";

// 在 useTree 配置中添加
features: [
  // 其他功能...
  dragAndDropFeature,
]
```

### 搜索功能

在 `headless-tree` 中，搜索功能通过 `searchFeature` 提供：

```tsx
import { searchFeature } from "@headless-tree/core";

// 在 useTree 配置中添加
features: [
  // 其他功能...
  searchFeature,
]

// 使用搜索功能
const treeWithSearch = tree as unknown as { search?: (term: string) => void };
if (treeWithSearch.search) {
  treeWithSearch.search(searchTerm);
}
```

### 重命名功能

在 `headless-tree` 中，重命名功能通过 `renamingFeature` 提供：

```tsx
import { renamingFeature } from "@headless-tree/core";

// 在 useTree 配置中添加
features: [
  // 其他功能...
  renamingFeature,
]
```

## 6. 性能优化

`headless-tree` 提供了更好的性能优化选项：

1. **虚拟化**：可以轻松与任何虚拟化库集成
2. **树摇**：只包含你需要的功能
3. **异步数据加载**：通过 `asyncDataLoaderFeature` 支持异步数据加载

## 7. 常见问题

### 类型错误

如果遇到类型错误，确保正确导入所需的类型：

```tsx
import { 
  FeatureImplementation,
  TreeInstance
} from "@headless-tree/core";
```

### 搜索功能不可用

`headless-tree` 中的搜索功能需要额外处理：

```tsx
const treeWithSearch = tree as unknown as { search?: (term: string) => void };
if (treeWithSearch.search) {
  treeWithSearch.search(searchTerm);
}
```

### 功能数组过滤

确保正确过滤功能数组中的 `undefined` 值：

```tsx
const features: FeatureImplementation[] = useMemo(() => {
  const featureArray = [
    // 功能列表...
    searchable ? searchFeature : undefined,
  ];
  
  return featureArray.filter((feature): feature is FeatureImplementation => feature !== undefined);
}, [searchable]);
```

## 结论

迁移到 `headless-tree` 可能需要一些初始工作，但长期来看，它提供了更好的性能、更小的包体积和更灵活的定制能力。通过遵循本指南，您可以平滑地完成迁移过程。 
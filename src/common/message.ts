import {saveNotebook} from "@/api/notebook";
import {workspaceService} from "@/local";
import {createProject} from "@/api/project";

// 创建默认工作区
const createDefaultWorkspace = async () => {
    const workspace: any = await createProject({
        cid: "0",
        title: "默认工作区"
    })
    return workspace.wid
}
window.addEventListener('message', async (event) => {
    try {
        let data
        switch (event.data.type) {
            // 保存笔记
            case 'SAVE_NOTES_REQUEST':
                const wid = event.data.data.wid
                const notes: any = await workspaceService.noteContent({wid})
                await saveNotebook({
                    wid: wid,
                    content: (notes.content && notes.content.trim().length > 0 ? (notes.content + "\n") : "") + event.data.data.content
                })
                data = {wid}
                break
            // 获取工作区
            case 'GET_WORKSPACE_REQUEST':
                data = await workspaceService.list({cid: "-1", from: 0, keyword: "", page: 1, page_size: 100})
                if (data.total === 0) {
                    const wid = await createDefaultWorkspace()
                    data = {
                        list: [{
                            wid: wid,
                            title: "默认工作区",
                        }],
                        total: 1,
                        page_count: 1
                    }
                }
                break
            case 'SAVE_PDF_REQUEST':
                data = await workspaceService.attach({
                    wid: event.data.data.wid,
                    attachs: [{
                        fname: event.data.data.filename,
                        content: event.data.data.content
                    }]
                })
                break
            default :
                return
        }
        console.log(event.data.type, event, data)
        window.postMessage({
            type: event.data.type.replace('_REQUEST', '_RESPONSE'),
            messageId: event.data.messageId,
            code: 200,
            data: data
        }, '*')
    } catch (e) {
        console.log(event.data.type, event, e)
        window.postMessage({
            type: event.data.type.replace('_REQUEST', '_RESPONSE'),
            messageId: event.data.messageId,
            code: 500,
            message: e
        }, '*')
    }
})

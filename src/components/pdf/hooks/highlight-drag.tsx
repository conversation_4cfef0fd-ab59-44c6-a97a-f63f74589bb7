import {CustomHighlight, getNodeHighlight, modeType, usePdfStore} from "@/store/pdf-store.ts";
import {useCallback} from "react";
import {updateHighlight} from "@/api/pdf";
import {getWsId} from "@/tools/params.ts";
import {scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import {useFlowStore} from "@/store/flow-store.ts";

export const useHighlightDrag = () => {
    const {updateHighlights} = usePdfStore((state) => ({
        updateHighlights: state.updateHighlights
    }))
    const {updateNodes, setCenter} = useFlowStore((state) => ({
        updateNodes: state.updateNodes,
        setCenter: state.setCenter
    }))
    const onDragStop = useCallback(async (highlight: CustomHighlight) => {
        const pdfState = usePdfStore.getState()
        const highlighterUtils = pdfState.pdfs.get(highlight.aid)?.pdfHighlighterUtils!
        const wid = getWsId()
        const highlightTip: Tip = {
            position: scaledPositionToViewport(highlight.position, highlighterUtils.getViewer()!),
            content: <HighlightToolbar customHighlight={highlight}/>,
        }
        console.log(highlight, 'highlight---------')
        // 弹出工具栏  text-insert 不弹工具栏
        const isTextInsert = highlight.type === "text-insert"
        !isTextInsert && highlighterUtils.setTip(highlightTip)
        // 节点
        updateNodes([{id: highlight.nid, selected: true, selectedFromHighlight: true}], true)
        setCenter(highlight.nid)
        // 不更新
        if (JSON.stringify(highlight.position.boundingRect) === JSON.stringify(getNodeHighlight([highlight.nid])[0].position.boundingRect)) {
            return
        }
        try {
            // 创建包含完整信息的mark对象
            const completeMarkInfo = {
                ...highlight.position,
                type: highlight.type,
                content: highlight.content
            }
            // 更新高亮
            await updateHighlight({
                wid: wid,
                mid: highlight.id,
                mark: JSON.stringify(completeMarkInfo),
                color: highlight?.color?.slice(1) || ''
            })
        } catch (e) {
            console.log(e)
            return
        }
        // 高亮
        updateHighlights(highlight.aid, {
            id: highlight.id,
            position: highlight.position,
            content: highlight.content
        })

        // 结束编辑状态
        highlighterUtils.toggleEditInProgress(false)
    }, [updateHighlight, updateHighlights, updateNodes, setCenter])
    return {
        onDragStop
    }
}
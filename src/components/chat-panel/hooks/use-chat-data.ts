import {useCallback, useEffect} from 'react'
import {getSessionDetail, getSessionList} from "@/api/chat";
import {useChatStore} from '@/store/workerspace-store/chat-store'

export const useChatData = () => {
  const wid = new URLSearchParams(window.location.search).get('wid') || '';
  const { sessions, setSessions, setChatList, setSessionId, setSessionDetailList } = useChatStore()

  const onFetchData = useCallback(async () => {
    try {
      const res = await getSessionList({
        wid: wid,
        page: 1,
        page_size: 100
      })
      const list = res.data.list || []
      // setSessions(list)
      setSessions(list)
      if (list && list.length > 0) {
        setSessionId(list[0].sid)
      }
      if (list.length > 0) {
        onFetchChatList(list[0].sid, 0)
      } else {
        setChatList([])
      }


    } catch (error) {
      console.error(error)
    }
  }, [wid, setSessions])

  const onFetchChatList = useCallback(async (sid: string, rid: number) => {
    try {
      const res = await getSessionDetail({
        sid: sid,
        rid: rid,
        page_size: 10
      })
      if ((res as any).code === 0) {
        const chatList = res.data.chat ? res.data.chat : []
        setChatList(chatList)
        setSessionDetailList([{
          sid: sid,
          chatList: chatList
        }])
      }
    } catch (error) {
      console.error(error)
    }
  }, [setChatList])

  useEffect(() => {
    onFetchData()
  }, [wid])
} 
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 */
.PlaygroundEditorTheme__ltr {
  text-align: left;
}
.PlaygroundEditorTheme__rtl {
  text-align: right;
}
.PlaygroundEditorTheme__paragraph {
  margin: 0;
  position: relative;
}
.PlaygroundEditorTheme__quote {
  margin: 0;
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}
.PlaygroundEditorTheme__h1 {
  font-size: 24px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
}
.PlaygroundEditorTheme__h2 {
  font-size: 15px;
  color: rgb(101, 103, 107);
  font-weight: 700;
  margin: 0;
  text-transform: uppercase;
}
.PlaygroundEditorTheme__h3 {
  font-size: 12px;
  margin: 0;
  text-transform: uppercase;
}
.PlaygroundEditorTheme__indent {
  --lexical-indent-base-value: 40px;
}
.PlaygroundEditorTheme__textBold {
  font-weight: bold;
}
.PlaygroundEditorTheme__paragraph mark {
  background-color: unset;
}
.PlaygroundEditorTheme__textHighlight {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
}
.PlaygroundEditorTheme__textItalic {
  font-style: italic;
}
.PlaygroundEditorTheme__textUnderline {
  text-decoration: underline;
}

.PlaygroundEditorTheme__textStrikethrough {
  text-decoration: line-through;
}

.PlaygroundEditorTheme__textUnderlineStrikethrough {
  text-decoration: underline line-through;
}

.PlaygroundEditorTheme__tabNode {
  position: relative;
  text-decoration: none;
}

.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textUnderline::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0.15em;
  border-bottom: 0.1em solid currentColor;
}

.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textStrikethrough::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0.69em;
  border-top: 0.1em solid currentColor;
}

.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textUnderlineStrikethrough::before,
.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textUnderlineStrikethrough::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
}

.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textUnderlineStrikethrough::before {
  top: 0.69em;
  border-top: 0.1em solid currentColor;
}

.PlaygroundEditorTheme__tabNode.PlaygroundEditorTheme__textUnderlineStrikethrough::after {
  bottom: 0.05em;
  border-bottom: 0.1em solid currentColor;
}

.PlaygroundEditorTheme__textSubscript {
  font-size: 0.8em;
  vertical-align: sub !important;
}
.PlaygroundEditorTheme__textSuperscript {
  font-size: 0.8em;
  vertical-align: super;
}
.PlaygroundEditorTheme__textCode {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}
.PlaygroundEditorTheme__textLowercase {
  text-transform: lowercase;
}
.PlaygroundEditorTheme__textUppercase {
  text-transform: uppercase;
}
.PlaygroundEditorTheme__textCapitalize {
  text-transform: capitalize;
}
.PlaygroundEditorTheme__hashtag {
  background-color: rgba(88, 144, 255, 0.15);
  border-bottom: 1px solid rgba(88, 144, 255, 0.3);
}
.PlaygroundEditorTheme__link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}
.PlaygroundEditorTheme__link:hover {
  text-decoration: underline;
  cursor: pointer;
}
.PlaygroundEditorTheme__blockCursor {
  display: block;
  pointer-events: none;
  position: absolute;
}
.PlaygroundEditorTheme__blockCursor:after {
  content: '';
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: CursorBlink 1.1s steps(2, start) infinite;
}
@keyframes CursorBlink {
  to {
    visibility: hidden;
  }
}
.PlaygroundEditorTheme__code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  overflow-x: auto;
  position: relative;
  tab-size: 2;
}
.PlaygroundEditorTheme__code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}
.PlaygroundEditorTheme__tableScrollableWrapper {
  overflow-x: auto;
  margin: 0px 25px 30px 0px;
}
.PlaygroundEditorTheme__tableScrollableWrapper > .PlaygroundEditorTheme__table {
  /* Remove the table's vertical margin and put it on the wrapper */
  margin-top: 0;
  margin-bottom: 0;
}
.PlaygroundEditorTheme__tableAlignmentCenter {
  margin-left: auto;
  margin-right: auto;
}
.PlaygroundEditorTheme__tableAlignmentRight {
  margin-left: auto;
}
.PlaygroundEditorTheme__table {
  border-collapse: collapse;
  border-spacing: 0;
  overflow-y: scroll;
  overflow-x: scroll;
  table-layout: fixed;
  width: fit-content;
  margin-top: 25px;
  margin-bottom: 30px;
}
.PlaygroundEditorTheme__tableScrollableWrapper.PlaygroundEditorTheme__tableFrozenRow {
  /* position:sticky needs overflow:clip or visible
     https://github.com/w3c/csswg-drafts/issues/865#issuecomment-350585274 */
  overflow-x: clip;
}
.PlaygroundEditorTheme__tableFrozenRow tr:nth-of-type(1) > td {
  overflow: clip;
  background-color: #ffffff;
  position: sticky;
  z-index: 2;
  top: 44px;
}
.PlaygroundEditorTheme__tableFrozenRow tr:nth-of-type(1) > th {
  overflow: clip;
  background-color: #f2f3f5;
  position: sticky;
  z-index: 2;
  top: 44px;
}
.PlaygroundEditorTheme__tableFrozenRow tr:nth-of-type(1) > th:after,
.PlaygroundEditorTheme__tableFrozenRow tr:nth-of-type(1) > td:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-bottom: 1px solid #bbb;
}
.PlaygroundEditorTheme__tableFrozenColumn tr > td:first-child {
  background-color: #ffffff;
  position: sticky;
  z-index: 2;
  left: 0;
}
.PlaygroundEditorTheme__tableFrozenColumn tr > th:first-child {
  background-color: #f2f3f5;
  position: sticky;
  z-index: 2;
  left: 0;
}
.PlaygroundEditorTheme__tableFrozenColumn tr > :first-child::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  border-right: 1px solid #bbb;
}
.PlaygroundEditorTheme__tableRowStriping tr:nth-child(even),
.PlaygroundEditorTheme__tableFrozenColumn
  .PlaygroundEditorTheme__table.PlaygroundEditorTheme__tableRowStriping
  tr:nth-child(even)
  > td:first-child {
  background-color: #f2f5fb;
}
.PlaygroundEditorTheme__tableSelection *::selection {
  background-color: transparent;
}
.PlaygroundEditorTheme__tableSelected {
  outline: 2px solid rgb(60, 132, 244);
}
.PlaygroundEditorTheme__tableCell {
  border: 1px solid #bbb;
  width: 75px;
  vertical-align: top;
  text-align: start;
  padding: 6px 8px;
  position: relative;
  outline: none;
  overflow: auto;
}
/*
  A firefox workaround to allow scrolling of overflowing table cell
  ref: https://bugzilla.mozilla.org/show_bug.cgi?id=1904159
*/
.PlaygroundEditorTheme__tableCell > * {
  overflow: inherit;
}
.PlaygroundEditorTheme__tableCellResizer {
  position: absolute;
  right: -4px;
  height: 100%;
  width: 8px;
  cursor: ew-resize;
  z-index: 10;
  top: 0;
}
.PlaygroundEditorTheme__tableCellHeader {
  background-color: #f2f3f5;
  text-align: start;
}
.PlaygroundEditorTheme__tableCellSelected {
  caret-color: transparent;
}
.PlaygroundEditorTheme__tableCellSelected::after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: highlight;
  mix-blend-mode: multiply;
  content: '';
  pointer-events: none;
}
.PlaygroundEditorTheme__tableAddColumns {
  position: absolute;
  background-color: #eee;
  height: 100%;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
}
.PlaygroundEditorTheme__tableAddColumns:after {
  background-image: url(../images/icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
.PlaygroundEditorTheme__tableAddColumns:hover,
.PlaygroundEditorTheme__tableAddRows:hover {
  background-color: #c9dbf0;
}
.PlaygroundEditorTheme__tableAddRows {
  position: absolute;
  width: calc(100% - 25px);
  background-color: #eee;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
}
.PlaygroundEditorTheme__tableAddRows:after {
  background-image: url(../images/icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
@keyframes table-controls {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.PlaygroundEditorTheme__tableCellResizeRuler {
  display: block;
  position: absolute;
  width: 1px;
  background-color: rgb(60, 132, 244);
  height: 100%;
  top: 0;
}
.PlaygroundEditorTheme__tableCellActionButtonContainer {
  display: block;
  right: 5px;
  top: 6px;
  position: absolute;
  z-index: 4;
  width: 20px;
  height: 20px;
}
.PlaygroundEditorTheme__tableCellActionButton {
  background-color: #eee;
  display: block;
  border: 0;
  border-radius: 20px;
  width: 20px;
  height: 20px;
  color: #222;
  cursor: pointer;
}
.PlaygroundEditorTheme__tableCellActionButton:hover {
  background-color: #ddd;
}
.PlaygroundEditorTheme__characterLimit {
  display: inline;
  background-color: #ffbbbb !important;
}
.PlaygroundEditorTheme__ol1 {
  padding: 0;
  margin: 0;
  list-style-position: outside;
}
.PlaygroundEditorTheme__ol2 {
  padding: 0;
  margin: 0;
  list-style-type: upper-alpha;
  list-style-position: outside;
}
.PlaygroundEditorTheme__ol3 {
  padding: 0;
  margin: 0;
  list-style-type: lower-alpha;
  list-style-position: outside;
}
.PlaygroundEditorTheme__ol4 {
  padding: 0;
  margin: 0;
  list-style-type: upper-roman;
  list-style-position: outside;
}
.PlaygroundEditorTheme__ol5 {
  padding: 0;
  margin: 0;
  list-style-type: lower-roman;
  list-style-position: outside;
}
.PlaygroundEditorTheme__ul {
  padding: 0;
  margin: 0;
  list-style-position: outside;
}
.PlaygroundEditorTheme__listItem {
  margin: 0 32px;
  font-family: var(--listitem-marker-font-family);
  font-size: var(--listitem-marker-font-size);
  background-color: var(--listitem-marker-background-color);
}
.PlaygroundEditorTheme__listItem::marker {
  color: var(--listitem-marker-color);
  background-color: var(--listitem-marker-background-color);
  font-family: var(--listitem-marker-font-family);
  font-size: var(--listitem-marker-font-size);
}
.PlaygroundEditorTheme__listItemChecked,
.PlaygroundEditorTheme__listItemUnchecked {
  position: relative;
  margin-left: 0.5em;
  margin-right: 0.5em;
  padding-left: 1.5em;
  padding-right: 1.5em;
  list-style-type: none;
  outline: none;
  display: block;
  min-height: 1.5em;
}
.PlaygroundEditorTheme__listItemChecked > *,
.PlaygroundEditorTheme__listItemUnchecked > * {
  margin-left: 0.01em;
}
.PlaygroundEditorTheme__listItemUnchecked:before,
.PlaygroundEditorTheme__listItemChecked:before {
  content: '\200B';
  width: 0.9em;
  height: 0.9em;
  top: 50%;
  left: 0;
  cursor: pointer;
  display: block;
  background-size: cover;
  position: absolute;
  transform: translateY(-50%);
}
.PlaygroundEditorTheme__listItemChecked {
  text-decoration: line-through;
}
.PlaygroundEditorTheme__listItemUnchecked:focus:before,
.PlaygroundEditorTheme__listItemChecked:focus:before {
  box-shadow: 0 0 0 2px #a6cdfe;
  border-radius: 2px;
}
.PlaygroundEditorTheme__listItemUnchecked:before {
  border: 1px solid #999;
  border-radius: 2px;
}
.PlaygroundEditorTheme__listItemChecked:before {
  border: 1px solid rgb(61, 135, 245);
  border-radius: 2px;
  background-color: #3d87f5;
  background-repeat: no-repeat;
}
.PlaygroundEditorTheme__listItemChecked:after {
  content: '';
  cursor: pointer;
  border-color: #fff;
  border-style: solid;
  position: absolute;
  display: block;
  top: 45%;
  width: 0.2em;
  left: 0.35em;
  height: 0.4em;
  transform: translateY(-50%) rotate(45deg);
  border-width: 0 0.1em 0.1em 0;
}
.PlaygroundEditorTheme__nestedListItem {
  list-style-type: none;
}
.PlaygroundEditorTheme__nestedListItem:before,
.PlaygroundEditorTheme__nestedListItem:after {
  display: none;
}
.PlaygroundEditorTheme__tokenComment {
  color: slategray;
}
.PlaygroundEditorTheme__tokenDeleted {
  border-image: linear-gradient(to right, #ffcecb 50%, #ffebe9 50%) fill 0/0/0
    100vw;
}
.PlaygroundEditorTheme__tokenInserted {
  border-image: linear-gradient(to right, #aceebb 50%, #dafbe1 50%) fill 0/0/0
    100vw;
}
.PlaygroundEditorTheme__tokenUnchanged {
  border-image: linear-gradient(to right, #ddd 50%, #f0f2f5 50%) fill 0/0/0
    100vw;
}
.PlaygroundEditorTheme__tokenPunctuation {
  color: #999;
}
.PlaygroundEditorTheme__tokenProperty {
  color: #905;
}
.PlaygroundEditorTheme__tokenSelector {
  color: #690;
}
.PlaygroundEditorTheme__tokenOperator {
  color: #9a6e3a;
}
.PlaygroundEditorTheme__tokenAttr {
  color: #07a;
}
.PlaygroundEditorTheme__tokenVariable {
  color: #e90;
}
.PlaygroundEditorTheme__tokenFunction {
  color: #dd4a68;
}
.PlaygroundEditorTheme__mark {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
  padding-bottom: 2px;
}
.PlaygroundEditorTheme__markOverlap {
  background: rgba(255, 212, 0, 0.3);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.PlaygroundEditorTheme__mark.selected {
  background: rgba(255, 212, 0, 0.5);
  border-bottom: 2px solid rgba(255, 212, 0, 1);
}
.PlaygroundEditorTheme__markOverlap.selected {
  background: rgba(255, 212, 0, 0.7);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.PlaygroundEditorTheme__embedBlock {
  user-select: none;
}
.PlaygroundEditorTheme__embedBlockFocus {
  outline: 2px solid rgb(60, 132, 244);
}
.PlaygroundEditorTheme__layoutContainer {
  display: grid;
  gap: 10px;
  margin: 10px 0;
}
.PlaygroundEditorTheme__layoutItem {
  border: 1px dashed #ddd;
  padding: 8px 16px;
  min-width: 0;
  max-width: 100%;
}
.PlaygroundEditorTheme__autocomplete {
  color: #ccc;
}
.PlaygroundEditorTheme__hr {
  padding: 2px 2px;
  border: none;
  margin: 1em 0;
  cursor: pointer;
}
.PlaygroundEditorTheme__hr:after {
  content: '';
  display: block;
  height: 2px;
  background-color: #ccc;
  line-height: 2px;
}
.PlaygroundEditorTheme__hr.PlaygroundEditorTheme__hrSelected {
  outline: 2px solid rgb(60, 132, 244);
  user-select: none;
}

.PlaygroundEditorTheme__specialText {
  background-color: yellow;
  font-weight: bold;
}

.PlaygroundEditorTheme__contextMenu {
  outline: 0;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}
.PlaygroundEditorTheme__contextMenu button:first-child {
  border-radius: 8px 8px 0px 0px;
}
.PlaygroundEditorTheme__contextMenu button:last-child {
  border-radius: 0px 0px 8px 8px;
}
.PlaygroundEditorTheme__contextMenuItem {
  width: 100%;
  display: block;
}
.PlaygroundEditorTheme__contextMenuItem {
  display: flex;
  justify-content: left;
  width: 100%;
  background-color: #fff;
  color: #050505;
  border: 0;
  border-radius: 0px;
  font-size: 15px;
  text-align: left;
  line-height: 20px;
  padding: 8px;
  padding-right: 14px;
  outline: 0;
  cursor: pointer;
}
.PlaygroundEditorTheme__contextMenuItem.open {
  background: #ddd;
}
.PlaygroundEditorTheme__contextMenuItem:focus,
.PlaygroundEditorTheme__contextMenuItem:not([disabled]):active {
  background: #eee;
}
.PlaygroundEditorTheme__contextMenuItem:disabled {
  background: #fff;
  color: #aaa;
  cursor: not-allowed;
}
.PlaygroundEditorTheme__contextMenuItemIcon {
  width: 20px;
  margin-right: 8px;
  background-repeat: no-repeat;
  background-size: contain;
}

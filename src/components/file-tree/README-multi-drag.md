# 文件树功能说明

## 功能概述

支持多级文件结构、多文件拖拽和智能文件夹创建，提供完整的文件管理体验。

## 使用方法

### 1. 多选文件
- **Ctrl + 点击**：选择多个文件/文件夹
- **Shift + 点击**：选择连续范围的文件/文件夹
- **Ctrl + A**：全选当前层级的所有文件/文件夹

### 2. 拖拽操作
- **拖拽单个文件**：直接拖拽任意文件/文件夹
- **拖拽多个文件**：
  1. 先使用 Ctrl+点击 选择多个文件
  2. 拖拽其中任意一个选中的文件
  3. 所有选中的文件都会被一起拖拽

### 3. 拖拽目标
- 只能拖拽到文件夹中
- 不能拖拽到根节点
- 不能拖拽到自己或自己的子文件夹中

## 新建文件夹功能

### 1. 创建方式
- **Enter 键**：确认创建文件夹
- **失焦操作**：点击其他地方或按 Tab 键时自动创建
- **Escape 键**：取消创建

### 2. 智能创建逻辑
- 如果输入框为空，失焦时取消创建
- 如果输入框有内容，失焦时自动创建文件夹
- 避免与点击事件冲突，使用 100ms 延迟

### 3. 用户体验优化
- 自动聚焦到输入框
- 清晰的视觉反馈
- 支持键盘和鼠标操作

## 多级文件结构

### 1. 结构支持
- **无限层级**：支持任意深度的文件夹嵌套
- **递归操作**：删除文件夹时递归删除所有子项
- **路径构建**：自动构建完整的文件路径
- **面包屑导航**：支持多级路径导航

### 2. 层级限制
- **理论上无限制**：数据库设计支持无限层级
- **实际建议**：建议不超过10层，避免性能问题
- **路径长度**：路径字符串长度有限制

### 3. 性能优化
- **递归算法**：使用高效的递归算法构建树结构
- **缓存机制**：避免重复计算文件夹路径
- **增量更新**：支持增量更新，避免全量重建

## 删除功能

### 1. 删除方式
- **右键菜单**：右键点击文件/文件夹，选择"删除"
- **Delete 键**：选中文件/文件夹后按 Delete 键
- **多选删除**：选中多个文件/文件夹后，右键删除或按 Delete 键

### 2. 智能删除逻辑
- **单选删除**：删除单个文件/文件夹
- **多选删除**：如果当前右键的文件在选中列表中，删除所有选中的项目
- **无确认弹窗**：直接删除，不显示确认对话框
- **批量处理**：使用 Promise.all 并行删除多个项目

### 3. 删除范围
- 不能删除根节点
- 文件夹删除时会递归删除所有子项
- 文件删除时会同时从PDF store和标签页中移除

## 重命名功能

### 1. 重命名方式
- **右键菜单**：右键点击文件/文件夹，选择"重命名"
- **F2 键**：选中单个文件/文件夹后按 F2 键
- **内联编辑**：直接在树中编辑文件名，类似新建文件夹

### 2. 智能重命名逻辑
- **Enter 键**：确认重命名
- **失焦操作**：点击其他地方或按 Tab 键时自动确认
- **Escape 键**：取消重命名
- **自动选中**：进入重命名模式时自动选中所有文本

### 3. 用户体验优化
- **无弹窗确认**：直接内联编辑，操作更流畅
- **自动聚焦**：进入重命名模式时自动聚焦到输入框
- **文本选中**：自动选中所有文本，方便直接输入新名称
- **智能判断**：如果名称没有变化，自动取消重命名

## 技术实现

### 多文件拖拽核心逻辑
```typescript
// 在拖拽开始时检查选中的项目
const handleDragStart = (e, node) => {
  const selectedItems = getSelectedItems();
  
  // 如果当前拖拽的项目在选中列表中，拖拽所有选中的项目
  if (selectedItems.some(item => item.id === node.id)) {
    itemsToDrag = selectedItems;
  } else {
    // 否则只拖拽当前项目
    itemsToDrag = [node];
  }
};
```

### 批量移动
```typescript
// 使用 Promise.all 并行移动所有文件
const movePromises = draggedItems.map(item => 
  folderService.move({
    id: item.id,
    new_parent_id: targetNode.id
  })
);
await Promise.all(movePromises);
```

## 视觉反馈

### 拖拽提示
- 单个文件：显示文件名
- 多个文件：显示"X 个项目"

### 样式变化
- 被拖拽的文件：透明度降低到 0.5
- 多选拖拽：透明度降低到 0.3，添加虚线边框
- 拖拽目标：高亮显示，蓝色边框

## 注意事项

1. **性能优化**：使用 `Promise.all` 并行处理多个移动操作
2. **错误处理**：如果任何一个文件移动失败，会显示错误信息
3. **状态同步**：移动完成后自动刷新文件树
4. **用户体验**：提供清晰的视觉反馈和操作提示

## 兼容性

- 支持所有现代浏览器
- 与现有的单选拖拽功能完全兼容
- 不影响其他文件操作功能

## 智能文件夹创建技术实现

### 核心逻辑
```typescript
// 统一的创建逻辑
const createFolder = () => {
  if (newFolderName.trim()) {
    // 发送创建请求
    document.dispatchEvent(new CustomEvent('ht-folder-create-request', {
      detail: { path: creatingFolderPath, name: newFolderName.trim() }
    }));
    cancelCreateFolder();
  } else {
    cancelCreateFolder();
  }
};

// 失焦时自动创建
const handleNewFolderBlur = () => {
  setTimeout(() => {
    if (creatingFolder) {
      createFolder();
    }
  }, 100); // 避免与点击事件冲突
};
```

### 用户体验优化
- **延迟处理**：使用 100ms 延迟避免与点击事件冲突
- **智能判断**：空内容时取消，有内容时创建
- **多种触发方式**：Enter 键、失焦、Escape 键

### 智能删除技术实现
```typescript
// 处理删除操作（支持单选和多选）
const handleDelete = async (node?: FileTreeNode) => {
  let itemsToDelete: FileTreeNode[] = [];
  
  if (node) {
    // 如果指定了节点，检查是否在选中列表中
    const selectedItems = getSelectedItems();
    if (selectedItems.some(item => item.id === node.id)) {
      // 如果当前节点在选中列表中，删除所有选中的项目
      itemsToDelete = selectedItems;
    } else {
      // 否则只删除当前节点
      itemsToDelete = [node];
    }
  } else {
    // 如果没有指定节点，删除所有选中的项目
    itemsToDelete = getSelectedItems();
  }
  
  // 并行删除所有项目
  const deletePromises = itemsToDelete.map(item => deleteSingleItem(item));
  await Promise.all(deletePromises);
};
```

### 删除功能特性
- **无确认弹窗**：直接删除，提升操作效率
- **多选支持**：智能判断删除范围
- **键盘快捷键**：支持 Delete 键快速删除
- **批量处理**：并行删除多个项目
- **状态同步**：自动更新UI和存储状态

### 重命名功能特性
- **内联编辑**：直接在树中编辑，无需弹窗
- **键盘快捷键**：支持 F2 键快速重命名
- **智能确认**：Enter 键确认，失焦自动确认
- **自动选中**：进入重命名模式时自动选中文本
- **状态同步**：自动更新UI和存储状态

### 多级结构技术实现
```typescript
// 递归构建文件夹路径
const buildFolderPath = (folderId: string): string => {
  const folder = folders.find(f => f.id === folderId);
  if (!folder || folder.parent_id === 'root') {
    return `/${folder?.file_name || ''}`;
  }
  
  const parentPath = buildFolderPath(folder.parent_id);
  return `${parentPath}/${folder.file_name}`;
};

// 递归构建文件夹树
const buildFolderTree = (folderId: string): string[] => {
  const children: string[] = [];
  
  folders.forEach(folder => {
    if (folder.parent_id === folderId) {
      children.push(folder.id);
      const subChildren = buildFolderTree(folder.id);
      const folderNode = folderMap.get(folder.id);
      if (folderNode) {
        folderNode.children = [...(folderNode.children || []), ...subChildren];
      }
    }
  });
  
  return children;
};
```

### 多级结构特性
- **递归算法**：高效的递归构建算法
- **路径计算**：自动计算完整文件路径
- **父子关系**：正确建立多级父子关系
- **性能优化**：避免重复计算和循环引用 
import {getCategoryList} from '@/api/category'
import {useEffect, useRef} from 'react'
import {useHomeStore} from '@/store/home-store'
import {CategoryType} from '../response-type'

export const useCategoryData = () => {
  const { projects, categories, selectCategoryId, setCategories, setCategorySelectList, setCurrentCategory } = useHomeStore();
  const initialFetchDone = useRef(false);
  
  // 初始化获取分类列表
  const onFetchData = async () => {
    try {
      const { code, data } = await getCategoryList({
        page: 1,
        page_size: 100,
      }) as any
      console.log(code, data)
      if (code === 0) {
        const list = data.list ? data.list : []
        setCategories(list)
        initialFetchDone.current = true
      }
    } catch (error) {
      console.log(error)
    }
  }
  
  // 更新分类中的项目数量
  const updateCategoryProjectCount = () => {
    if (categories.length > 0 && projects.length >= 0) {
      const updatedList = categories.map((category: CategoryType) => {
        if (category.cid === "-1") {
          // 全部项目
          return {
            ...category,
            wnum: projects.length
          }
        } else {
          // 计算该分类下的项目数量
          const count = projects.filter(project => project.cid === category.cid).length
          return {
            ...category,
            wnum: count
          }
        }
      })
      
      setCategories(updatedList)
    }
  }
  
  useEffect(() => {
    const currentCategory = categories.find((item) => item.cid === selectCategoryId)
    if (currentCategory) {
      setCurrentCategory(currentCategory)
    }
  }, [categories])
  
  // 只在初始化时获取分类数据
  useEffect(() => {
    if (!initialFetchDone.current) {
      onFetchData()
    }
  }, []);
  
  // 当projects发生变化时，只更新分类中的项目数量，不重新请求API
  useEffect(() => {
    if (initialFetchDone.current) {
      updateCategoryProjectCount()
    }
  }, [projects]);
  
  // 初始化下拉框
  useEffect(() => {
    setCategorySelectList(categories.map((item) => {
      return {
        key: item?.cid,
        label: item?.cname,
        value: item?.cid
      }
    }))
  }, [categories, selectCategoryId, projects])
}
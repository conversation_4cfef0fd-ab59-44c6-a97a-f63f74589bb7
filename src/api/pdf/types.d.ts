// 上传参数类型
export type UploadPdfType = {
  wid: string | number // 工作区id
  attachs: {
    fname: string // 文件名
    content: Blob // 文件内容
  }[]
}

export type DeletePdftType = {
  aid: string | number,
  type: 1 | 2 // 1: 只删除pdf, 2: 删除pdf及高亮和节点
}

export type PdfHighlightType = {
  wid: string,
  mid: string,
  mark: string,
  color: string
}

// PDF文件元数据
export type PdfFileMetadata = {
  aid: string,
  fname: string,
  url: string,
  path: string,
  marks?: Array<{
    mid: string;
    aid: string;
    nid?: string;
    mark: string;
    color: string;
    mark_type?: string;
    content?: string;
  }>
}
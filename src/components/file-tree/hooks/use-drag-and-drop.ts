import { useState, useCallback } from 'react';
import { FileTreeNode } from '../HeadlessFileTreePanel';
import { folderService } from '@/local';

interface UseDragAndDropProps {
  onRefresh?: () => void;
  getSelectedItems?: () => FileTreeNode[]; // 获取选中的项目
  onExpandTarget?: (targetId: string) => void; // 展开目标文件夹的回调
}

export const useDragAndDrop = ({ onRefresh, getSelectedItems, onExpandTarget }: UseDragAndDropProps = {}) => {
  const [draggedItems, setDraggedItems] = useState<FileTreeNode[]>([]);
  const [dropTarget, setDropTarget] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // 处理拖拽开始
  const handleDragStart = useCallback((e: React.DragEvent, node: FileTreeNode) => {
    if (node.id === 'root') return; // 不能拖拽根节点
    
    // 获取选中的项目
    let itemsToDrag: FileTreeNode[] = [];
    
    if (getSelectedItems) {
      const selectedItems = getSelectedItems();
      // 如果当前拖拽的项目在选中列表中，拖拽所有选中的项目
      if (selectedItems.some(item => item.id === node.id)) {
        itemsToDrag = selectedItems;
      } else {
        // 否则只拖拽当前项目
        itemsToDrag = [node];
      }
    } else {
      // 如果没有提供获取选中项目的方法，只拖拽当前项目
      itemsToDrag = [node];
    }
    
    // 过滤掉根节点
    itemsToDrag = itemsToDrag.filter(item => item.id !== 'root');
    
    if (itemsToDrag.length === 0) return;
    
    setIsDragging(true);
    setDraggedItems(itemsToDrag);
    
    e.dataTransfer.effectAllowed = 'move';
    // 设置拖拽数据，包含所有要拖拽的项目ID
    e.dataTransfer.setData('text/plain', JSON.stringify(itemsToDrag.map(item => item.id)));
    
    // 添加拖拽样式
    (e.currentTarget as HTMLElement).style.opacity = '0.5';
  }, [getSelectedItems]);

  // 处理拖拽结束
  const handleDragEnd = useCallback((e: React.DragEvent) => {
    setIsDragging(false);
    setDraggedItems([]);
    setDropTarget(null);
    
    // 移除拖拽样式
    (e.currentTarget as HTMLElement).style.opacity = '1';
  }, []);

  // 处理拖拽悬停
  const handleDragOver = useCallback((e: React.DragEvent, node: FileTreeNode) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    // 只有文件夹可以作为拖拽目标，且不能拖拽到自己
    if (node.isDirectory && !draggedItems.some(item => item.id === node.id)) {
      setDropTarget(node.id);
    }
  }, [draggedItems]);

  // 处理拖拽离开
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    setDropTarget(null);
  }, []);

  // 处理拖拽放置
  const handleDrop = useCallback(async (e: React.DragEvent, targetNode: FileTreeNode) => {
    e.preventDefault();
    
    if (draggedItems.length === 0 || !targetNode.isDirectory || 
        draggedItems.some(item => item.id === targetNode.id)) {
      setDropTarget(null);
      return;
    }

    try {
      // 批量移动所有拖拽的项目
      const movePromises = draggedItems.map(item => 
        folderService.move({
          id: item.id,
          new_parent_id: targetNode.id
        })
      );
      
      await Promise.all(movePromises);

      // 触发刷新
      if (onRefresh) {
        onRefresh();
      }

      // 展开目标文件夹，让用户能看到移动的文件
      if (onExpandTarget) {
        onExpandTarget(targetNode.id);
      }

      const itemNames = draggedItems.map(item => item.title).join(', ');
      console.log(`成功将 ${itemNames} 移动到 ${targetNode.title}`);
    } catch (error) {
      console.error('移动文件失败:', error);
      alert(`移动文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setDropTarget(null);
      setDraggedItems([]);
      setIsDragging(false);
    }
  }, [draggedItems, onRefresh]);

  return {
    // 状态
    draggedItems,
    dropTarget,
    isDragging,
    
    // 事件处理器
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    
    // 工具函数
    isDropTarget: (itemId: string) => dropTarget === itemId,
    isBeingDragged: (itemId: string) => draggedItems.some(item => item.id === itemId),
  };
}; 
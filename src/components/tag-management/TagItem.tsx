import React, { useRef, useEffect } from "react";
import { Edit, Trash2, GripVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  draggable,
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { useDragAndDrop } from "./hooks/useDragAndDrop";

export interface TagItemProps {
  id: string;
  name: string;
  groupId: string; // 添加 groupId 属性
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  draggable?: boolean;
}

export const TagItem: React.FC<TagItemProps> = ({
  id,
  name,
  groupId,
  onEdit,
  onDelete,
  draggable: isDraggable = true,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  
  // 使用拖拽 hook
  const { isBeingDragged, isDropTarget, getDragStyles } = useDragAndDrop();
  
  // 检查当前标签是否正在被拖拽
  const isTagDragging = isBeingDragged(id, 'tag');
  
  // 检查当前标签是否是放置目标
  const isTagDropTarget = isDropTarget(id, 'tag');

  // 设置标签拖拽功能 - 只设置拖拽元素，不处理事件
  useEffect(() => {
    const element = ref.current;
    if (!element || !isDraggable) return;
    
    return combine(
      draggable({
        element,
        getInitialData: () => ({
          id,
          name,
          type: "tag",
          groupId, // 添加 groupId 到拖拽数据中
        }),
      }),
      dropTargetForElements({
        element,
        getData: () => ({
          id,
          type: "tag",
          groupId,
        }),
        canDrop({ source }) {
          // 只允许同分组内的标签重排序
          return source.data.type === "tag" && 
                 source.data.groupId === groupId && 
                 source.data.id !== id;
        },
      })
    );
  }, [id, name, groupId, isDraggable]);

  // 合并拖拽样式
  const dragStyles = getDragStyles(id, 'tag');

  return (
    <div 
      ref={ref} 
      className={dragStyles}
      data-tag-id={id} // 添加用于触发后效果的标识
    >
      <Badge
        variant="secondary"
        className={`
          inline-flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-200
          ${
            isDraggable
              ? "cursor-grab active:cursor-grabbing"
              : "cursor-default"
          }
          ${
            isTagDragging
              ? "shadow-lg transform scale-105"
              : isTagDropTarget
              ? "ring-2 ring-blue-400 bg-blue-50"
              : "hover:bg-gray-100"
          }
        `}
      >
        <span className="text-gray-700">{name}</span>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onEdit?.(id);
            }}
            className="h-6 w-6 p-0 hover:bg-gray-200"
          >
            <Edit className="h-3 w-3" />
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => e.stopPropagation()}
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-100"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认删除标签</AlertDialogTitle>
                <AlertDialogDescription>
                  确定要删除标签 "{name}" 吗？此操作无法撤销。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete?.(id)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  确认删除
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {isDraggable && (
            <div className="flex items-center cursor-grab active:cursor-grabbing p-1">
              <GripVertical className="h-3 w-3 text-gray-400" />
            </div>
          )}
        </div>
      </Badge>
    </div>
  );
};

export default TagItem;

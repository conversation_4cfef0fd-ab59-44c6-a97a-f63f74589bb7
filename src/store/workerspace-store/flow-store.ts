import type {<PERSON>, Edge, <PERSON>Change, Node, NodeChange} from "@xyflow/react";
import {addEdge, applyEdgeChanges, applyNodeChanges} from "@xyflow/react";
import {devtools} from 'zustand/middleware';
import {message} from "antd";
import {shallow} from "zustand/shallow";
import {createWithEqualityFn} from 'zustand/traditional'

export enum NodeType {
  markdownNode = "markdownNode",
}
export enum EdgeType {
  buttonEdge = "buttonEdge"
}

// 自定义节点类型定义
export type CustomNode = Node<{
  title?: string;      // 节点标题
  content?: string;    // 节点内容
  img?: string;        // 节点图片
  color?: string;      // 节点颜色
  tags?: string[];     // 节点关联的标签ID数组
}> & {
  isHover?: boolean;   // 是否悬停
  sourcePosition?: string;  // 源连接点位置
  targetPosition?: string;  // 目标连接点位置
};
// 流程图状态类型定义
export interface FlowState {
  isEditing: boolean;
  canvansRefresh: number | string
  isloaded: boolean;
  nodes: CustomNode[];
  edges: Edge[];
  createNodeId: string | null;
  zoomLevel: number;
  minZoom: number;
  maxZoom: number;
  layoutDirection: 'HORIZONTAL' | 'VERTICAL';
  selectedNodes: CustomNode[];
  potentialTargetNodeId: string | null;
    center: { x: number; y: number; };
  viewport: { x: number; y: number; zoom: number };
  setViewport: (viewport: { x: number; y: number; zoom: number }) => void;
  setZoom: (zoom: number) => void;
  setIsEditing: (isEditing: boolean) => void;
  setIsloaded: (isloading: boolean) => void;
  setCanvansRefresh: (canvansRefresh: number | string) => void;
  setCreateNodeId: (nodeId: string | null) => void;
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect: (connection: Connection) => void;
  deleteNode: (nodeId: string) => void;
  batchDeleteNodes: (nodeIds: string[]) => void;
  addNode: (node: CustomNode) => void;
  addEdge: (edge: Edge) => void;
  updateNodes: (node: CustomNode) => void;
  isValidConnection: (connection: Connection) => boolean;
  setSelectedNodes: (nodes: CustomNode[]) => void;
  setNodes: (nodes: CustomNode[]) => void;
  getNodes: () => CustomNode[];
  setEdges: (edges: Edge[]) => void;
  getEdges: () => Edge[];
  setLayoutDirection: (direction: 'HORIZONTAL' | 'VERTICAL') => void;
  setPotentialTargetNode: (nodeId: string | null) => void;
  updateNodeMeasurements: (id: string, measurements: { width: number; height: number }) => void;
  updateNodeId: (oldId: string, newId: string) => void;
  updateNodeTags: (nodeId: string, tags: string[]) => void;
  clearNodes: () => void;
    setCenter: (center: { x: number; y: number; }) => void;
}

// 修改 store 创建
export const useFlowStore = createWithEqualityFn<FlowState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      canvansRefresh: 0,
      nodes: [],
      edges: [],
      zoomLevel: 0.5,
      minZoom: 0.1,
      maxZoom: 2,
      createNodeId: '',
      layoutDirection: 'HORIZONTAL',
      selectedNodes: [],
      isloaded: false,
      isEditing: false,
      batchDeleteNodes: (nodeIds) => set((state) => ({ nodes: state.nodes.filter(node => !nodeIds.includes(node.id)), edges: state.edges.filter(edge => !nodeIds.includes(edge.source) && !nodeIds.includes(edge.target)) })),
      setIsEditing: (isEditing: boolean) => set({ isEditing }),
      setIsloaded: (isloaded: boolean) => set({ isloaded }),
      setCanvansRefresh: (canvansRefresh: number | string) => set({ canvansRefresh }),
      setCreateNodeId: (nodeId: string | null) => set({ createNodeId: nodeId }),
        setSelectedNodes: (nodes) => {
            const state = get();
            const selectedNodeIds = new Set(nodes.map(node => node.id));

            const updatedNodes = state.nodes.map(node => ({
                ...node,
                selected: selectedNodeIds.has(node.id),
            }));

            console.log("更新状态", updatedNodes)
            set({
                nodes: updatedNodes,
                selectedNodes: nodes,
            });
        },

      setZoom: (zoom) => {
        const { minZoom, maxZoom } = get();
        set({
          zoomLevel: Math.min(Math.max(zoom, minZoom), maxZoom)
        });
      },

      onNodesChange: (changes) => {
        // console.log('$$$')
        set((state) => ({
          nodes: applyNodeChanges(changes, state.nodes) as CustomNode[],
        }))
      }
      ,

      onEdgesChange: (changes) => set((state) => ({
        edges: applyEdgeChanges(changes, state.edges),
      })),

      onConnect: (connection) => {
        const state = get();
        if (!state.isValidConnection(connection)) {
          return;
        }
        set({
          edges: addEdge({
            ...connection,
          }, state.edges),
        });
      },

      isValidConnection: (connection: Connection) => {
        const { edges } = get();
        const { source, target } = connection;

        // 使用一个函数就足够检测环
        const willFormCycle = (start: string, end: string, visited: Set<string> = new Set()): boolean => {
          if (start === end) return true;
          visited.add(start);

          for (const edge of edges) {
            if (edge.source === start && !visited.has(edge.target)) {
              if (willFormCycle(edge.target, end, visited)) {
                return true;
              }
            }
          }

          return false;
        };

        // 检查新连接是否会形成环
        if (willFormCycle(target, source)) {
          message.error('该连接会形成循环依赖，无法建立');
          return false;
        }

        return true;
      },

      deleteNode: (nodeId) => {
        set((state) => ({
          nodes: state.nodes.filter((node) => node.id !== nodeId),
          edges: state.edges.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId
          ),
        }));
      },

      addNode: (node) =>
        set((state) => {
          return {
            nodes: [...state.nodes, node],
          };
        }),

      addEdge: (edge) => set((state) => ({
        edges: [...state.edges, edge],
      })),

      updateNodes: (node) => set((state) => ({
        nodes: state.nodes.map((n) =>
          n.id === node.id ? node : n
        ),
      })),

      setNodes: (nodes: CustomNode[]) => set({ nodes }),
      getNodes: () => get().nodes,
      setEdges: (edges: Edge[]) => set({ edges }),
      getEdges: () => get().edges,

      setLayoutDirection: (direction) => set({ layoutDirection: direction }),

      potentialTargetNodeId: null,
      setPotentialTargetNode: (nodeId: string | null) => set({ potentialTargetNodeId: nodeId }),

      updateNodeMeasurements: (id, measurements) =>
        set((state) => ({
          nodes: state.nodes.map(node =>
            node.id === id
              ? {
                ...node,
                data: {
                  ...node.data,
                  measurements
                }
              }
              : node
          )
        })),

      updateNodeId: (oldId: string, newId: string) => set((state) => ({
        nodes: state.nodes.map((node) =>
          node.id === oldId ? { ...node, id: newId } : node
        ),
      })),

      updateNodeTags: (nodeId: string, tags: string[]) => set((state) => ({
        nodes: state.nodes.map((node) =>
          node.id === nodeId 
            ? { ...node, data: { ...node.data, tags } }
            : node
        ),
      })),

      viewport: (() => {
        try {
          if (typeof window === 'undefined') {
            return { x: 0, y: 0, zoom: 1 };
          }

          const savedViewport = localStorage.getItem('flow-viewport');
          if (savedViewport) {
            const parsed = JSON.parse(savedViewport);
            // 验证解析结果
            if (
              parsed &&
              typeof parsed.x === 'number' &&
              typeof parsed.y === 'number' &&
              typeof parsed.zoom === 'number'
            ) {
              console.log('初始化时加载视口位置:', parsed);
              return parsed;
            }
          }
        } catch (error) {
          console.error('加载视口位置失败:', error);
        }

        // 默认视口位置
        return { x: 0, y: 0, zoom: 1 };
      })(),

      // 设置视口位置并保存到localStorage
      setViewport: (viewport: { x: number; y: number; zoom: number }) => {
        try {
          // 验证参数有效性
          if (
            viewport &&
            typeof viewport.x === 'number' &&
            typeof viewport.y === 'number' &&
            typeof viewport.zoom === 'number'
          ) {
            set({ viewport });

            // 保存到localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('flow-viewport', JSON.stringify(viewport));
              // console.log('保存视口位置:', viewport);
            }
          } else {
            console.warn('无效的视口数据:', viewport);
          }
        } catch (error) {
          console.error('保存视口位置失败:', error);
        }
      },

      clearNodes: () => set({ nodes: [], selectedNodes: [] }),
        setCenter: (center: { x: number, y: number }) => {
            set({center})
        },
    }),
  ),
  shallow
);

// 添加选择器函数
export const useFlowSelector = {
  zoom: () => useFlowStore(
    (state) => ({
      zoomLevel: state.zoomLevel,
      minZoom: state.minZoom,
      maxZoom: state.maxZoom,
    })),
  nodes: () => useFlowStore((state) => state.nodes),
  edges: () => useFlowStore((state) => state.edges),
};

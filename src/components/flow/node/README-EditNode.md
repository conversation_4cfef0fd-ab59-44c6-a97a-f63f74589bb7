# EditNode 编辑节点窗口组件

## 功能概述

`EditNode` 是一个独立的节点编辑窗口组件，使用 React Portal 技术将编辑界面渲染到 `document.body`，确保完全独立于父组件的拖拽事件。

## 主要特性

### 1. 完全独立的窗口
- **Portal 渲染**：使用 `createPortal` 将组件渲染到 `document.body`
- **事件隔离**：完全隔离于父组件的拖拽和其他事件
- **最高层级**：`z-index: 100000` 确保在所有其他元素之上

### 2. 可拖拽和缩放
- **自由拖拽**：通过标题栏拖拽移动窗口位置
- **自由缩放**：支持调整窗口大小
- **边界限制**：限制在浏览器窗口内移动

### 3. 用户交互优化
- **无遮罩设计**：编辑窗口完全独立，背景可以继续操作
- **ESC 键关闭**：支持 ESC 键快速关闭
- **悬浮效果**：鼠标悬停时的阴影和位移效果
- **自动聚焦**：打开时自动聚焦到标题输入框

### 4. 丰富的编辑功能
- **Markdown 编辑器**：集成 Vditor 富文本编辑器
- **所见即所得**：支持实时预览
- **全屏支持**：编辑器支持全屏模式

## 技术实现

### 核心文件
```
src/components/flow/node/
├── EditNode.tsx              # 主组件
├── README-EditNode.md        # 使用文档
└── ../style/index.css        # 样式文件
```

### 关键技术点

#### 1. Portal 渲染
```tsx
import { createPortal } from 'react-dom';

return createPortal(editWindow, document.body);
```

#### 2. 事件隔离
```tsx
// 阻止所有鼠标事件冒泡到遮罩层
onMouseDown={(e) => e.stopPropagation()}
onMouseMove={(e) => e.stopPropagation()}
onMouseUp={(e) => e.stopPropagation()}
onClick={(e) => e.stopPropagation()}
onWheel={(e) => e.stopPropagation()}
```

#### 3. 拖拽配置
```tsx
<Rnd
  bounds="window"
  dragHandleClassName="edit-node-drag-handle"
  style={{ zIndex: 100000, position: 'fixed' }}
>
```

#### 4. 无遮罩设计
- 移除了遮罩层，编辑窗口完全独立
- 背景的流程图可以正常操作（缩放、拖拽节点等）
- 编辑窗口使用高 z-index 确保在最顶层显示
- 使用 `fixed` 定位和 `bounds="window"` 确保窗口在浏览器范围内

## 使用方法

### 基本用法
```tsx
import { EditNode } from '@/components/flow/node/EditNode';

const [editNodeDialogOpen, setEditNodeDialogOpen] = useState(false);

// 打开编辑窗口
<button onClick={() => setEditNodeDialogOpen(true)}>
  编辑节点
</button>

// 渲染编辑窗口
{editNodeDialogOpen && (
  <EditNode 
    setIsModalOpen={setEditNodeDialogOpen} 
    highlight={nodeId} // 可以是字符串或 CustomHighlight 对象
  />
)}
```

### Props 接口
```typescript
interface EditNodeModalProps {
  setIsModalOpen: (isModalOpen: boolean) => void;
  highlight: CustomHighlight | string;
}
```

## 样式定制

### CSS 类名
- `.edit-node-window` - 窗口主体样式
- `.edit-node-drag-handle` - 拖拽手柄样式

### 自定义样式示例
```css
/* 自定义窗口阴影 */
.edit-node-window {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 自定义拖拽手柄样式 */
.edit-node-drag-handle {
  background: linear-gradient(90deg, #f0f0f0, #e0e0e0);
}
```

## 键盘快捷键

- **ESC** - 关闭编辑窗口
- **Ctrl/Cmd + S** - 保存（如果在 Vditor 中配置）
- **Ctrl/Cmd + Enter** - 快速保存并关闭

## 注意事项

### 1. 性能考虑
- 使用 Portal 会在 DOM 树外创建元素，注意内存管理
- 组件卸载时会自动清理 Vditor 实例

### 2. 兼容性
- 需要支持 `createPortal` 的 React 版本 (16.0+)
- Vditor 编辑器需要现代浏览器支持

### 3. 最佳实践
- 确保在适当的时机调用 `setIsModalOpen(false)`
- 避免同时打开多个编辑窗口
- 在移动设备上可能需要调整窗口大小

## 故障排除

### 1. 拖拽时带动父节点移动
**解决方案**：确保使用了 Portal 渲染，并且正确设置了事件阻止冒泡。

### 2. 窗口被其他元素遮挡
**解决方案**：检查 CSS 中的 `z-index` 设置，确保编辑窗口的层级足够高。

### 3. Vditor 编辑器显示异常
**解决方案**：确保 Vditor 的 CSS 样式正确加载，并且容器高度设置正确。 
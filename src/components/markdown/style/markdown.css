/* Markdown 样式优化 */
:root {
  --md-text-color: #333;
  --md-code-bg: rgba(27, 31, 35, 0.05);
  --md-code-block-bg: #282c34;
  --md-code-text-color: #d9d9d9;
  --md-border-color: #dfe2e5;
  --md-blockquote-color: #6a737d;
  --md-table-even-bg: #f6f8fa;
  --md-table-border: #dfe2e5;
  --md-copy-btn-color: #1890ff;
  --md-copy-success-color: #52c41a;
  --md-ellipsis-color: #999;
  --md-toggle-hover-color: #40a9ff;
  --md-code-header-color: #9da5b4;
}

/* 基础样式 */
.markdown-container .markdown-body {
  font-size: 14px;
  line-height: 1.6;
}

/* 标题样式 */
.markdown-container .markdown-body h1,
.markdown-container .markdown-body h2,
.markdown-container .markdown-body h3,
.markdown-container .markdown-body h4,
.markdown-container .markdown-body h5,
.markdown-container .markdown-body h6 {
  margin: 24px 0 16px;
  font-weight: 600;
  line-height: 1.25;
}

/* 表格样式 */
.markdown-container .markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
  margin: 0 0 16px;
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-container .markdown-body table th,
.markdown-container .markdown-body table td {
  padding: 6px 13px;
  border: 1px solid var(--md-table-border);
}

.markdown-container .markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-container .markdown-body table tr:nth-child(even) {
  background-color: var(--md-table-even-bg);
}

/* 代码块和代码样式 */
.markdown-container .markdown-body .code-block-wrapper {
  border-radius: 6px;
  overflow: hidden;
  margin: 16px 0;
  display: block !important;
  width: 100%;
}

.markdown-container .markdown-body .code-block-header {
  height: 30px;
  background-color: var(--md-code-block-bg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 12px;
  color: var(--md-code-header-color);
}

.markdown-container .markdown-body pre {
  margin: 0;
  font-size: 90%;
  line-height: 1.45;
  background-color: var(--md-code-block-bg);
  border-radius: 0 0 6px 6px;
  display: block;
}

.markdown-container .markdown-body pre code {
  color: var(--md-code-text-color);
}

.markdown-container .markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: var(--md-code-bg);
  border-radius: 6px;
}

/* 引用样式 */
.markdown-container .markdown-body blockquote {
  margin: 0;
  padding: 0 1em;
  color: var(--md-blockquote-color);
  border-left: 0.25em solid var(--md-border-color);
}

/* 列表样式 */
.markdown-container .markdown-body ul,
.markdown-container .markdown-body ol {
  padding-left: 2em;
  margin: 0 0 16px;
}

/* 图片样式 */
.markdown-container .markdown-body img {
  max-width: 100%;
  box-sizing: content-box;
}

/* 复制按钮样式 */
.markdown-container .ant-typography-copy {
  color: var(--md-copy-btn-color) !important;
  transition: color 0.2s ease, opacity 0.2s ease;
}

.markdown-container .ant-typography-copy:hover,
.markdown-container .ant-typography-copy-success:hover {
  opacity: 0.8;
}

.markdown-container .ant-typography-copy-success {
  color: var(--md-copy-success-color) !important;
}

/* 内容包装器样式 */
.markdown-container .markdown-content-wrapper {
  display: inline;
  line-height: 1.6;
}

.markdown-container .markdown-body {
  display: inline;
}

.markdown-container .markdown-body > *:last-child {
  margin-bottom: 0;
}

.markdown-container .markdown-body p {
  display: inline;
  margin: 0;
}

/* 省略号和展开/收起按钮样式 */
.markdown-container .markdown-ellipsis {
  color: var(--md-ellipsis-color);
  font-style: italic;
  margin-left: 0;
}

.markdown-container .markdown-inline-toggle {
  padding: 0 !important;
  height: auto !important;
  color: var(--md-copy-btn-color) !important;
  font-size: 14px;
  margin-left: 4px;
  display: inline !important;
  vertical-align: baseline;
  line-height: 1.6;
}

.markdown-container .markdown-inline-toggle:hover {
  color: var(--md-toggle-hover-color) !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .markdown-container .markdown-body {
    font-size: 13px;
  }
  
  .markdown-container .markdown-body .code-block-header {
    padding: 0 8px;
  }
} 
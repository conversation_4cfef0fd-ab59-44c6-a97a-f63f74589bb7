import { useState, useCallback, useEffect, useRef } from 'react';
import { FileTreeNode } from '../HeadlessFileTreePanel';
import { folderService } from '@/local';

interface UseRenameOperationsProps {
  onRefresh?: () => void;
}

export const useRenameOperations = ({ onRefresh }: UseRenameOperationsProps = {}) => {
  const [renamingItem, setRenamingItem] = useState<FileTreeNode | null>(null);
  const [newName, setNewName] = useState('');
  const renameInputRef = useRef<HTMLInputElement>(null);

  // 开始重命名
  const startRename = useCallback((node: FileTreeNode) => {
    setRenamingItem(node);
    setNewName(node.title);
  }, []);

  // 取消重命名
  const cancelRename = useCallback(() => {
    setRenamingItem(null);
    setNewName('');
  }, []);

  // 执行重命名
  const executeRename = useCallback(async () => {
    if (!renamingItem || !newName.trim() || newName.trim() === renamingItem.title) {
      cancelRename();
      return;
    }

    try {
      // 调用API重命名
      await folderService.rename({
        id: renamingItem.id,
        new_name: newName.trim()
      });

      console.log(`✅ 重命名成功: "${renamingItem.title}" → "${newName.trim()}"`);

      // 触发刷新
      if (onRefresh) {
        onRefresh();
      }

      cancelRename();
    } catch (error) {
      console.error('重命名失败:', error);
      
      // 如果是重名错误，恢复原名称
      if (error instanceof Error && error.message.includes('已存在')) {
        setNewName(renamingItem.title);
        // 可以在这里添加错误提示，但不使用弹窗
        // 比如通过 toast 通知或其他非阻塞的方式
        console.warn('重命名失败: 名称已存在');
      } else {
        // 其他错误，取消重命名
        cancelRename();
      }
    }
  }, [renamingItem, newName, onRefresh, cancelRename]);

  // 处理重命名输入框的键盘事件
  const handleRenameKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeRename();
    } else if (e.key === 'Escape') {
      cancelRename();
    }
  }, [executeRename, cancelRename]);

  // 处理重命名输入框的失焦事件
  const handleRenameBlur = useCallback(() => {
    // 延迟执行，避免与点击事件冲突
    setTimeout(() => {
      if (renamingItem) {
        executeRename();
      }
    }, 100);
  }, [renamingItem, executeRename]);

  // 当进入重命名模式时，自动聚焦输入框并选中文本
  useEffect(() => {
    if (renamingItem && renameInputRef.current) {
      renameInputRef.current.focus();
      renameInputRef.current.select(); // 选中所有文本
    }
  }, [renamingItem]);

  return {
    // 状态
    renamingItem,
    newName,
    renameInputRef,
    
    // 事件处理器
    startRename,
    cancelRename,
    handleRenameKeyDown,
    handleRenameBlur,
    
    // 工具函数
    setNewName,
  };
}; 
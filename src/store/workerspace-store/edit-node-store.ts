import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CustomHighlight } from '@/store/pdf-store';

interface EditNodeState {
    isOpen: boolean;
    nodeId: string | null;
    highlight: CustomHighlight | string | null;
}

interface EditNodeActions {
    openEditNode: (highlight: CustomHighlight | string) => void;
    closeEditNode: () => void;
}

type EditNodeStore = EditNodeState & EditNodeActions;

export const useEditNodeStore = create<EditNodeStore>()(
    subscribeWithSelector((set, get) => ({
        // 状态
        isOpen: false,
        nodeId: null,
        highlight: null,

        // 操作
        openEditNode: (highlight: CustomHighlight | string) => {
            const nodeId = typeof highlight === "string" ? highlight : highlight.nid;
            set({
                isOpen: true,
                nodeId,
                highlight,
            });
        },

        closeEditNode: () => {
            set(() => ({
                isOpen: false,
                nodeId: null,
                highlight: null,
            }));
        },
    }))
); 
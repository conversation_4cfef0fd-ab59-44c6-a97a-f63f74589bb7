import React from 'react';
import {useLexicalComposerContext} from '@lexical/react/LexicalComposerContext';
import {
  $getSelection,
  $isRangeSelection,
  $createParagraphNode,
  $createTextNode,
  $getRoot
} from 'lexical';
import {$createDiffNode} from './DiffNode.js';
import {
  computeChineseDiff,
  getDiffStats,
  makePatches,
  applyPatches
} from '../../utils/diffUtils.js';

// Hook 用于使用 Diff 功能
export function useDiff() {
  const [editor] = useLexicalComposerContext();

  // 接受 diff 修改（使用 DMP 补丁）
  const acceptDiff = (diffNode) => {
    editor.update(() => {
      const diffData = diffNode.getDiffData();
      const paragraph = $createParagraphNode();
      
      if (diffData.patches && diffData.originalText) {
        // 使用 DMP 应用补丁
        const resultText = applyPatches(diffData.originalText, diffData.patches);
        const textNode = $createTextNode(resultText);
        paragraph.append(textNode);
      } else {
        // 回退到原来的方法
        diffData.changes.forEach(change => {
          if (change.type === 'added' || change.type === 'unchanged') {
            const textNode = $createTextNode(change.text);
            paragraph.append(textNode);
          }
        });
      }
      
      // 替换 diff 节点
      diffNode.replace(paragraph);
    });
  };

  // 拒绝 diff 修改
  const rejectDiff = (diffNode) => {
    editor.update(() => {
      const diffData = diffNode.getDiffData();
      const paragraph = $createParagraphNode();
      
      if (diffData.originalText) {
        // 直接使用原始文本
        const textNode = $createTextNode(diffData.originalText);
        paragraph.append(textNode);
      } else {
        // 回退到原来的方法
        diffData.changes.forEach(change => {
          if (change.type === 'removed' || change.type === 'unchanged') {
            const textNode = $createTextNode(change.text);
            paragraph.append(textNode);
          }
        });
      }
      
      // 替换 diff 节点
      diffNode.replace(paragraph);
    });
  };

  // 插入中文字符级 diff
  const insertChineseDiff = (originalText, newText) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const changes = computeChineseDiff(originalText, newText);
        const stats = getDiffStats(changes);
        
        const diffData = {
          changes,
          stats,
          originalText,
          newText,
          type: 'chinese',
          patches: makePatches(originalText, newText)
        };

        const diffId = `chinese_diff_${Date.now()}`;
        const diffNode = $createDiffNode(diffData, diffId);
        
        selection.insertNodes([diffNode]);
      }
    });
  };

  return {
    insertChineseDiff,
    acceptDiff,
    rejectDiff,
  };
} 
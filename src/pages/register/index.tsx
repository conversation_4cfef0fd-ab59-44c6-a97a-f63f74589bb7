import { Form, Input, Button } from "antd";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { message } from "antd";
import { register, type RegisterParams } from "@/api/user";
const RegisterContainer = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #2e4b75;
  color: #fff;
`;
export const Register = () => {
  const navigate = useNavigate();
  const onRegister = async (formdata: RegisterParams) => {
    try {
      const data = (await register(formdata)) as any;
      console.log(data);
      if (data.code === 3) {
        message.warning(data.msg);
      }
      if (data.code === 0) {
        message.success("注册成功");
        navigate("/login");
      }
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <RegisterContainer>
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-3/4">
        <div className="text-5xl font-bold mb-5 text-center ">注册用户</div>
        <div className="border p-5 rounded-md shadow-md bg-gray-500 border-gray-500 text-white  ">
          <Form labelCol={{ span: 4 }} onFinish={onRegister}>
            <Form.Item
              label={<div className="text-white">邮箱</div>}
              labelCol={{ span: 4 }}
              className="h-10"
              name="name"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value) {
                      return Promise.reject(new Error("请输入邮箱"));
                    }
                    const emailPattern =
                      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (emailPattern.test(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("请输入有效的邮箱"));
                  },
                },
              ]}
            >
              <Input className="ml-2 w-[300px] h-10" />
            </Form.Item>
            <Form.Item
              label={<div className="text-white">密码</div>}
              className="h-10"
              name="pwd"
              rules={[
                {
                  validator(rule, value, callback) {
                    if (!value) {
                      callback("请输入密码");
                    } else if (value.length < 8) {
                      callback("密码长度不能小于8位");
                    } else if (value.length > 20) {
                      callback("密码长度不能超过20位");
                    } else if (!/^[a-zA-Z]/.test(value)) {
                      callback("密码必须以字母开头");
                    } else if (!/^[a-zA-Z0-9]+$/.test(value)) {
                      callback("密码只能包含字母和数字");
                    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                      callback("密码需包含大小写字母和数字");
                    } else {
                      callback();
                    }
                  },
                },
              ]}
            >
              <Input.Password className="ml-2 w-[300px] h-10" />
            </Form.Item>
            <Form.Item
              label={<div className="text-white">验证码</div>}
              className="h-10"
              name="code"
            >
              <Input className="ml-2 w-[300px] h-10" />
            </Form.Item>
            <Form.Item className="flex justify-center">
              <Button
                type="primary"
                htmlType="submit"
                className="w-96 h-10 text-xl"
              >
                注册
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </RegisterContainer>
  );
};

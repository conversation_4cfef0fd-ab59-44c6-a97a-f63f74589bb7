.draggable-block-menu {
  border-radius: 4px;
  padding: 2px 1px;
  cursor: grab;
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  will-change: transform;
  display: flex;
  gap: 2px;
}

.draggable-block-menu .icon {
  width: 16px;
  height: 16px;
  opacity: 0.3;
  background-image: url(../../images/icons/draggable-block-menu.svg);
}

.draggable-block-menu .icon-plus {
  display: inline-block;
  border: none;
  cursor: pointer;
  background-color: transparent;
  background-image: url(../../images/icons/plus.svg);
}

.draggable-block-menu:active {
  cursor: grabbing;
}

.draggable-block-menu .icon:hover {
  background-color: #efefef;
}

.draggable-block-target-line {
  pointer-events: none;
  background: deepskyblue;
  height: 4px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  will-change: transform;
}

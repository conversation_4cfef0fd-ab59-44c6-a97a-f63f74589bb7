import {DragAndResize} from "@/components/custom-drag-and-resize";
import {PanelPosition, ResizableContainer,} from '@/pages/workspace/Panel';
import {useChatStore} from "@/store/workerspace-store/chat-store";
import React, {FC, useCallback, useRef, useState} from "react";
import styled from "styled-components";
import {Chat} from "./components/chat";
import {Toolbar} from "./components/chat-toolbar";
import {HistorySession} from "./components/history-session";
import {useChatData} from "./hooks/use-chat-data";

// 添加样式，使工具栏可拖拽
const DraggableToolbar = styled.div`
    cursor: move;
`;

// 可拖拽分割线样式
const ResizableDivider = styled.div`
    width: 4px;
    background-color: #d9d9d9;
    cursor: col-resize;
    position: relative;
    user-select: none;

    &:hover {
        background-color: #1890ff;
    }

    &:active {
        background-color: #1890ff;
    }
`;

const MAX_Width = 450
const MIN_WIDTH = 150
export const CHAT_INPUT_DIV__TAG = 'CHAT_INPUT_DIV__TAG'
export const ChatPanel_: FC = () => {
    const {openHistory} = useChatStore();
    const [rightPanelWidth, setRightPanelWidth] = useState(220); // 固定右侧面板宽度
    const [isDragging, setIsDragging] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    // 处理分割线拖拽
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        setIsDragging(true);

        const handleMouseMove = (e: MouseEvent) => {
            if (!containerRef.current) return;

            const containerRect = containerRef.current.getBoundingClientRect();
            const containerWidth = containerRect.width;
            const mouseX = e.clientX - containerRect.left;

            // 计算新的右侧面板宽度（从右边缘计算）
            const newRightWidth = containerWidth - mouseX;

            if (newRightWidth < MAX_Width && newRightWidth > MIN_WIDTH) {
                setRightPanelWidth(newRightWidth);
            }
        };

        const handleMouseUp = () => {
            setIsDragging(false);
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }, []);

    return (
        <div
            className="h-full bg-[#E0E2F3] scrollbar-custom"
            id={CHAT_INPUT_DIV__TAG}
        >
            {/* 顶部工具栏 - 添加drag-handle类使其可拖拽 */}
            <DraggableToolbar className="drag-handle">
                <Toolbar/>
            </DraggableToolbar>

            {/* 主要内容区域 */}
            <div
                ref={containerRef}
                className="flex-1 flex h-[calc(100%-60px)] overflow-hidden"
                style={{userSelect: isDragging ? 'none' : 'auto'}}
            >
                {/* 聊天主区域 - 左侧自适应宽度 */}
                <div className="flex-1 overflow-hidden">
                    <Chat/>
                </div>

                {/* 历史会话区域 - 只在openHistory为true时显示 */}
                {openHistory && (
                    <>
                        {/* 可拖拽的分割线 */}
                        <ResizableDivider
                            onMouseDown={handleMouseDown}
                            style={{
                                backgroundColor: isDragging ? '#1890ff' : '#d9d9d9'
                            }}
                        />

                        {/* 右侧历史记录面板 - 固定宽度 */}
                        <div
                            className="overflow-hidden"
                            style={{
                                width: rightPanelWidth,
                                minWidth: rightPanelWidth,
                                maxWidth: rightPanelWidth
                            }}
                        >
                            <HistorySession/>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

const ChatPanelWrapper = styled(ResizableContainer)`
    top: calc(100% - 520px); /* 使用top而不是bottom，预留120px给控制面板 */
    left: calc(100% - 420px); /* 使用left而不是right，预留20px边距 */
    border-radius: 30px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;
`;

export const ChatPanel: FC<{
    zIndex: number,
    panelPosition: PanelPosition,
    setPanelPosition: (position: PanelPosition) => void,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    handlePanelClick?: () => void,
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = (props) => {

    const {
        zIndex,
        panelPosition,
        otherPanels = [],
        ...rest
    } = props

    useChatData();

    return <DragAndResize
        panelPosition={panelPosition}
        className="chat-panel-resizable"
        panelClassName="chat-panel-resizable"
        zIndex={zIndex}
        innerElement={<ChatPanel_/>}
        PanelWrapper={ChatPanelWrapper}
        otherPanels={otherPanels}
        {...rest}
    />
}

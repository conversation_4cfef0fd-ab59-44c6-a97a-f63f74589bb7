import { useState, useCallback, useEffect } from 'react';
import { Edge, useReactFlow } from '@xyflow/react';
import { useFlowStore } from '@/store/flow-store';

interface UseEdgePopupReturn {
  selectedEdge: Edge | null;
  popupPosition: { left: number; top: number };
  onEdgeClick: (event: React.MouseEvent, edge: Edge) => void;
  closePopup: () => void;
  onPaneClick: () => void;
}

export const useEdgePopup = (): UseEdgePopupReturn => {
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [popupPosition, setPopupPosition] = useState({ left: 0, top: 0 });
  
  const { nodes , edges} = useFlowStore();
  const { getViewport } = useReactFlow();

  // 计算边在屏幕视口的位置
  const calculateEdgeViewportPosition = useCallback((edge: Edge, clickEvent: React.MouseEvent) => {
    // 获取源节点和目标节点
    const sourceNode = nodes.find(node => node.id === edge.source);
    const targetNode = nodes.find(node => node.id === edge.target);
    if (!sourceNode || !targetNode) return null;

    const viewport = getViewport();

    // 计算节点中心点（画布坐标）
    const sourceX = sourceNode.position.x + (sourceNode.width || 100) / 2;
    const sourceY = sourceNode.position.y + (sourceNode.height || 40) / 2;
    const targetX = targetNode.position.x + (targetNode.width || 100) / 2;
    const targetY = targetNode.position.y + (targetNode.height || 40) / 2;

    // 计算边的边界框（画布坐标）
    const edgeBounds = {
      left: Math.min(sourceX, targetX),
      top: Math.min(sourceY, targetY),
      right: Math.max(sourceX, targetX),
      bottom: Math.max(sourceY, targetY),
      centerX: (sourceX + targetX) / 2,
      centerY: (sourceY + targetY) / 2,
    };

    // 获取Flow容器的屏幕位置
    const flowContainer = clickEvent.currentTarget.closest('.react-flow__renderer')?.parentElement;
    if (!flowContainer) return null;
    const flowRect = flowContainer.getBoundingClientRect();

    // 将边界框从画布坐标转换为视口坐标
    const edgeBoundsViewport = {
      left: (edgeBounds.left * viewport.zoom) + viewport.x,
      top: (edgeBounds.top * viewport.zoom) + viewport.y,
      right: (edgeBounds.right * viewport.zoom) + viewport.x,
      bottom: (edgeBounds.bottom * viewport.zoom) + viewport.y,
      centerX: (edgeBounds.centerX * viewport.zoom) + viewport.x,
      centerY: (edgeBounds.centerY * viewport.zoom) + viewport.y,
    };

    // 将边界框从视口坐标转换为屏幕坐标
    const edgeBoundsScreen = {
      left: flowRect.left + edgeBoundsViewport.left,
      top: flowRect.top + edgeBoundsViewport.top,
      right: flowRect.left + edgeBoundsViewport.right,
      bottom: flowRect.top + edgeBoundsViewport.bottom,
      centerX: flowRect.left + edgeBoundsViewport.centerX,
      centerY: flowRect.top + edgeBoundsViewport.centerY,
    };

    return {
      boundsCanvas: edgeBounds,
      boundsViewport: edgeBoundsViewport,
      boundsScreen: edgeBoundsScreen,
      flowRect,
      viewport
    };
  }, [nodes, getViewport]);

  // 使用boundsScreen（屏幕坐标）来计算弹框位置
  const calculatePopupPositionByBounds = useCallback((edgeInfo: any) => {
    const { boundsScreen } = edgeInfo;
    const popupOffset = 20; // 弹框与边的偏移距离
    const popupWidth = 200; // 估算弹框宽度
    const popupHeight = 120; // 估算弹框高度
    const screenMargin = 10;

    // 默认：centerX, top 上方
    let finalX = boundsScreen.centerX;
    let finalY = boundsScreen.top - popupOffset;

    // 如果上方超出屏幕，则放在bottom下方
    if (finalY - popupHeight < screenMargin) {
      finalY = boundsScreen.bottom + popupOffset;
    }

    // 如果下方超出屏幕，则往上顶
    if (finalY + popupHeight > window.innerHeight - screenMargin) {
      finalY = window.innerHeight - screenMargin - popupHeight;
    }

    // 如果左侧超出屏幕，则横向跟随right
    if (finalX - popupWidth / 2 < screenMargin) {
      finalX = boundsScreen.right + popupOffset + popupWidth / 2;
      finalY = boundsScreen.centerY - popupHeight / 2;
    }

    // 如果右侧超出屏幕，则横向跟随left
    if (finalX + popupWidth / 2 > window.innerWidth - screenMargin) {
      finalX = boundsScreen.left - popupOffset - popupWidth / 2;
      finalY = boundsScreen.centerY - popupHeight / 2;
    }

    // 最终防止超出屏幕
    if (finalX - popupWidth / 2 < screenMargin) {
      finalX = screenMargin + popupWidth / 2;
    }
    if (finalX + popupWidth / 2 > window.innerWidth - screenMargin) {
      finalX = window.innerWidth - screenMargin - popupWidth / 2;
    }
    if (finalY < screenMargin) {
      finalY = screenMargin;
    }
    if (finalY + popupHeight > window.innerHeight - screenMargin) {
      finalY = window.innerHeight - screenMargin - popupHeight;
    }

    return {
      left: finalX,
      top: finalY,
    };
  }, []);

  // 边点击处理
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation();
    const edgeInfo = calculateEdgeViewportPosition(edge, event);
    if (edgeInfo) {
      const popupPosition = calculatePopupPositionByBounds(edgeInfo);
      setPopupPosition({
        left: popupPosition.left,
        top: popupPosition.top
      });
      setSelectedEdge(edge);
    } else {
      setPopupPosition({ left: event.clientX, top: event.clientY - 30 });
      setSelectedEdge(edge);
    }
  }, [calculateEdgeViewportPosition, calculatePopupPositionByBounds]);

  // 关闭弹框
  const closePopup = useCallback(() => {
    setSelectedEdge(null);
  }, []);

  // 点击面板关闭弹框
  const onPaneClick = useCallback(() => {
    setSelectedEdge(null);
  }, []);

  useEffect(() => {
    console.log('edges', edges)
  }, [edges])

  return {
    selectedEdge,
    popupPosition,
    onEdgeClick,
    closePopup,
    onPaneClick,
  };
}; 
import {useCallback} from "react";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {message} from "antd";
import {useNodeAdd} from "@/components/flow/hooks/node-add.ts";
import {scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";

export const useHighlightAdd = () => {
    const {handleAddNode} = useNodeAdd()
    const {addHighlights} = usePdfStore((state) => ({
        addHighlights: state.addHighlights
    }));
    const addHighlight = useCallback(async (highlight: CustomHighlight) => {
        let content = highlight.content?.text || "";
        // "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHc...";
        if (highlight.type == "area") {
            content = `![pdf截图-${new Date().toLocaleString()}](${highlight.content?.image})`
        }

        // 创建包含完整信息的mark对象
        const completeMarkInfo = {
            ...highlight.position,
            type: highlight.type,
            content: highlight.content
        }
        const state = usePdfStore.getState()
        const highlighterUtils = state.pdfs.get(highlight.aid)?.pdfHighlighterUtils
        try {
            // 新建节点 - 启用自动高度计算
            const res = await handleAddNode({
                content: content,
                aid: highlight.aid,
                color: (highlight.color ?? state.defaultColor).slice(1),
                mark: JSON.stringify(completeMarkInfo),
                mid: highlight.id,
                setCenter: true,
                autoHeight: true, // 启用自动高度计算
            })
            if ((res as any).code != 0) {
                message.error("创建高亮节点失败");
                highlighterUtils?.removeGhostHighlight()
                return
            }
            // 自定义高亮
            const customHighlight = {
                ...highlight,
                color: highlight.color ?? state.defaultColor,
                id: (res.data as any).mid,
                aid: highlight.aid,
                nid: (res.data as any).nid,
            }
            // 添加高亮
            addHighlights(highlight.aid, [customHighlight])
            highlighterUtils?.removeGhostHighlight()
            // 工具栏
            const highlightTip: Tip = {
                position: scaledPositionToViewport(highlight.position, highlighterUtils?.getViewer()!),
                content: <HighlightToolbar customHighlight={customHighlight}/>,
            }
            highlighterUtils?.setTip(highlightTip)
        } catch (e) {
            console.log(e)
        }
    }, [handleAddNode, addHighlights])
    return {
        addHighlight
    }
}
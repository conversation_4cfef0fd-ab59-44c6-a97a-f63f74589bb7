import {CSSProperties, useCallback, useMemo, useState} from "react";
import {Connection, Edge, getOutgoers, OnConnectStartParams} from "@xyflow/react";
import {CustomNode, useFlowStore} from "@/store/flow-store.js";
import {createLink} from "@/api/canvas";
import { message } from "antd";

const useEdgeConnect = () => {
    const {addEdges, updatedEdges} = useFlowStore((state) => ({
        addEdges: state.addEdges,
        updatedEdges: state.updateEdges,
    }));
    const [isReverse, setIsReverse] = useState(false);

    const isValidConnection = useCallback(
        (connection: Edge | Connection) => {
            const nodes = useFlowStore.getState().nodes;
            const edges = useFlowStore.getState().edges;

            const isExist = edges.some((edge) => {
                return edge.source === connection.source && edge.target === connection.target
            })
            // 如果连线已存在，则不进行连线

            if (isExist) {
                return false
            };

            const target = nodes.find((node) => node.id === connection.target);
            const hasCycle = (node: CustomNode, visited = new Set()) => {
                if (visited.has(node.id)) return false;

                visited.add(node.id);

                for (const outgoer of getOutgoers(node, nodes, edges)) {
                    if (outgoer.id === connection.source) return true;
                    if (hasCycle(outgoer, visited)) return true;
                }
            };

            if (target!.id === connection.source) return false;
            return !hasCycle(target!);
        },
        [],
    );

    const onConnectStart = useCallback((_: MouseEvent | TouchEvent, params: OnConnectStartParams) => {
        // 根据handle类型设置方向状态
        setIsReverse(params.handleType === 'target');
    }, []);

    const onConnect = useCallback(
        (connection: Connection) => {
            // 接口
            try {
                const res = createLink({nid: connection.target, pids: [connection.source], type: "default", label: ''})

                const tmpId = `tmp-${connection.target}-${connection.source}` 
                const updateEdgesData = {
                    id: tmpId,
                    source: connection.source,
                    target: connection.target,
                    type: 'editableEdge',
                    data: { label: '' },
                }
                addEdges([updateEdgesData])

                // 临时边创建完成后，需要从数据库把新增的连线id 更新到当前的数据中，目的: 修改连线标签需要用到id 
                res.then((res) => {
                    updatedEdges(updateEdgesData?.id, {
                        id: res?.data?.[0]
                    })   
                })

            } catch (e) {
                console.log(e)
            }
        },
        [],
    );

    const connectionLineStyle = useMemo<CSSProperties>(() => {
        return {
            animation: isReverse
                ? 'dashdraw 0.5s linear infinite reverse'
                : 'dashdraw 0.5s linear infinite',
            strokeDasharray: 'none',
        };
    }, [isReverse]);

    return {
        isValidConnection,
        onConnect,
        onConnectStart,
        connectionLineStyle
    }
}

export {useEdgeConnect}
import React from 'react';
import { useTagGroups, useTagActions } from './stores/useTagStore';

/**
 * 简单的测试页面来验证重构后的功能
 */
export const TestPage: React.FC = () => {
  const tagGroups = useTagGroups();
  const actions = useTagActions();
  
  const handleAddTestGroup = () => {
    actions.addGroup({ title: `测试分组 ${Date.now()}`, tags: [] });
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">标签管理测试页面</h1>
      
      <button 
        onClick={handleAddTestGroup}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        添加测试分组
      </button>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">当前分组 ({tagGroups.length}):</h2>
        {tagGroups.map((group) => (
          <div key={group.id} className="p-3 border rounded">
            <span className="font-medium">{group.title}</span>
            <span className="ml-2 text-gray-500">({group.tags.length} 个标签)</span>
          </div>
        ))}
        
        {tagGroups.length === 0 && (
          <div className="text-gray-500 italic">暂无分组</div>
        )}
      </div>
    </div>
  );
};
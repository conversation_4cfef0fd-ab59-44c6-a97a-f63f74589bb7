import axios, {AxiosResponse, InternalAxiosRequestConfig} from 'axios'
// 导入所有本地服务
import {categoryService, nodeService, sessionService, workspaceService, tagService} from "@/local";
import {RxError} from "rxdb";

const baseUrl = 'http://10.79.149.39:8080'
// const baseUrl2 = 'https://3d51-114-251-196-92.ngrok-free.app/'

enum CodeEType {
  "SUCCESS" = 0,
  "PARAMS_ERROR" = 1,
  "PARAMS_TYPE" = 2,
  "CUSTOME_ERROR_OR_NETWORK_ERROR" = 3,
  "UNLOGIN" = 4,
  "AUTH" = 5
}
type ResponseType = {
  code: number,
  data: Record<string, unknown>,
  msg: string
}
// 1. 首先定义类型
type tokenInfoType = {
  token: string;
  expire: number;
};

export const httpRequest = axios.create({
  baseURL: baseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
  // 允许跨域请求携带凭证
  // withCredentials: true
})

// API请求白名单
const apiWhiteList = ['/user/login', '/user/reg']
// 前端路由白名单
const routeWhiteList = ['/login', '/register']

// 本地服务实例
type ServiceMap = Record<string, any>;

const localServices: ServiceMap = {
  cate: categoryService,
  ws: workspaceService,
  node: nodeService,
  session: sessionService,
  tag: tagService
};

// 本地API映射表
type ApiMethodHandler = { service: string; method: string };
type ApiMethodMap = Record<string, ApiMethodHandler>;
type ApiMap = Record<string, ApiMethodMap>;

const localApiMap: ApiMap = {
  'cate': {
    'post': { service: 'cate', method: 'create' },
    'get': { service: 'cate', method: 'list' },
    'patch': { service: 'cate', method: 'update' },
    'delete': { service: 'cate', method: 'delete' }
  },
  'cate/list': {
    'get': { service: 'cate', method: 'list' }
  },
  // 工作区相关API
  'ws': {
    'post': { service: 'ws', method: 'create' },
    'patch': { service: 'ws', method: 'update' },
    'delete': { service: 'ws', method: 'delete' }
  },
  'ws/list': {
    'get': { service: 'ws', method: 'list' }
  },
  'ws/pdf': {
    'post': { service: 'ws', method: 'pdf' }
  },
  'ws/img': {
    'post': { service: 'ws', method: 'img' }
  },
  'ws/attach': {
    'post': { service: 'ws', method: 'attach' },
    'delete': { service: 'ws', method: 'deleteAttach' }
  },
  'ws/preview': {
    'post': { service: 'ws', method: 'preview' }
  },
  'ws/attach-list': {
    'get': { service: 'ws', method: 'attachList' }
  },
  'ws/mark': {
    'patch': { service: 'ws', method: 'updateMark' }
  },
  'ws/restore': {
    'post': { service: 'ws', method: 'restore' }
  },
  'ws/clean': {
    'post': { service: 'ws', method: 'clean' }
  },
  'ws/note': {
    'post': { service: 'ws', method: 'noteUpdate' },
    'get': { service: 'ws', method: 'noteContent' }
  },
  // 节点相关API
  'node': {
    'post': { service: 'node', method: 'create' },
    'patch': { service: 'node', method: 'update' },
    'delete': { service: 'node', method: 'delete' }
  },
  'node/list': {
    'get': { service: 'node', method: 'list' }
  },
  'node/link': {
    'post': { service: 'node', method: 'link' },
    'delete': { service: 'node', method: 'deleteLink' }
  },
  'node/pos': {
    'patch': { service: 'node', method: 'updatePosition' }
  },
  'node/size': {
    'patch': { service: 'node', method: 'updateSize' }
  },
  'node/edge-label': {
    'patch': { service: 'node', method: 'updateEdgeLabel' }
  },
  'node/edge-style': {
    'patch': { service: 'node', method: 'updateEdgeStyle' }
  },
  'node/edge-data': {
    'patch': { service: 'node', method: 'updateEdgeData' }
  },
  'pdf/insert-text': {
    'post': { service: 'ws', method: 'insertText' }
  },
  // 会话
  'sess': {
    'post': {service: 'session', method: 'create'},
    'patch': {service: 'session', method: 'update'},
    'delete': {service: 'session', method: 'delete'},
    'get': {service: 'session', method: 'list'}
  },
  'sess/detail': {
    'get': {service: 'session', method: 'detail'}
  },
  // 标签组相关API
  'tag/group': {
    'post': { service: 'tag', method: 'createGroup' },
    'patch': { service: 'tag', method: 'updateGroup' },
    'delete': { service: 'tag', method: 'deleteGroup' },
    'get': { service: 'tag', method: 'listGroups' }
  },
  // 标签相关API
  'tag': {
    'post': { service: 'tag', method: 'createTag' },
    'patch': { service: 'tag', method: 'updateTag' },
    'delete': { service: 'tag', method: 'deleteTag' }
  },
  'tag/move': {
    'patch': { service: 'tag', method: 'moveTag' }
  },
  'tag/search': {
    'get': { service: 'tag', method: 'searchTags' }
  },
};

// 请求拦截器
httpRequest.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const url = config.url || '';
    const method = config.method?.toLowerCase() || 'get';
    console.log(`开始请求 ${url} ${method}`, config)

    // 检查是否有对应的本地API处理方法
    let apiPath = url.startsWith('/') ? url.substring(1) : url;
    apiPath = apiPath.split('?')[0];
    const localApiHandler = localApiMap[apiPath] && localApiMap[apiPath][method];

    if (localApiHandler) {
      try {
        const { service, method: serviceMethod } = localApiHandler;
        const serviceInstance = localServices[service];

        if (serviceInstance && typeof serviceInstance[serviceMethod] === 'function') {
          // 处理请求数据，确保ArrayBuffer能够正确传递
          let requestData = method === 'get' ? config.params : config.data;

          // 记录请求数据（不包含二进制内容）
          console.log('请求数据:', JSON.stringify(requestData, (key, value) => {
            if (value instanceof ArrayBuffer) {
              return `[ArrayBuffer ${value.byteLength} bytes]`;
            }
            return value;
          }));

          // 调用本地服务方法
          const result = await serviceInstance[serviceMethod](requestData);
          console.log(`请求结束: ${url} ${method}`, result)
          // 不在请求拦截器中处理响应，而是抛出一个特殊的错误对象
          // 这个错误对象会被响应拦截器捕获并处理
          throw {
            isLocalResponse: true,
            response: {
              data: {
                code: CodeEType.SUCCESS,
                data: result,
                msg: '操作成功'
              }
            }
          };
        }
      } catch (error: any) {
        if (error.isLocalResponse) {
          throw error; // 传递给响应拦截器
        }
        if (error instanceof RxError && error.code === "VD2" && error.parameters.writeError) {
          const writeError = error.parameters.writeError;
          if ('validationErrors' in writeError) {
            throw {
              isLocalResponse: true,
              response: {
                data: {
                  code: CodeEType.PARAMS_ERROR,
                  data: null,
                  msg: writeError.validationErrors[0].message
                }
              }
            };
          }
        }
        throw {
          isLocalResponse: true,
          response: {
            data: {
              code: CodeEType.CUSTOME_ERROR_OR_NETWORK_ERROR,
              data: null,
              msg: error instanceof Error ? error.message : '操作失败'
            }
          }
        };
      }
    }

    // 检查当前页面路由是否在白名单中
    const currentPath = window.location.pathname
    if (routeWhiteList.includes(currentPath)) {
      return config
    }

    // 检查API请求路径是否在白名单中·
    if (apiWhiteList.includes(config.url || '')) {
      return config
    }

    const tokenInfo = localStorage.getItem('tokenInfo');
    if (!tokenInfo) {
      // 如果当前不在登录页，才跳转
      if (currentPath !== '/login') {
        window.location.href = '/login';
      }
      return Promise.reject('未登录');
    }


    // 如果有 token
    if (tokenInfo) {
      try {
        const { token, expire }: tokenInfoType = JSON.parse(tokenInfo)
        if (Date.now() >= expire * 1000) {
          if (currentPath !== '/login') {
            window.location.href = '/login';
          }
          localStorage.removeItem('tokenInfo');
          return Promise.reject('token已过期');
        }
        config.headers["X-JWT-TOKEN"] = `${token}`;
      } catch (error) {
        localStorage.removeItem('tokenInfo');
        if (currentPath !== '/login') {
          window.location.href = '/login';
        }
        return Promise.reject('token无效');
      }
    }
  
    return config;
  }, (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
httpRequest.interceptors.response.use(
  (response: AxiosResponse) => {
    const data: ResponseType = response.data;
    if ((data as any).code === CodeEType.SUCCESS) {
      return Promise.resolve(response.data);
    }
    return Promise.reject(response.data);
  },
  (error) => {
    // 处理本地API响应
    if (error.isLocalResponse) {
      if (error.response.data.code === CodeEType.SUCCESS) {
        return Promise.resolve(error.response.data);
      }
      return Promise.reject(error.response.data);
    }

    // 处理其他错误
    return Promise.reject(error?.response?.data || { code: 3, msg: '网络错误', data: null });
  }
);
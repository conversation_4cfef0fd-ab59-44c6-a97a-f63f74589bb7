import "./App.css";
import {WorkerSpace} from "./pages/workspace";
import {Login} from "./pages/login";
import {Home} from "./pages/home";
import {Register} from "./pages/register";
import {NotFound} from "./pages/not-found";
import Test from "./test";
import {BrowserRouter, Route, Routes} from "react-router-dom";
import {Toaster} from "sonner";
import {DndProvider} from "react-dnd";
import {HTML5Backend} from "react-dnd-html5-backend";
import {TabDragMonitor} from "@/components/pdf/components/draggable-tabs/TabDragMonitor.tsx";

function App() {
    // 获取基础路径，用于GitHub Pages部署
    const basename = (import.meta.env as any).BASE_URL || '/';

    return (
        <DndProvider backend={HTML5Backend}>
            <BrowserRouter
                basename={basename}
                future={{
                    v7_startTransition: true,
                    v7_relativeSplatPath: true,
                }}
            >
                <div className="app">
                    <Routes>
                        <Route path="/" element={<Home/>}/>
                        <Route path="/workerspace" element={<WorkerSpace/>}/>
                        <Route path="/login" element={<Login/>}/>
                        <Route path="/home" element={<Home/>}/>
                        <Route path="/register" element={<Register/>}/>
                        <Route path="/test" element={<Test/>}/>
                        <Route path="*" element={<NotFound/>}/>
                    </Routes>
                    <Toaster/>
                </div>
            </BrowserRouter>
            <TabDragMonitor/>
        </DndProvider>
    );
}

export default App;

import type { JSX } from 'react';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { SelectionAlwaysOnDisplay } from '@lexical/react/LexicalSelectionAlwaysOnDisplay';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { useLexicalEditable } from '@lexical/react/useLexicalEditable';
import { CAN_USE_DOM } from '@lexical/utils';
import { useSettings } from './context/SettingsContext';
import AutoLinkPlugin from './plugins/AutoLinkPlugin';
import CodeActionMenuPlugin from './plugins/CodeActionMenuPlugin';
import CodeHighlightPrismPlugin from './plugins/CodeHighlightPrismPlugin';
// import CursorToEndPlugin from './plugins/CursorToEndPlugin';
import CodeHighlightShikiPlugin from './plugins/CodeHighlightShikiPlugin';
import DragDropPaste from './plugins/DragDropPastePlugin';
import DraggableBlockPlugin from './plugins/DraggableBlockPlugin';
import FloatingTextFormatToolbarPlugin from './plugins/FloatingTextFormatToolbarPlugin';
import ImagesPlugin from './plugins/ImagesPlugin';
import LinkPlugin from './plugins/LinkPlugin';
import MarkdownShortcutPlugin from './plugins/MarkdownShortcutPlugin';
import { MaxLengthPlugin } from './plugins/MaxLengthPlugin';
import ShortcutsPlugin from './plugins/ShortcutsPlugin';
import TableHoverActionsPlugin from './plugins/TableHoverActionsPlugin';
import ToolbarPlugin from './plugins/ToolbarPlugin';
import ContentEditable from './ui/ContentEditable';
import { $createParagraphNode, $createTextNode, $getRoot, BLUR_COMMAND, EditorState } from 'lexical';
import { useFetchData } from "@/components/note/hooks/fetch-data.ts";

import './index.css'
import { useNoteStore } from '@/store/note-store';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { useNoteSave } from '../note/hooks/note-save';
import { PLAYGROUND_TRANSFORMERS } from './plugins/MarkdownTransformers';
import { EditorWrapper } from './App';
import useNodeEdit from '../flow/hooks/node-edit';

export enum NoteTypeEnum {
  FLOW = 'flow',
  NOTE = 'note',
  NODE = 'node'
}

export type NoteType = (typeof NoteTypeEnum)[keyof typeof NoteTypeEnum]

export default function Editor({ onChange }: { onChange?: (editor: EditorState) => void }): JSX.Element {
  const {
    settings: {
      isCodeHighlighted,
      isCodeShiki,
      isCollab,
      isAutocomplete,
      isMaxLength,
      // isCharLimit,
      hasLinkAttributes,
      // isCharLimitUtf8,
      isRichText,
      showTreeView,
      // showTableOfContents,
      // shouldUseLexicalContextMenu,
      // shouldPreserveNewLinesInMarkdown,
      tableCellMerge,
      tableCellBackgroundColor,
      tableHorizontalScroll,
      // shouldAllowHighlightingWithBrackets,
      selectionAlwaysOnDisplay,
      listStrictIndent,
    },
  } = useSettings();
  // const isEditable = useLexicalEditable();
  const placeholder = isCollab
    ? 'Enter some collaborative rich text...'
    : isRichText
      ? 'Enter some rich text...'
      : 'Enter some plain text...';
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);
  const [isSmallWidthViewport, setIsSmallWidthViewport] =
    useState<boolean>(false);
  const [editor] = useLexicalComposerContext();
  const [activeEditor, setActiveEditor] = useState(editor);
  const [isLinkEditMode, setIsLinkEditMode] = useState<boolean>(false);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };


  useEffect(() => {
    const updateViewPortWidth = () => {
      const isNextSmallWidthViewport =
        CAN_USE_DOM && window.matchMedia('(max-width: 1025px)').matches;

      if (isNextSmallWidthViewport !== isSmallWidthViewport) {
        setIsSmallWidthViewport(isNextSmallWidthViewport);
      }
    };
    updateViewPortWidth();
    window.addEventListener('resize', updateViewPortWidth);

    return () => {
      window.removeEventListener('resize', updateViewPortWidth);
    };
  }, [isSmallWidthViewport]);

  return (
    <>
      <ToolbarPlugin
        editor={editor}
        activeEditor={activeEditor}
        setActiveEditor={setActiveEditor}
        setIsLinkEditMode={setIsLinkEditMode}
      />
      {/* 快捷键插件 */}
      {isRichText && (
        <ShortcutsPlugin
          editor={activeEditor}
          setIsLinkEditMode={setIsLinkEditMode}
        />
      )}
      <div
        className={`flex-1 editor-container h-full hide-scrollbar overflow-auto`}>
        {isMaxLength && <MaxLengthPlugin maxLength={30} />}
        <DragDropPaste />
        {selectionAlwaysOnDisplay && <SelectionAlwaysOnDisplay />}
        <ClearEditorPlugin />
        {/* <EmojiPickerPlugin /> */}
        {/* <AutoEmbedPlugin /> */}
        {/* <MentionsPlugin /> */}
        {/* <EmojisPlugin /> */}
        {/* <HashtagPlugin /> */}
        {/* <KeywordsPlugin /> */}
        {/* <SpeechToTextPlugin /> */}
        <AutoLinkPlugin />
        {/* <CursorToEndPlugin /> */}
        <>

          <RichTextPlugin
            contentEditable={
              <div className="editor-scroller">
                <div className="editor" ref={onRef}>
                  <ContentEditable placeholder={placeholder} />
                </div>
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <MarkdownShortcutPlugin />
          {isCodeHighlighted &&
            (isCodeShiki ? (
              <CodeHighlightShikiPlugin />
            ) : (
              <CodeHighlightPrismPlugin />
            ))}
          <ListPlugin hasStrictIndent={listStrictIndent} />
          <CheckListPlugin />
          <TablePlugin
            hasCellMerge={tableCellMerge}
            hasCellBackgroundColor={tableCellBackgroundColor}
            hasHorizontalScroll={tableHorizontalScroll}
          />
          <ImagesPlugin />
          <LinkPlugin hasLinkAttributes={hasLinkAttributes} />
          {floatingAnchorElem && !isSmallWidthViewport && (
            <>
              <DraggableBlockPlugin anchorElem={floatingAnchorElem} />
              <CodeActionMenuPlugin anchorElem={floatingAnchorElem} />
              <TableHoverActionsPlugin anchorElem={floatingAnchorElem} />
              <FloatingTextFormatToolbarPlugin
                anchorElem={floatingAnchorElem}
                setIsLinkEditMode={setIsLinkEditMode}
              />
            </>
          )}
        </>
      </div>
    </>
  );
}

const useInitData = () => {
  const [editor] = useLexicalComposerContext();
  // 获取数据
  const { fetchData } = useFetchData()

  useEffect(() => {
    //  初始化数据
    fetchData().then((e) => {
      editor.update(() => {
        $convertFromMarkdownString(e, PLAYGROUND_TRANSFORMERS);
      });
    })
  }, [editor])

}

// TODO 后续把三种情况的编辑器都拆分单独文件
export const GlobalEditorWithHook = () => {
  const [editor] = useLexicalComposerContext();
  const { saveNote } = useNoteSave()
  // TODO: 多个编辑器同时存在的时候，这里需要调整
  const { insertContent } = useNoteStore((state) => ({
    insertContent: state.insertContent,
    setInsertContent: state.setInsertContent,
  }))

  useInitData()

  useEffect(() => {
    if (insertContent) {
      editor.update(() => {
        const root = $getRoot();
        // 创建一个新的段落节点
        const paragraphNode = $createParagraphNode();
        // 创建一个文本节点，包含要插入的文本
        const textNode = $createTextNode(insertContent?.trim() || '');

        // 将文本节点添加到段落节点
        paragraphNode.append(textNode);

        // 将段落节点添加到根节点的末尾
        root.append(paragraphNode);
      }, {
        onUpdate: () => {
          // 在编辑器更新后执行滚动

          setTimeout(() => {
            const editorRootElement = editor.getRootElement();
            if (editorRootElement) {
              editorRootElement.scrollIntoView({ block: 'end', behavior: 'smooth' });
            }
          }, 10)
        }
      });
    }
  }, [insertContent])

  return (
    <>
      <Editor />
      <MyBlurPlugin onChange={(value) => {
        saveNote(value)
      }} />
    </>
  )
}

// flow节点弹框编辑器
export const FlowEditorWithHook = (props: {
  value?: string,
  onChange?: (value: string) => void,
}) => {

  const [editor] = useLexicalComposerContext();

  useEffect(() => {

    editor.update(() => {
      $convertFromMarkdownString(props?.value || '', PLAYGROUND_TRANSFORMERS);
    });
  }, [editor])

  return (
    <>
      <Editor />
      <MyBlurPlugin onChange={(value) => {
        console.log('onChange', value)
        props?.onChange?.(value)
      }} />
    </>
  )
}

// flow节点内部编辑器
export const NodeEditorWithHook = (props: {
  onChange?: (value: string) => void,
  content?: string,
}) => {

  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    editor.update(() => {
      $convertFromMarkdownString(props?.content || '', PLAYGROUND_TRANSFORMERS);
    });
  }, [editor, props?.content])

  return <>
    <Editor />
    <MyBlurPlugin onChange={(value) => {
      props?.onChange?.(value)
    }} />
  </>
}

const MyBlurPlugin = ({ onChange }: { onChange?: (editor: string) => void }) => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerCommand(
      BLUR_COMMAND,
      () => {
        editor.read(() => {
          const markdown = $convertToMarkdownString(
            PLAYGROUND_TRANSFORMERS,
            undefined, // node
            false, // shouldPreserveNewLinesInMarkdown
          );
          onChange?.(markdown)
        })
        // 在这里处理失焦逻辑
        return false; // 返回 false 允许命令继续传播
      },
      0 // 优先级
    );
  }, [editor, onChange]);

  return null;
}
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import { Tag } from '../TagGroup';
import { TagGroupData, mockTagGroups } from '../mock-data';
import { tagService } from '@/local';
import { TagEvents } from '@/local/services/tag-service';

export interface TagStore {
  // 状态
  tagGroups: TagGroupData[];
  isLoading: boolean;
  error: string | null;
  
  // 基础操作
  loadTagGroups: () => Promise<void>;
  setTagGroups: (groups: TagGroupData[]) => void;
  resetToMockData: () => void;
  
  // 分组操作
  addGroup: (group: Omit<TagGroupData, 'id'>) => Promise<string>;
  updateGroup: (id: string, updates: Partial<Omit<TagGroupData, 'id'>>) => Promise<boolean>;
  deleteGroup: (id: string) => Promise<boolean>;
  getGroup: (id: string) => TagGroupData | undefined;
  
  // 标签操作  
  addTag: (groupId: string, tag: Omit<Tag, 'id'>) => Promise<string | null>;
  updateTag: (groupId: string, tagId: string, updates: Partial<Omit<Tag, 'id'>>) => Promise<boolean>;
  deleteTag: (groupId: string, tagId: string) => Promise<boolean>;
  moveTag: (tagId: string, fromGroupId: string, toGroupId: string) => Promise<boolean>;
  getTag: (groupId: string, tagId: string) => Tag | undefined;
  
  // 查询操作
  findTagInGroups: (tagId: string) => { group: TagGroupData; tag: Tag } | null;
  getGroupsCount: () => number;
  getTagsCount: (groupId?: string) => number;
  searchTags: (query: string) => Promise<{ group: TagGroupData; tag: Tag }[]>;
  searchGroups: (query: string) => TagGroupData[];
  
  // 排序操作
  reorderGroups: (groupIds: string[]) => Promise<boolean>;
  reorderTags: (groupId: string, tagIds: string[]) => Promise<boolean>;
}

const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const useTagStore = create<TagStore>()(
  devtools(
    immer((set, get) => ({
      // 初始状态
      tagGroups: [],
      isLoading: false,
      error: null,
      
      // 基础操作
      loadTagGroups: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          const response = await tagService.listGroups();
          console.log('response',response);
          set((state) => {
            state.tagGroups = response.groups;
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '加载标签组失败';
            state.isLoading = false;
          });
        }
      },
      
      setTagGroups: (groups) => {
        set((state) => {
          state.tagGroups = groups;
        });
      },
      
      resetToMockData: async () => {
        try {
          // 先清空数据库中的所有标签和标签组
          const currentGroups = get().tagGroups;
          for (const group of currentGroups) {
            await tagService.deleteGroup({ id: group.id });
          }
          
          // 重新创建mock数据
          for (const mockGroup of mockTagGroups) {
            const { id: groupId } = await tagService.createGroup({ title: mockGroup.title });
            for (const mockTag of mockGroup.tags) {
              await tagService.createTag({ groupId, name: mockTag.name });
            }
          }
          
          // 重新加载数据
          await get().loadTagGroups();
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '重置数据失败';
          });
        }
      },
      
      // 分组操作
      addGroup: async (group) => {
        try {
          const { id } = await tagService.createGroup({ title: group.title });
          await get().loadTagGroups(); // 重新加载数据
          return id;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '创建标签组失败';
          });
          throw error;
        }
      },
      
      updateGroup: async (id, updates) => {
        try {
          await tagService.updateGroup({ id, title: updates.title || '' });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '更新标签组失败';
          });
          return false;
        }
      },
      
      deleteGroup: async (id) => {
        try {
          await tagService.deleteGroup({ id });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '删除标签组失败';
          });
          return false;
        }
      },
      
      getGroup: (id) => {
        return get().tagGroups.find(g => g.id === id);
      },
      
      // 标签操作
      addTag: async (groupId, tag) => {
        try {
          const { id } = await tagService.createTag({ groupId, name: tag.name });
          await get().loadTagGroups(); // 重新加载数据
          return id;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '创建标签失败';
          });
          return null;
        }
      },
      
      updateTag: async (groupId, tagId, updates) => {
        try {
          await tagService.updateTag({ id: tagId, name: updates.name || '' });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '更新标签失败';
          });
          return false;
        }
      },
      
      deleteTag: async (groupId, tagId) => {
        try {
          await tagService.deleteTag({ id: tagId });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '删除标签失败';
          });
          return false;
        }
      },
      
      moveTag: async (tagId, fromGroupId, toGroupId) => {
        if (fromGroupId === toGroupId) return true;
        
        try {
          await tagService.moveTag({ tagId, fromGroupId, toGroupId });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '移动标签失败';
          });
          return false;
        }
      },
      
      getTag: (groupId, tagId) => {
        const group = get().tagGroups.find(g => g.id === groupId);
        return group?.tags.find(t => t.id === tagId);
      },
      
      // 查询操作
      findTagInGroups: (tagId) => {
        const state = get();
        for (const group of state.tagGroups) {
          const tag = group.tags.find(t => t.id === tagId);
          if (tag) {
            return { group, tag };
          }
        }
        return null;
      },
      
      getGroupsCount: () => {
        return get().tagGroups.length;
      },
      
      getTagsCount: (groupId) => {
        const state = get();
        if (groupId) {
          const group = state.tagGroups.find(g => g.id === groupId);
          return group?.tags.length || 0;
        }
        return state.tagGroups.reduce((total, group) => total + group.tags.length, 0);
      },
      
      searchTags: async (query) => {
        try {
          const results = await tagService.searchTags(query);
          return results.map((result: any) => ({
            group: { id: result.groupId, title: result.groupTitle, tags: [] },
            tag: result.tag
          }));
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '搜索标签失败';
          });
          return [];
        }
      },
      
      searchGroups: (query) => {
        const state = get();
        const lowerQuery = query.toLowerCase();
        return state.tagGroups.filter(group => 
          group.title.toLowerCase().includes(lowerQuery)
        );
      },
      
      // 排序操作
      reorderGroups: async (groupIds) => {
        try {
          await tagService.reorderGroups({ groupIds });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '重排序标签组失败';
          });
          return false;
        }
      },
      
      reorderTags: async (groupId, tagIds) => {
        try {
          await tagService.reorderTags({ groupId, tagIds });
          await get().loadTagGroups(); // 重新加载数据
          return true;
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : '重排序标签失败';
          });
          return false;
        }
      },
    })),
    {
      name: 'tag-store',
      partialize: (state: TagStore) => ({ tagGroups: state.tagGroups })
    }
  )
);

// 设置事件监听器来自动更新状态
TagEvents.on('groupCreated', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('groupUpdated', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('groupDeleted', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('tagCreated', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('tagUpdated', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('tagDeleted', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('tagMoved', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('groupsReordered', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

TagEvents.on('tagsReordered', () => {
  const store = useTagStore.getState();
  store.loadTagGroups();
});

// 简化的选择器 Hooks
export const useTagGroups = () => useTagStore(state => state.tagGroups);
export const useTagActions = () => useTagStore(state => ({
  addGroup: state.addGroup,
  updateGroup: state.updateGroup,
  deleteGroup: state.deleteGroup,
  addTag: state.addTag,
  updateTag: state.updateTag,
  deleteTag: state.deleteTag,
  moveTag: state.moveTag,
  resetToMockData: state.resetToMockData,
  reorderGroups: state.reorderGroups,
  reorderTags: state.reorderTags,
}));
export const useTagQueries = () => useTagStore(state => ({
  getGroup: state.getGroup,
  getTag: state.getTag,
  findTagInGroups: state.findTagInGroups,
  searchTags: state.searchTags,
  searchGroups: state.searchGroups,
}));
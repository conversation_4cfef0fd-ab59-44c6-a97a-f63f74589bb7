import React, { useState, useCallback, useMemo } from 'react';
import { 
  UncontrolledTreeEnvironment, 
  Tree, 
  StaticTreeDataProvider,
  TreeItem as RCTTreeItem,
  TreeItemIndex,
  TreeItemRenderContext
} from 'react-complex-tree';
import { 
  Folder, 
  File, 
  Plus,
  Search,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react';
import 'react-complex-tree/lib/style-modern.css';
import './FileTreePanel.css';

export interface FileTreeNode {
  id: string;
  title: string;
  subtitle?: string;
  isDirectory?: boolean;
  path?: string;
  size?: number;
  lastModified?: Date;
  isHidden?: boolean;
  children?: string[];
  data?: any;
  metadata?: {
    tags?: string[];
    color?: string;
    starred?: boolean;
  };
}

export interface TreeItems {
  [key: string]: RCTTreeItem<FileTreeNode>;
}

interface FileTreePanelProps {
  initialData?: FileTreeNode[];
  onFileSelect?: (node: FileTreeNode) => void;
  onFileCreate?: (path: string, type: 'file' | 'folder') => void;
  onFileDelete?: (path: string) => void;
  onFileRename?: (oldPath: string, newPath: string) => void;
  showHiddenFiles?: boolean;
  searchable?: boolean;
  className?: string;
}

export const FileTreePanel: React.FC<FileTreePanelProps> = ({
  initialData = [],
  onFileSelect,
  onFileCreate,
  onFileDelete,
  onFileRename,
  showHiddenFiles = false,
  searchable = true,
  className = ''
}) => {
  const [searchString, setSearchString] = useState('');
  const [showHidden, setShowHidden] = useState(showHiddenFiles);
  const [selectedNode, setSelectedNode] = useState<FileTreeNode | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    node: FileTreeNode;
  } | null>(null);

  // 将数据转换为 react-complex-tree 格式
  const convertToTreeItems = useCallback((nodes: FileTreeNode[]): { items: TreeItems; rootItem: string } => {
    const items: TreeItems = {};
    const rootId = 'root';
    
    items[rootId] = {
      index: rootId,
      canMove: false,
      canRename: false,
      data: {
        id: rootId,
        title: 'Root',
        isDirectory: true
      },
      children: [],
      isFolder: true
    };

    // 处理所有节点
    nodes.forEach(node => {
      if (!showHidden && node.isHidden) return;

      const itemId = node.id || node.path || node.title;

      items[itemId] = {
        index: itemId,
        canMove: true,
        canRename: true,
        data: node,
        children: node.children || [],
        isFolder: node.isDirectory || false
      };

      // 如果是顶级节点，添加到根节点
      const isTopLevel = !nodes.some(n => 
        n.children && n.children.includes(itemId)
      );

      if (isTopLevel) {
        items[rootId].children = [...(items[rootId].children || []), itemId];
      }
    });

    return { items, rootItem: rootId };
  }, [showHidden]);

  // 确保数据格式正确
  const processedData = useMemo(() => {
    return initialData.map(node => ({
          ...node,
      id: node.id || node.path || node.title,
      path: node.path || `/${node.title}`
    }));
  }, [initialData]);

  const { items, rootItem } = useMemo(() => convertToTreeItems(processedData), [processedData, convertToTreeItems]);

  const dataProvider = useMemo(() => {
    return new StaticTreeDataProvider(items);
  }, [items]);

  // 获取文件图标
  const getFileIcon = useCallback((node: FileTreeNode) => {
    if (node.isDirectory) {
      return <Folder size={16} className="text-blue-500" />;
    }
    
    const extension = node.title.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf': return <File size={16} className="text-red-500" />;
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx': return <File size={16} className="text-yellow-500" />;
      case 'css':
      case 'scss':
      case 'sass': return <File size={16} className="text-blue-500" />;
      case 'html': return <File size={16} className="text-orange-500" />;
      case 'json': return <File size={16} className="text-green-500" />;
      case 'md': return <File size={16} className="text-purple-500" />;
      default: return <File size={16} className="text-gray-500" />;
    }
  }, []);

  // 处理节点点击
  const handleNodeClick = useCallback((node: FileTreeNode) => {
    setSelectedNode(node);
    if (onFileSelect) {
      onFileSelect(node);
    }
  }, [onFileSelect]);

  // 处理右键菜单
  const handleContextMenu = useCallback((event: React.MouseEvent, node?: FileTreeNode) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      node: node || {
        id: 'root',
        title: '根目录', 
        isDirectory: true,
        path: '/'
      }
    });
  }, []);

  // 处理空白区域右键菜单
  const handleTreeContainerContextMenu = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const isFileItem = target.closest('.rct-tree-item-li') || target.closest('.rct-tree-item-button');
    
    if (!isFileItem) {
      handleContextMenu(event);
    }
  }, [handleContextMenu]);

  // 自定义渲染函数
  const renderItem = useCallback(({ item, depth, children, title, arrow, context }: {
    item: RCTTreeItem<FileTreeNode>;
    depth: number;
    children: React.ReactNode;
    title: React.ReactNode;
    arrow: React.ReactNode;
    context: TreeItemRenderContext;
  }) => {
    const node = item.data;
    const isSelected = selectedNode?.id === node.id;
    const InteractiveComponent = context.isRenaming ? 'div' : 'button';
    const type = context.isRenaming ? undefined : 'button';

    return (
      <div
        {...(context.itemContainerWithChildrenProps as any)}
        className={`rct-tree-item-li ${isSelected ? 'rct-tree-item-li-selected' : ''} ${node.isHidden ? 'rct-tree-item-li-hidden' : ''}`}
      >
        <div
          {...(context.itemContainerWithoutChildrenProps as any)}
          className={`rct-tree-item-title-container ${isSelected ? 'rct-tree-item-title-container-selected' : ''}`}
          onContextMenu={(e) => handleContextMenu(e, node)}
          style={{ paddingLeft: `${depth * 20}px` }}
        >
          <InteractiveComponent
            {...(context.interactiveElementProps as any)}
            type={type}
            className="rct-tree-item-button"
            onClick={() => handleNodeClick(node)}
          >
            <div className="rct-tree-item-arrow">
              {arrow}
            </div>
            <div className="rct-tree-item-icon">
              {getFileIcon(node)}
            </div>
            <div className="rct-tree-item-title">
              {title}
            </div>
            {node.metadata?.starred && (
              <span className="rct-tree-item-star">⭐</span>
            )}
            {node.isHidden && (
              <span className="rct-tree-item-hidden">•</span>
            )}
          </InteractiveComponent>
        </div>
        {children}
      </div>
    );
  }, [selectedNode, getFileIcon, handleNodeClick, handleContextMenu]);

  // 上下文菜单组件
  const ContextMenu = () => {
    if (!contextMenu) return null;

    const isRootContext = contextMenu.node.id === 'root';

    return (
      <div 
        className="rct-context-menu"
        style={{ 
          position: 'fixed', 
          top: contextMenu.y, 
          left: contextMenu.x,
          zIndex: 1000
        }}
        onBlur={() => setContextMenu(null)}
      >
        <div className="rct-context-menu-item" onClick={() => {
          if (onFileCreate) onFileCreate(contextMenu.node.path || '/', 'file');
          setContextMenu(null);
        }}>
          <Plus size={14} /> 上传文件
        </div>
        <div className="rct-context-menu-item" onClick={() => {
          if (onFileCreate) onFileCreate(contextMenu.node.path || '/', 'folder');
          setContextMenu(null);
        }}>
          <Folder size={14} /> 新建文件夹
        </div>
        
        {!isRootContext && (
          <>
            <hr className="rct-context-menu-separator" />
            <div className="rct-context-menu-item" onClick={() => {
              if (onFileDelete) onFileDelete(contextMenu.node.path || '');
              setContextMenu(null);
            }}>
              删除
            </div>
            <div className="rct-context-menu-item" onClick={() => {
              const newName = prompt('请输入新名称:', contextMenu.node.title);
              if (newName && newName !== contextMenu.node.title && onFileRename) {
                const oldPath = contextMenu.node.path || '';
                const newPath = oldPath.replace(contextMenu.node.title, newName);
                onFileRename(oldPath, newPath);
              }
              setContextMenu(null);
            }}>
              重命名
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <div className={`rct-file-tree-panel ${className}`}>
      {/* 搜索栏 */}
      {searchable && (
        <div className="rct-search-container">
          <Search size={14} className="rct-search-icon" />
          <input
            type="text"
            placeholder="搜索文件..."
            value={searchString}
            onChange={(e) => setSearchString(e.target.value)}
            className="rct-search-input"
          />
        </div>
      )}

      {/* 文件树 */}
      <div 
        className="rct-tree-container"
        onContextMenu={handleTreeContainerContextMenu}
      >
        <UncontrolledTreeEnvironment
          dataProvider={dataProvider}
          getItemTitle={item => item.data.title}
          viewState={{
            'file-tree': {
              expandedItems: Object.keys(items).filter(key => items[key].isFolder)
            }
          }}
          renderItemTitle={({ title }) => <span>{title}</span>}
          renderItem={renderItem}
          canDragAndDrop={true}
          canDropOnFolder={true}
          canReorderItems={true}
          canSearch={true}
        >
          <Tree
            treeId="file-tree"
            rootItem={rootItem}
            treeLabel="文件浏览器"
          />
        </UncontrolledTreeEnvironment>
      </div>

      {/* 上下文菜单 */}
      <ContextMenu />
      
      {/* 点击其他地方关闭菜单 */}
      {contextMenu && (
        <div 
          className="rct-context-menu-overlay"
          onClick={() => setContextMenu(null)}
        />
      )}
    </div>
  );
};

export default FileTreePanel; 
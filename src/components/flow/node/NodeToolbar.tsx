import {DeleteOutlined, EditOutlined,} from "@ant-design/icons";
import {memo, useState} from "react";
import {Button, Space, Tooltip} from "antd";
import {nodeData} from "@/store/flow-store.ts";
import {SketchPicker} from "react-color";
import {useNodeDelete} from "@/components/flow/hooks/node-delete.ts";
import {useNodeColor} from "@/components/flow/hooks/node-color.ts";
import {useNodeChat} from "@/components/flow/hooks/node-chat.ts";
import {useNodeNote} from "@/components/flow/hooks/node-note.ts";
import {IconFontNode} from "@/common/constant.ts";
import {useEditNodeStore} from "@/store/workerspace-store/edit-node-store";
import { messageError } from "@/components/message/hooks";

const NodeToolbar = memo(({nodeData, id, setEditMode}: {
    nodeData: nodeData
    id: string
    setEditMode: (editMode: number) => void,
}) => {
    // 颜色列表
    const colorList = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF"]
    // 是否显示颜色列表
    const [showColorList, setShowColorList] = useState(false);
    // 是否显示颜色选择器面板
    const [showColorPicker, setShowColorPicker] = useState(false);
    // 编辑节点弹窗状态
    const { openEditNode } = useEditNodeStore();
    // 删除节点
    const {handleDeleteNode} = useNodeDelete()
    // 选择颜色
    const {handleColorChange} = useNodeColor()
    // AI聊天
    const {handleChat} = useNodeChat()
    // 记事本
    const {handleCopyToNotebook} = useNodeNote()
    return (
        <div className="z-10 absolute bottom-0 right-0 w-full translate-y-1/2 bg-transparent">
            <div className="node-toolbar w-full relative">
                <div
                    className="w-full inline-flex  justify-between  items-center gap-2.5  p-2 rounded-md  text-white cursor-pointer bg-transparent">
                    {/* 删除按钮 */}
                    <Tooltip title="删除节点">
                        <Button
                            type="primary"
                            shape="circle"
                            size="small"
                            style={{
                                backgroundColor: "rgba(0,0,0,.5)",
                            }}
                            icon={<DeleteOutlined/>}
                            onClick={(e) => {
                                handleDeleteNode([id])
                                e.preventDefault()
                                e.stopPropagation()
                            }}
                        />
                    </Tooltip>

                    {/* 聊天按钮 */}
                    <Space>
                        {/* 颜色预览框 */}
                        <div
                            className="relative overflow-hidden color-select"
                            onMouseEnter={() => setShowColorList(true)}
                            onMouseLeave={() => setShowColorList(false)}
                        >
                            {/* 历史颜色选择区域 */}
                            <div className="flex justify-around items-center">
                                {showColorList && (
                                    <Space
                                        style={{
                                            animation: `${showColorList ? "slideOutFromRight" : "slideInToRight"
                                            } 0.3s ease-in-out forwards`,
                                        }}
                                    >
                                        <div className="w-5 h-5 rounded-full border border-white cursor-pointer"></div>
                                        {colorList.map((historyColor, index) => (
                                            <div
                                                key={index}
                                                className="w-5 h-5 rounded-full border border-white cursor-pointer"
                                                style={{backgroundColor: historyColor}}
                                                onClick={(e) => {
                                                    handleColorChange(id, historyColor)
                                                    e.preventDefault()
                                                    e.stopPropagation()
                                                }}
                                            />
                                        ))}
                                    </Space>
                                )}
                                <Tooltip title="选择颜色">
                                    <Button
                                        type="primary"
                                        shape="circle"
                                        size="small"
                                        style={{
                                            backgroundColor: "rgba(0,0,0,.5)",
                                        }}
                                        icon={<IconFontNode type="icon-color-filling" style={{color: "#fff"}}/>}
                                        onClick={(e) => {
                                            setShowColorPicker(true)
                                            e.preventDefault()
                                            e.stopPropagation()
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                        <Tooltip title="AI聊天">
                            <Button
                                type="primary"
                                shape="circle"
                                size="small"
                                style={{
                                    backgroundColor: "rgba(0,0,0,.5)",
                                }}
                                icon={<IconFontNode type="icon-liaotianchat52" style={{color: "#fff"}}/>}
                                onClick={(e) => {
                                    if (nodeData?.content?.trim()) {
                                        handleChat(id)
                                        e.preventDefault()
                                        e.stopPropagation()
                                    }else {
                                        messageError('当前节点为空')
                                    }
                                }}
                            />
                        </Tooltip>
                        <Tooltip title="复制到笔记本">
                            <Button
                                type="primary"
                                shape="circle"
                                size="small"
                                style={{
                                    backgroundColor: "rgba(0,0,0,.5)",
                                }}
                                icon={<IconFontNode type="icon-note"/>}
                                onClick={(e) => {
                                    if (nodeData?.content?.trim()) {
                                        handleCopyToNotebook([id])
                                        e.preventDefault()
                                        e.stopPropagation()
                                    }else {
                                        messageError('当前节点为空')
                                    }
                                }}
                            />
                        </Tooltip>
                        {/* 编辑按钮 */}
                        <Tooltip title="编辑节点">
                            <Button
                                type="primary"
                                shape="circle"
                                size="small"
                                icon={<EditOutlined/>}
                                style={{
                                    backgroundColor: "rgba(0,0,0,.5)",
                                }}
                                onClick={(e) => {
                                    setEditMode(0)
                                    openEditNode(id)
                                    e.preventDefault()
                                    e.stopPropagation()
                                }}
                            />
                        </Tooltip>
                    </Space>
                </div>
                {/* 颜色选择器面板 */}
                {showColorPicker && (
                    <div className="absolute z-2000 top-full right-0 nodrag">
                        <SketchPicker
                            color={nodeData.color}
                            onChange={(color) => {
                                handleColorChange(id, color.hex);
                                setShowColorPicker(false);
                            }}
                        />
                    </div>
                )}

            </div>
        </div>
    );
})

export {NodeToolbar}
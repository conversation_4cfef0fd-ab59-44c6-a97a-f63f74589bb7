import { useCallback } from "react";
import { useChatStore } from "@/store/workerspace-store/chat-store.ts";
import { CustomHighlight, usePdfStore } from "@/store/pdf-store.ts";
import { message } from "antd";
import { ChatResponseRefs } from "@/components/chat-panel/types";
import { usePanelOpenStore } from "@/store/panel-open-store.tsx";
import { messageError } from "@/components/message/hooks";

export const useHighlightChat = () => {
    const { toggleChatPanel } = usePanelOpenStore((state) => ({
        toggleChatPanel: state.toggleChatPanel,
    }));
    const { addFooterRef, updateCurrentChat, updateChatList } = useChatStore((state) => ({
        addFooterRef: state.addFooterRef,
        updateCurrentChat: state.updateCurrentChat,
        updateChatList: state.updateChatList,
    }));
    const handleChat = useCallback((highlight: CustomHighlight) => {
        // 打开聊天面板
        toggleChatPanel(true);
        const pdfState = usePdfStore.getState()
        const chatState = useChatStore.getState()
        const currentChatRid = chatState.currentChatRid
        const footerRefs = chatState.footerRefs
        const currentChat = chatState.currentChat

        const pdfHightLightState = pdfState.pdfs.get(highlight.aid)

        const pdfRef = {
            id: highlight.id,
            title: pdfHightLightState?.filename || "",
            url: pdfHightLightState?.url || "",
            page: highlight.position.boundingRect?.pageNumber || 1,
            content: highlight.type !== "area" ? highlight.content?.text : highlight.content?.image,
            type: highlight.type !== "area" ? 3 : 4
        }
        if (currentChatRid === -1) {
            if (footerRefs.length >= 10) {
                message.error("最多只能添加10个页脚引用");
                return;
            }
            const item = footerRefs.find((item) => item.id === pdfRef.id);
            const pdfItem = footerRefs.find(item => item.id === (highlight as CustomHighlight).aid);
            if (!item) {
                // 追加附件
                if (!pdfItem) {
                    addFooterRef({
                        ...pdfRef
                    });
                    return
                }
                // 添加高亮
                addFooterRef(pdfRef);
            } else {
                messageError("重复引用!")
            }
        } else {
            const tempRefs: ChatResponseRefs = currentChat?.refs || [];
            if (tempRefs.length >= 10) {
                message.error("最多只能添加10个引用");
                return;
            }
            const item = tempRefs.find((item) => item.id === pdfRef.id);
            const pdfItem = tempRefs.find(item => item.id === (highlight as CustomHighlight).aid);
            if (!item) {
                // 追加附件
                if (!pdfItem) {
                    tempRefs.push({
                        type: 1,
                        id: (highlight as CustomHighlight).aid,
                        title: pdfRef.title,
                        url: pdfRef.url,
                        content: "",
                    });
                }
                // 添加高亮
                tempRefs.push({
                    type: pdfRef.type,
                    id: pdfRef.id,
                    title: pdfRef.title,
                    content: pdfRef.content!,
                    page: pdfRef.page,
                });
                // 更新当前聊天和会话详情
                if (currentChat) {
                    currentChat.refs = tempRefs;
                    updateCurrentChat(currentChat);
                    updateChatList(currentChat);
                }
            } else {
                messageError("重复引用!")
            }
        }
    }, [toggleChatPanel, addFooterRef, updateCurrentChat, updateChatList])
    return {
        handleChat
    }
}
import { create } from "zustand";

export const useWorkerSpaceStore = create<{
  wid: string | null;
  workerspaceRef: HTMLDivElement | null;
  // refreshSnapshot: number | string;
  snapshot: File | null;
  setWid: (wid: string | null) => void;
  setWorkerspaceRef: (workerspaceRef: HTMLDivElement | null) => void;
  // setRefreshSnapshot: (refreshSnapshot: number | string) => void;
  setSnapshot: (snapshot: File | null) => void;
}>((set) => ({
  wid: null,
  workerspaceRef: null,
  // refreshSnapshot: "",
  snapshot: null,
  setWid: (wid) => set({ wid }),
  setWorkerspaceRef: (workerspaceRef) => set({ workerspaceRef }),
  // setRefreshSnapshot: (refreshSnapshot) => set({ refreshSnapshot }),
  setSnapshot: (snapshot) => set({ snapshot }),
}));

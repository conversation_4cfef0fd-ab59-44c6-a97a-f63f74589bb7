import {useCallback} from "react";
import {CustomNode, useFlowStore} from "@/store/flow-store.ts";
import {Edge, Position} from "@xyflow/react";
import dagre from "@dagrejs/dagre";
import {updateNodesPosition} from "@/api/canvas";
import {NODE_DEFAULTS} from "@/components/flow/constants/node-defaults";

const useNodeLayout = () => {
    const {setNodes} = useFlowStore((state) => ({
        setNodes: state.setNodes,
    }));
    const getLayoutedElements = useCallback((nodes: CustomNode[], edges: Edge[], direction = 'TB') => {
        const dagreGraph = new dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
        const isHorizontal = direction === 'LR';
        dagreGraph.setGraph({rankdir: direction});
        dagreGraph.setDefaultEdgeLabel(() => ({}));
        // 非独立节点自动布局
        const connectedNodeIds = new Set();
        edges.forEach(edge => {
            connectedNodeIds.add(edge.source);
            connectedNodeIds.add(edge.target);
            dagreGraph.setEdge(edge.source, edge.target)
        });

        const connectedNodes = nodes.filter(node => connectedNodeIds.has(node.id));
        const freeNodes = nodes.filter(node => !connectedNodeIds.has(node.id));
        connectedNodes.forEach((node) => {
            dagreGraph.setNode(node.id, {width: node.measured?.width, height: node.measured?.height});
        });

        dagre.layout(dagreGraph);

        let maxPositionX = -Infinity
        const newConnectedNodes = connectedNodes.map((node) => {
            const nodeWithPosition = dagreGraph.node(node.id);
            maxPositionX = Math.max(maxPositionX, nodeWithPosition.x + (node.measured?.width ?? 0) / 2)
            return {
                ...node,
                targetPosition: isHorizontal ? Position.Left : Position.Top,
                sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
                // We are shifting the dagre node position (anchor=center center) to the top left
                // so it matches the React Flow node anchor point (top left).
                position: {
                    x: nodeWithPosition.x - (node.measured?.width ?? 0) / 2,
                    y: nodeWithPosition.y - (node.measured?.height ?? 0) / 2,
                },
            };
        });
        if (maxPositionX === -Infinity) {
            maxPositionX = 0
        }
        let positionX = maxPositionX + 30
        let positionY = 0
        let prevNode: CustomNode | null
        // 独立节点布局
        const newFreeNodes = freeNodes.map((node, index) => {
            if (index !== 0 && index % 5 === 0) {
                positionX += 30 + (prevNode?.measured?.width ?? 0)
                positionY = 0
            } else {
                positionY += 30 + (prevNode?.measured?.height ?? 0)
            }
            return prevNode = {
                ...node,
                targetPosition: isHorizontal ? Position.Left : Position.Top,
                sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
                position: {
                    x: positionX,
                    y: positionY,
                },
            }
        });

        return {nodes: [...newConnectedNodes, ...newFreeNodes]};
    }, []);
    const onLayout = useCallback(
        async (direction: 'TB' | 'LR' = "LR") => {
            const nodes = useFlowStore.getState().nodes;
            const edges = useFlowStore.getState().edges;
            const {nodes: layoutedNodes} =
                getLayoutedElements(nodes, edges, direction);
            // 接口
            try {
                await updateNodesPosition({
                    positions: layoutedNodes.map((node) => ({
                        nid: node.id,
                        x: node.position.x,
                        y: node.position.y,
                    })),
                })
            } catch (e) {
                console.log(e)
            }
            setNodes([...layoutedNodes]);
        },
        []
    );
    // 计算新节点位置
    const calculateNodePosition = useCallback((pids?: string[], nid?: string) => {
        const DEFAULT_WIDTH = NODE_DEFAULTS.WIDTH
        const DEFAULT_MIN_HEIGHT = NODE_DEFAULTS.MIN_HEIGHT
        const DEFAULT_MAX_HEIGHT = NODE_DEFAULTS.MAX_HEIGHT
        // 子节点
        if (pids && pids.length > 0) {
            const nodes = useFlowStore.getState().nodes.filter(node => pids.includes(node.id))
            if (nodes.length === 1) {
                const node = useFlowStore.getState().nodes.find(node => node.id === nid)
                return {
                    x: nodes[0].position.x + (nodes[0].measured?.width ?? 0) + 30,
                    y: nodes[0].position.y + (nodes[0].measured?.height ?? 0) / 2 - (node?.measured?.height ?? 0) / 2,
                }
            }
            // 计算所有父节点的中心点
            const centerY = nodes.reduce((sum, node) => sum + node.position.y + (node.measured?.height ?? 0) / 2, 0) / nodes.length;

            // 找到最右边的父节点
            const rightmostNode = nodes.reduce((max, node) => {
                return node.position.x > max.position.x ? node : max;
            }, nodes[0]);

            return {
                x: rightmostNode.position.x + (rightmostNode.measured?.width ?? 0) + 80,
                y: centerY - DEFAULT_MIN_HEIGHT / 2,
            }
        }
        const nodes = useFlowStore.getState().nodes
        if (nodes.length === 0) {
            return {x: 0, y: 0}
        }
        // 找到最右边的节点
        const rightmostNode = nodes.reduce((max, node) => {
            return node.position.x > max.position.x ? node : max;
        }, nodes[0]);

        // 找到最右边一列的所有节点
        const rightColumn = nodes.filter(
            (node) => (rightmostNode.position.x - node.position.x) < DEFAULT_WIDTH
        );

        // 找出该列最上方和最下方的节点
        const {topNode, bottomNode} = rightColumn.reduce((res, node) => {
                return {
                    topNode: node.position.y < res.topNode.position.y ? node : res.topNode,
                    bottomNode: node.position.y > res.bottomNode.position.y ? node : res.bottomNode,
                }
            },
            {topNode: rightColumn[0], bottomNode: rightColumn[0]});

        // 计算该列占据的高度
        const columnHeight = bottomNode.position.y - topNode.position.y + (bottomNode.measured?.height || DEFAULT_MIN_HEIGHT);

        // 判断是否需要开始新的一列
        const shouldStartNewColumn = window.innerHeight < columnHeight + DEFAULT_MIN_HEIGHT

        return {
            x: shouldStartNewColumn ? rightmostNode.position.x + DEFAULT_WIDTH + 30 : rightmostNode.position.x,
            y: shouldStartNewColumn ? 0 : bottomNode.position.y + (bottomNode.measured?.height || DEFAULT_MAX_HEIGHT) + 15,
        };
    }, []);

    return {
        getLayoutedElements,
        onLayout,
        calculateNodePosition
    }
}

export {useNodeLayout}
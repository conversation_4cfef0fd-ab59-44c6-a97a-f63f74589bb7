import { useMemo } from 'react';
import { FileTreeNode } from '@/components/file-tree';
import { FileTreeItem } from './use-file-manager';

// 创建一个节流日志函数
const createThrottledLog = () => {
  let lastTime = 0;
  let lastMessage = '';
  
  return (message: string) => {
    const now = Date.now();
    // 500ms内相同消息只打印一次，或者消息变化时打印
    if (now - lastTime > 500 || message !== lastMessage) {
      console.log(message);
      lastTime = now;
      lastMessage = message;
    }
  };
};

// 创建一个全局的节流日志函数实例
const throttledLog = createThrottledLog();

export function useFileTree(
  folders: FileTreeItem[],
  files: FileTreeItem[],
  pdfs: Map<string, { aid: string; filename: string; url: string }>
) {
  // 将PDF数据和文件夹数据转换为FileTreePanel格式
  const fileTreeData = useMemo((): FileTreeNode[] => {
    // 使用节流日志，避免频繁输出
    throttledLog(`构建文件树 - 文件夹数量: ${folders.length} 文件数量: ${files.length}`);
    

    
    // 创建根节点和文件夹映射
    const folderMap = new Map<string, FileTreeNode>();
    
    // 确保有"所有文档"文件夹
    const allDocsFolder: FileTreeNode = {
      id: 'folder-all',
      title: '所有文档',
      isDirectory: true,
      path: '/所有文档',
      children: []
    };
    folderMap.set('root', allDocsFolder);
    
    // 添加用户创建的文件夹
    folders.forEach(folder => {
      const folderNode: FileTreeNode = {
        id: folder.id,
        title: folder.file_name,
        isDirectory: true,
        path: `/${folder.file_name}`, // 临时路径，后面会重新计算
        children: [],
        data: {
          isFolder: true,
          folderInfo: folder
        }
      };
      folderMap.set(folder.id, folderNode);
    });
    
    // 构建多级文件夹结构
    const buildFolderHierarchy = () => {
      const result: FileTreeNode[] = [];
      const processedFolders = new Set<string>();
      
      // 递归构建文件夹路径
      const buildFolderPath = (folderId: string): string => {
        const folder = folders.find(f => f.id === folderId);
        if (!folder || folder.parent_id === 'root') {
          return `/${folder?.file_name || ''}`;
        }
        
        const parentPath = buildFolderPath(folder.parent_id);
        return `${parentPath}/${folder.file_name}`;
      };
      
      // 递归构建文件夹树
      const buildFolderTree = (folderId: string): string[] => {
        const children: string[] = [];
        
        // 查找所有直接子文件夹
        folders.forEach(folder => {
          if (folder.parent_id === folderId) {
            children.push(folder.id);
            // 递归处理子文件夹
            const subChildren = buildFolderTree(folder.id);
            const folderNode = folderMap.get(folder.id);
            if (folderNode) {
              // 避免重复添加子项
              const existingChildren = folderNode.children || [];
              const newChildren = subChildren.filter(childId => !existingChildren.includes(childId));
              folderNode.children = [...existingChildren, ...newChildren];
            }
          }
        });
        
        return children;
      };
      
      // 处理所有文件夹
      folders.forEach(folder => {
        if (processedFolders.has(folder.id)) return;
        
        // 更新文件夹路径
        const folderNode = folderMap.get(folder.id);
        if (folderNode) {
          folderNode.path = buildFolderPath(folder.id);
        }
        
        // 如果是顶级文件夹，添加到结果中
        if (folder.parent_id === 'root') {
          result.push(folderNode!);
        }
        
        // 为所有文件夹构建子树（无论是否为顶级）
        const children = buildFolderTree(folder.id);
        // 避免重复添加子项
        const existingChildren = folderNode!.children || [];
        const newChildren = children.filter(childId => !existingChildren.includes(childId));
        folderNode!.children = [...existingChildren, ...newChildren];
        
        processedFolders.add(folder.id);
      });
      
      return result;
    };
    
    const result = buildFolderHierarchy();
    
    // 如果没有用户创建的文件夹，添加"所有文档"文件夹
    if (result.length === 0) {
      result.push(allDocsFolder);
    } else if (!result.some(item => item.id === 'folder-all')) {
      // 确保"所有文档"文件夹始终存在
      result.push(allDocsFolder);
    }
    
    // 处理所有文件，将它们放入对应的文件夹
    files.forEach(file => {
      // 构建文件路径
      const buildFilePath = (fileId: string): string => {
        const fileItem = files.find(f => f.id === fileId);
        if (!fileItem) return '';
        
        if (fileItem.parent_id === 'root') {
          return `/所有文档/${fileItem.file_name}`;
        }
        
        // 查找父文件夹路径
        const parentFolder = folders.find(f => f.id === fileItem.parent_id);
        if (!parentFolder) {
          return `/所有文档/${fileItem.file_name}`;
        }
        
        // 递归构建父文件夹路径
        const buildParentPath = (folderId: string): string => {
          const folder = folders.find(f => f.id === folderId);
          if (!folder || folder.parent_id === 'root') {
            return `/${folder?.file_name || '所有文档'}`;
          }
          
          const parentPath = buildParentPath(folder.parent_id);
          return `${parentPath}/${folder.file_name}`;
        };
        
        const parentPath = buildParentPath(fileItem.parent_id);
        return `${parentPath}/${fileItem.file_name}`;
      };
      
      // 创建文件节点
      const fileNode: FileTreeNode = {
        id: file.id,
        title: file.file_name,
        isDirectory: false,
        path: buildFilePath(file.id),
        size: file.size || 0,
        lastModified: new Date(file.create_at * 1000),
        data: {
          isFile: true,
          fileInfo: file,
          isPdf: file.mime_type === 'application/pdf',
          pdfInfo: {
            aid: file.id,
            filename: file.file_name,
            url: file.file_path
          }
        }
      };
      
      // 将文件添加到文件夹映射中，以便其他节点可以引用
      folderMap.set(file.id, fileNode);
      
      // 找到对应的父文件夹
      const parentId = file.parent_id || 'root';
      const parentFolder = folderMap.get(parentId);
      
      if (parentFolder) {
        // 添加文件到文件夹的children数组中
        parentFolder.children = [...(parentFolder.children || []), file.id];
      } else {
        console.warn(`找不到文件 ${file.file_name} 的父文件夹 ${parentId}`);
        // 如果找不到父文件夹，添加到"所有文档"
        const defaultFolder = folderMap.get('root');
        if (defaultFolder) {
          defaultFolder.children = [...(defaultFolder.children || []), file.id];
        }
      }
    });
    
    // 处理内存中的PDF文件
    // if (pdfs.size > 0) {
    //   const pdfArray = Array.from(pdfs.values());
      
    //   pdfArray.forEach(pdf => {
    //     // 检查文件是否已经存在于文件列表中
    //     const existsInDB = files.some(file => file.id === pdf.aid);
        
    //     if (!existsInDB) {
    //       const pdfNode: FileTreeNode = {
    //         id: pdf.aid,
    //         title: pdf.filename,
    //         isDirectory: false,
    //         path: `/所有文档/${pdf.filename}`,
    //         size: 0,
    //         lastModified: new Date(),
    //         data: {
    //           isPdf: true,
    //           pdfInfo: pdf,
    //           isMemoryOnly: true
    //         }
    //       };
          
    //       // 将PDF添加到文件夹映射中
    //       folderMap.set(pdf.aid, pdfNode);
          
    //       // 将内存中的PDF添加到"所有文档"文件夹
    //       const defaultFolder = folderMap.get('root');
    //       if (defaultFolder) {
    //         defaultFolder.children = [...(defaultFolder.children || []), pdf.aid];
    //       }
    //     }
    //   });
    // }
    
    // 将所有文件夹映射的值转换为数组返回
    return result.concat(Array.from(folderMap.values()).filter(node => {
      // 排除已经添加到结果中的顶级节点
      return !result.some(item => item.id === node.id);
    }));
  }, [folders, files]);

  return fileTreeData;
} 
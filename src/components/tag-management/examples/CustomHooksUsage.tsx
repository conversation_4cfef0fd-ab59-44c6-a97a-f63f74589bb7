import React from 'react';
import { 
  useTagGroups, 
  useTagOperations, 
  useDragAndDrop 
} from '../index';

/**
 * 自定义 Hook 使用示例
 * 
 * 展示如何使用分离后的各个 Hook 来构建自定义界面
 */
export const CustomHooksUsageExample: React.FC = () => {
  // 获取数据
  const tagGroups = useTagGroups();
  const groupsCount = tagGroups.length;
  const totalTagsCount = tagGroups.reduce((count, group) => count + group.tags.length, 0);
  
  // 获取操作方法
  const operations = useTagOperations();
  
  // 拖拽状态
  const { isDragging, draggedItem } = useDragAndDrop({
    enableCrossGroupMove: true,
    showDropIndicators: true,
  });
  
  const handleQuickAddGroup = async () => {
    try {
      await operations.createGroup(`新分组 ${groupsCount + 1}`);
    } catch (error) {
      console.error('创建分组失败:', error);
    }
  };
  
  const handleClearAllGroups = async () => {
    for (const group of tagGroups) {
      await operations.deleteGroup(group.id);
    }
  };
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">自定义标签管理界面</h1>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-100 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{groupsCount}</div>
            <div className="text-sm text-blue-500">分组数量</div>
          </div>
          <div className="bg-green-100 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">{totalTagsCount}</div>
            <div className="text-sm text-green-500">标签总数</div>
          </div>
          <div className="bg-orange-100 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">
              {isDragging ? '拖拽中' : '就绪'}
            </div>
            <div className="text-sm text-orange-500">状态</div>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex gap-2 mb-6">
          <button
            onClick={handleQuickAddGroup}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            快速添加分组
          </button>
          <button
            onClick={handleClearAllGroups}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            disabled={groupsCount === 0}
          >
            清空所有分组
          </button>
        </div>
      </div>
      
      {/* 拖拽状态显示 */}
      {isDragging && draggedItem && (
        <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
          <div className="font-medium">正在拖拽：</div>
          <div className="text-sm text-gray-600">
            {draggedItem.type === 'tag' ? '标签' : '分组'}: {draggedItem.data.name || draggedItem.data.title}
          </div>
        </div>
      )}
      
      {/* 分组列表 */}
      <div className="space-y-4">
        {tagGroups.map((group) => (
          <div 
            key={group.id} 
            className="border rounded-lg p-4 bg-white shadow-sm"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-lg">{group.title}</h3>
              <span className="text-sm text-gray-500">
                {group.tags.length} 个标签
              </span>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {group.tags.map((tag: any) => (
                <span
                  key={tag.id}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                >
                  {tag.name}
                </span>
              ))}
              {group.tags.length === 0 && (
                <span className="text-gray-400 italic">暂无标签</span>
              )}
            </div>
          </div>
        ))}
        
        {tagGroups.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            暂无分组，点击"快速添加分组"开始使用
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomHooksUsageExample;
import { useHighlightAdd } from "@/components/pdf/hooks/highlight-add.tsx";
import { CustomHighlight, usePdfStore } from "@/store/pdf-store";
import { useCallback } from "react";
import { insertText, TextInsertType, updateHighlight } from "@/api/pdf";
import { getWsId } from "@/tools/params";

export const usePdfInsertText = () => {

    const { addHighlights, state: pdfState } = usePdfStore((state) => ({
        addHighlights: state.addHighlights,
        state,
    }));

    // const { updateHighlights } = usePdfStore((state) => ({
    //     updateHighlights: state.updateHighlights
    // }))

    const onInsertText = useCallback(async (
        aid: string, 
        inserData: TextInsertType, 
        highlight: any
    ) => {

        if (highlight.type === "text-insert" && highlight.content.text?.trim().length === 0) {
            return
        }

        const hs = pdfState.pdfs.get(aid)?.highlights || []
        console.log(hs, 'hs---------')
        // const wid = getWsId()

        // 存入数据库
        const response = await insertText(inserData);

        const customHighlight:CustomHighlight = {
            id: response.data.mid,
            ...highlight
        }

        // // 高亮
        addHighlights(highlight.aid, [
            customHighlight
        ])
        // TODO: 插入文本后，需要更新高亮区域
        // 1. 获取高亮区域
    }, [addHighlights])

    return {
        onInsertText
    }
}

export const usePdfUpdateText = () => {

    const { updateHighlights } = usePdfStore((state) => ({
        updateHighlights: state.updateHighlights
    }))

    const onUpdateText = useCallback(async (highlight: CustomHighlight) => {

        const wid = getWsId()

        // 创建包含完整信息的mark对象
        const completeMarkInfo = {
            ...highlight.position,
            type: highlight.type,
            content: highlight.content
        }

        updateHighlight({
            wid: wid,
            mid: highlight.id,
            mark: JSON.stringify(completeMarkInfo),
            color: highlight.color.slice(1)
        })

        // 高亮
        updateHighlights(highlight.aid, {
            id: highlight.id,
            position: highlight.position,
            content: highlight.content
        })

    }, [updateHighlights])

    return {
        onUpdateText
    }
}
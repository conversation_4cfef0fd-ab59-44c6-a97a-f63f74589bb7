import {shallow} from "zustand/shallow";
import {createWithEqualityFn} from "zustand/traditional";

export type NoteState = {
    content: string,
    insertContent: string, // 插入内容
    setContent: (content: string) => void
    setInsertContent: (content: string) => void
}

export const useNoteStore = createWithEqualityFn<NoteState>((set) => ({
    content: '',
    insertContent: '',
    setContent: (content) => set(() => ({content})),
    setInsertContent: (content) => set(() => ({insertContent: content})),
}), shallow)

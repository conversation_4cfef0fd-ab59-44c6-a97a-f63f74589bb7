import {useCallback} from "react";
import {updateNode} from "@/api/canvas";
import {useFlowStore} from "@/store/flow-store.ts";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatResponseType } from "@/components/chat-panel/types";

const useNodeEdit = () => {
    const {updateNodes} = useFlowStore((state) => ({
        updateNodes: state.updateNodes
    }))
    const {updateFooterRef, updateCurrentChat, updateChatList}= useChatStore();
    
    const onNodeEdit = useCallback(async ({id, title, content}: {
        id: string,
        title?: string,
        content?: string
    }) => {
        if (typeof title === "undefined" && typeof content === "undefined") {
            return
        }
        try {
            let reqData, nodeData
            if (typeof title !== "undefined" && typeof content !== "undefined") {
                reqData = {type: 4, nid: id, title, content}
                nodeData = {title, content}
            } else if (typeof title !== "undefined") {
                reqData = {type: 1, nid: id, title}
                nodeData = {title}
            } else {
                reqData = {type: 2, nid: id, content: content}
                nodeData = {content}
            }
            // 接口
            await updateNode(reqData)
            // 更新节点
            updateNodes([{id: id, data: nodeData}])

            const currentChatRid = useChatStore.getState().currentChatRid
            const updateTitle = `${title || '未命名'}.node`
            const updaupChatRef = {
                title: updateTitle,
                id,
            }
            if (currentChatRid === -1) {
                if (reqData?.hasOwnProperty?.('title')){
                    updateFooterRef({
                        ...updaupChatRef
                    })
                }
            } else {
 
                const currentChat = useChatStore.getState().currentChat
                const updateCurrentRefs = currentChat?.refs?.map((ref) => {
                    if (ref.id === id) {
                        return {
                            ...ref,
                            title: updateTitle,
                        }
                    }
                    return ref
                }) || [] 
                
                const updateCurChat = {
                    ...currentChat,
                    refs: updateCurrentRefs
                } as ChatResponseType

                updateCurrentChat(updateCurChat)
                updateChatList(updateCurChat)
            }

            
            
        } catch (e) {
            console.log(e)
        }
    }, [updateNodes, updateFooterRef])

    return {
        onNodeEdit
    }
}

export default useNodeEdit
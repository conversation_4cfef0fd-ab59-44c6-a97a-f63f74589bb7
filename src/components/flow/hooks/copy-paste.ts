import { useCallback, useState, useRef } from 'react';
import { useFlowStore, CustomNode } from '@/store/flow-store.ts';
import { Edge } from '@xyflow/react';
import { createNode } from '@/api/canvas';
import { getWsId } from '@/tools/params.ts';
import { useNodeDelete } from './node-delete';

interface ClipboardData {
  nodes: CustomNode[];
  edges: Edge[];
}

export const useCopyPaste = () => {
  const { 
    nodes, 
    edges, 
    addNodes, 
    addEdges, 
    // deleteNodes,
    // setEdges,
    updateNodes
  } = useFlowStore();
  const { handleDeleteNode } = useNodeDelete()
  
  const [clipboard, setClipboard] = useState<ClipboardData | null>(null);
  const pasteCountRef = useRef(0);

  // 复制选中的节点和边
  const copy = useCallback(() => {
    // 获取选中的节点
    const selectedNodes = nodes.filter(node => node.selected);
    
    if (selectedNodes.length === 0) {
      console.log('没有选中的节点可复制');
      return false;
    }

    const selectedNodeIds = selectedNodes.map(node => node.id);
    
    // 获取选中节点之间的边
    const selectedEdges = edges.filter(edge => 
      selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target)
    );

    // 深拷贝节点和边，避免引用问题
    const copiedNodes = selectedNodes.map(node => ({
      ...node,
      selected: false,
    }));

    const copiedEdges = selectedEdges.map(edge => ({ ...edge }));

    setClipboard({
      nodes: copiedNodes,
      edges: copiedEdges
    });

    console.log(`已复制 ${copiedNodes.length} 个节点和 ${copiedEdges.length} 条边`);
    return true;
  }, [nodes, edges]);

  // 粘贴节点和边
  const paste = useCallback(async (targetPosition?: { x: number; y: number }) => {
    if (!clipboard || clipboard.nodes.length === 0) {
      console.log('剪贴板为空，无法粘贴');
      return false;
    }

    try {
      pasteCountRef.current += 1;
      const pasteOffset = pasteCountRef.current * 20; // 每次粘贴增加偏移量

      // 计算粘贴位置
      let offsetX = pasteOffset;
      let offsetY = pasteOffset;

      if (targetPosition && clipboard.nodes.length > 0) {
        // 如果指定了目标位置，计算相对于第一个节点的偏移
        const firstNode = clipboard.nodes[0];
        offsetX = targetPosition.x - firstNode.position.x;
        offsetY = targetPosition.y - firstNode.position.y;
      }

      // 取消所有现有节点的选中状态
      updateNodes(
        nodes.map(node => ({ id: node.id, selected: false }))
      );

      // 创建ID映射表（旧ID -> 新ID）
      const idMapping = new Map<string, string>();
      const newNodes: CustomNode[] = [];

      // 通过API创建新节点
      for (const node of clipboard.nodes) {
        const newPosition = {
          x: node.position.x + offsetX,
          y: node.position.y + offsetY,
        };

        // 准备节点数据
        const nodeData = {
          wid: getWsId(),
          content: node.data.content || '',
          title: node.data.title || '',
          color: node.data.color?.replace('#', '') || 'FFE28F',
          x: newPosition.x,
          y: newPosition.y,
          type: 'default' as const,
          pids: [] // 复制的节点不保持原有的父子关系
        };

        try {
          // 调用API创建节点
          const res = await createNode(nodeData);
          const newNodeId = (res.data as any).nid;
          
          // 建立ID映射关系
          idMapping.set(node.id, newNodeId);

          // 创建新节点对象
          const newNode: CustomNode = {
            ...node,
            id: newNodeId,
            position: newPosition,
            selected: true, // 新粘贴的节点设为选中状态
          };

          newNodes.push(newNode);
        } catch (error) {
          console.error('创建节点失败:', error);
          // 如果某个节点创建失败，继续创建其他节点
        }
      }

      // 创建新边（只有当相关的源节点和目标节点都成功创建时）
      const newEdges: Edge[] = [];
      for (const edge of clipboard.edges) {
        const newSourceId = idMapping.get(edge.source);
        const newTargetId = idMapping.get(edge.target);
        
        if (newSourceId && newTargetId) {
          const newEdge = {
            ...edge,
            id: `${newSourceId}-${newTargetId}`,
            source: newSourceId,
            target: newTargetId,
          };
          newEdges.push(newEdge);
        }
      }

      // 添加节点和边到store
      addNodes(newNodes);
      addEdges(newEdges);

      console.log(`已粘贴 ${newNodes.length} 个节点和 ${newEdges.length} 条边`);
      return true;
    } catch (error) {
      console.error('粘贴操作失败:', error);
      return false;
    }
  }, [clipboard, nodes, updateNodes, addNodes, addEdges]);

  // 剪切功能（复制后删除原节点）
  const cut = useCallback(() => {
    const copied = copy();
    if (copied) {
      // 删除选中的节点
      const selectedNodeIds = nodes.filter(node => node.selected).map(node => node.id);
      // deleteNodes(selectedNodeIds);
      
      console.log(`已剪切 ${selectedNodeIds.length} 个节点`);
    }
  }, [copy, nodes]);

  // 删除选中节点
  const deleteSelected = useCallback(() => {
    const selectedNodeIds = nodes.filter(node => node.selected).map(node => node.id);
    
    if (selectedNodeIds.length === 0) {
      console.log('没有选中的节点可删除');
      return false;
    }

    // deleteNodes(selectedNodeIds);
    handleDeleteNode(selectedNodeIds)
    console.log(`已删除 ${selectedNodeIds.length} 个节点`);
    return true;
  }, [nodes, handleDeleteNode]);

  // 全选节点
  const selectAll = useCallback(() => {
    if (nodes.length === 0) {
      console.log('没有节点可选择');
      return false;
    }

    updateNodes(
      nodes.map(node => ({ id: node.id, selected: true }))
    );
    
    console.log(`已选择 ${nodes.length} 个节点`);
    return true;
  }, [nodes, updateNodes]);

  return {
    copy,
    paste,
    cut,
    deleteSelected,
    selectAll,
    hasClipboard: !!clipboard && clipboard.nodes.length > 0,
    clipboardCount: clipboard?.nodes.length || 0,
  };
}; 
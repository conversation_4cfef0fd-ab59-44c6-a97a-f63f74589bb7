import {RxJsonSchema} from 'rxdb';

export interface MarkDocType {
    id: string;        // 标记ID
    attach_id: string; // 附件ID
    node_id?: string;  // 节点ID（可选）
    wid: string;      // 工作区ID
    mark: string;      // 标记内容（JSON字符串）
    color: string;     // 标记颜色
    mark_type?: string; // 标记类型（可选，如"text-insert"、"area"等），改名避免与RxDB内部字段冲突
    content?: string;  // 标记内容（可选，如文本插入的文本内容）
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const markSchema: RxJsonSchema<MarkDocType> = {
    title: 'marks schema',
    description: 'describes a mark',
    version: 0,
    keyCompression: false,
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        attach_id: {
            type: 'string',
            maxLength: 100
        },
        node_id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        mark: {
            type: 'string'
        },
        color: {
            type: 'string',
        },
        mark_type: {
            type: 'string',
            maxLength: 50
        },
        content: {
            type: 'string'
        },
        create_at: {
            type: 'number'
        },
        update_at: {
            type: 'number'
        }
    },
    required: ['id', 'attach_id', 'wid', 'mark', 'color'],
    indexes: ['attach_id', 'wid']
}; 
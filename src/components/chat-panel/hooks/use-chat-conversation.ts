import {message} from "antd";
import {useChatStore} from "@/store/workerspace-store/chat-store";
import {useWorkerSpaceStore} from "@/store/workerspace-store/store";
import {type ConversationType, createSession} from "@/api/chat";
import {SSEClient} from "@/tools/sse-client";
import {ChatResponseRefs, ChatResponseType} from '@/components/chat-panel/types'
import {sessionService} from "@/local";
import { useState } from "react";
// import config from "@/store/global"

export const useChatConversation = () => {
  const { wid } = useWorkerSpaceStore();
  const {
    footerRefs,
    sessionId,
    currentChat,
    chatList,
    currentChatRid,
    session_detail_list,
    cleanResponseStream,
    addSession,
    setSessionId,
    setResponseStream,
    setIsFetchStream,
    setSseClient,
    setCurrentChat,
    addChat,
    setCurrentChatRid,
    deleteChatCurrentWIdthAfter,
    setUserInput,
    setSessionDetailList,
    updateSessionDetail,
    addSessionDetail,
    setFooterRefs,
    setCurrentQuestionInStream,
  } = useChatStore();

  const buildRefs = (source: any, type: 'footer' | 'chat'): ConversationType['Refs'] => {
    if (!source || (Array.isArray(source) && source.length === 0)) return [];

    return (type === 'footer' ? source : source.refs)?.map((item: any) => ({
      type: item.type,
      id: item.id,
      path: item.type === 1 ? item.url : "",
      title: item.title,
      content: item.content,
      page: item.page
    })) || [];
  };

  // 构建聊天响应对象
  const buildChatResponse = (question: string, answer: string, rid: number, refs: any[]): ChatResponseType => {
    return {
      rid,
      question,
      answer,
      refs: refs.map(item => ({
        type: item.type,
        id: item.id,
        title: item.title,
        content: item.content,
        page: item.page
      })) as ChatResponseRefs
    };
  };
  // 更新会话详情
  const updateSessionDetails = (chat: ChatResponseType, sid?: string, reloadRid?: number) => {
    const currentSessionDetail = session_detail_list.find(item => item.sid === sid);
    console.log(currentSessionDetail, reloadRid, currentChat)
    if (currentSessionDetail) {
      if (!reloadRid && !currentChat) {
        currentSessionDetail.chatList.push(chat);
        updateSessionDetail(currentSessionDetail);
      } else if (reloadRid || currentChat) {

        // 更新截断的聊天记录
        const tempChatIndex = currentSessionDetail.chatList.findIndex(item => item.rid === reloadRid || item.rid === currentChat?.rid)
        const beforeChat = currentSessionDetail.chatList.slice(0, tempChatIndex);
        currentSessionDetail.chatList = [...beforeChat, chat];
        console.log(currentSessionDetail)
        updateSessionDetail(currentSessionDetail);
      }
    } else {
      const newSessionDetail = {
        sid: sid as string,
        chatList: [chat]
      }
      addSessionDetail(newSessionDetail);
    }
  };

  // 处理SSE完成回调
  const handleSSEComplete = (data: string, body: ConversationType, reloadRid?: number) => {
    setIsFetchStream(false);
    setCurrentChatRid(-1);
    let chat: ChatResponseType;
    const currentChat = useChatStore.getState().currentChat;
    const chatList = useChatStore.getState().chatList;
    const refs = body.Refs || []
    console.log("handleSSEComplete", body, currentChat, reloadRid, chatList)

    if (!currentChat && !reloadRid) {
      const lastRid = chatList.length > 0 ? chatList[chatList.length - 1].rid : 0;
      console.log("handleSSEComplete-lastRid", lastRid)
      chat = buildChatResponse(body.question, data, Number(lastRid) + 1, refs);
    } else if (currentChat) {
      chat = buildChatResponse(body.question, data, Number(currentChat.rid), refs);
    } else if (reloadRid) {
      const reloadChat = chatList.find(item => item.rid === reloadRid);
      if (!reloadChat) return;
      chat = buildChatResponse(body.question, data, Number(reloadRid), reloadChat.refs || []);
    } else {
      return;
    }
    console.log("handleSSEComplete-chat", chat)
    addChat([chat]);
    updateSessionDetails(chat, body.sid as string, reloadRid);
    cleanResponseStream();
    console.log("handleSSEComplete-updateSessionDetails", useChatStore.getState().chatList)
    //保存会话
    sessionService.saveChat({
      wid: body.wid,
      sid: body.sid,
      refs: body.Refs ? body.Refs.map((ref: any) => ({
        type: ref.type,
        id: ref.id,
        title: ref.title,
        content: ref.content,
        page: ref.page
      })) : [],
      question: body.question,
      answer: data,
      rid: chat.rid
    })
  };

  // 启动SSE连接
  const startSSEConnection = (body: ConversationType, reloadRid?: number) => {
    // 统一使用外部 OpenAI API
    const aiChatUrl = 'https://api.chatanywhere.tech/v1/chat/completions';
    const newSSEClient = new SSEClient(aiChatUrl as any);
    setSseClient(newSSEClient);

    newSSEClient.start(
      body,
      (data) => setResponseStream(data),
      (error) => {
        // const errorChatIndex = chatList.findIndex((chat) => chat.rid === body.rid);
        // if (errorChatIndex === 0) {
        //   cleanSession();
        // }

        cleanResponseStream();
        setIsFetchStream(false);
        setCurrentChatRid(-1);
        setCurrentChat(null);
      }
    );

    newSSEClient.onComplete = (data: string) => handleSSEComplete(data, body, reloadRid);
  };
  // 获取引用数据
  const getRefs = (reloadRid?: number): ConversationType['Refs'] => {
    const sessionId = useChatStore.getState().sessionId;
    const currentChatRid = useChatStore.getState().currentChatRid;
    const currentChat = useChatStore.getState().currentChat;
    const chatList = useChatStore.getState().chatList;
    console.log("getRefs", sessionId, currentChatRid, currentChat, chatList, reloadRid)
    if (sessionId === 0 || currentChatRid === -1) {
      return buildRefs(footerRefs, 'footer');
    }
    if (currentChat) {
      return buildRefs(currentChat, 'chat');
    }
    if (reloadRid) {
      const reloadChat = chatList.find(item => item.rid === reloadRid);
      if (reloadChat) return buildRefs(reloadChat, 'chat');
    }
    return [];
  };

  // 创建新会话
  const createNewSession = async (question: string, Refs: ConversationType['Refs']) => {
    const res = await createSession({ wid: wid!, question });
    if ((res as any).code === 0) {
      const newSessionId = (res as any).data.sid;
      setSessionId(newSessionId);

      const body: ConversationType = {
        wid: wid as string,
        sid: newSessionId,
        question,
        Refs,
      };

      setCurrentQuestionInStream(body)

      startSSEConnection(body);
      addSession({
        sid: newSessionId,
        title: question,
        datetime: new Date().getTime() / 1000,
      });
    }
  };

  // 处理现有会话
  const handleExistingSession = (
    question: string,
    Refs: ConversationType['Refs'],
    reloadRid?: number
  ) => {
    const currentChat = useChatStore.getState().currentChat;
    const body: ConversationType = {
      wid: wid as string,
      sid: sessionId as string,
      question,
      Refs,
      rid: currentChat ? Number(currentChat.rid) : reloadRid,
    };
    setCurrentQuestionInStream(body)
    console.log("handleExistingSession", currentChat, reloadRid)
    if (currentChat || reloadRid) {
      const rid = currentChat?.rid || reloadRid;
      if (rid) {
        deleteChatCurrentWIdthAfter(Number(rid));
        const oldSessionDetail = session_detail_list.slice(0, Number(rid));
        if (oldSessionDetail) setSessionDetailList(oldSessionDetail);
      }
    }

    if (reloadRid) setUserInput(question);
    startSSEConnection(body, reloadRid);
  };

  // 开始对话
  const onStartConversation = async (question: string, reloadRid?: number) => {
    setIsFetchStream(true);
    // 清除底部引用
    setFooterRefs([]);
    try {
      const Refs = getRefs(reloadRid);

      if (sessionId === 0) {
        await createNewSession(question, Refs);
      } else {
        handleExistingSession(question, Refs, reloadRid);
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : "会话创建失败");
    }
  };

  return {
    onStartConversation,
  };
}; 
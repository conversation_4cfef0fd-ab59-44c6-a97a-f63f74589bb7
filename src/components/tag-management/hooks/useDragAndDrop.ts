import { useState, useEffect, useCallback } from 'react';
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { extractClosestEdge } from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge';
import { reorderWithEdge } from '@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge';
import { triggerPostMoveFlash } from '@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash';
import { flushSync } from 'react-dom';
import { useTagOperations } from './useTagOperations';
import { useTagStore } from '../stores/useTagStore';
import { TagGroupData } from '../mock-data';

// 拖拽项类型
export interface DragItem {
  id: string;
  type: 'tag' | 'group';
  data: any;
}

// 拖拽状态
export interface DragState {
  isDragging: boolean;
  draggedItem: DragItem | null;
  dropTarget: {
    id: string;
    type: 'group' | 'tag';
    data: any;
  } | null;
  canDrop: boolean;
}

// 拖拽事件处理器
export interface DragHandlers {
  onDragStart: (item: DragItem) => void;
  onDragEnd: () => void;
  onDrop: (source: DragItem, target: any) => void;
}

// 拖拽配置
export interface DragConfig {
  /** 是否启用分组重排序功能 - 允许拖拽分组来改变它们的顺序 */
  enableGroupReordering?: boolean;
  /** 是否启用标签重排序功能 - 允许在同分组内拖拽标签来改变它们的顺序 */
  enableTagReordering?: boolean;
  /** 是否启用跨分组移动功能 - 允许将标签从一个分组拖拽到另一个分组 */
  enableCrossGroupMove?: boolean;
  /** 是否显示放置指示器 - 在拖拽过程中显示视觉反馈，指示可以放置的位置 */
  showDropIndicators?: boolean;
  /** 是否启用触觉反馈 - 在移动设备上拖拽时提供振动反馈 */
  hapticFeedback?: boolean;
}

const defaultConfig: DragConfig = {
  enableGroupReordering: true,
  enableTagReordering: true,
  enableCrossGroupMove: true,
  showDropIndicators: true,
  hapticFeedback: false,
};

export const useDragAndDrop = (config: Partial<DragConfig> = {}) => {
  const mergedConfig = { ...defaultConfig, ...config };
  const tagOperations = useTagOperations();
  
  // 获取 store 数据和方法
  const tagGroups = useTagStore(state => state.tagGroups);
  const setTagGroups = useTagStore(state => state.setTagGroups);
  
  // 拖拽状态
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    dropTarget: null,
    canDrop: false,
  });
  
  // 触觉反馈（移动端）
  const triggerHapticFeedback = useCallback(() => {
    if (mergedConfig.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }
  }, [mergedConfig.hapticFeedback]);
  
  // 检查是否可以放置
  const canDropOn = useCallback((source: DragItem, target: any): boolean => {
    // 标签拖拽到分组
    if (source.type === 'tag' && target.type === 'group') {
      if (!mergedConfig.enableCrossGroupMove) return false;
      // 不能拖拽到自己当前所在的分组
      return source.data.groupId !== target.groupId;
    }
    
    // 分组重排序
    if (source.type === 'group' && target.type === 'group') {
      if (!mergedConfig.enableGroupReordering) return false;
      return source.id !== target.id;
    }
    
    // 标签重排序（同分组内）
    if (source.type === 'tag' && target.type === 'tag') {
      if (!mergedConfig.enableTagReordering) return false;
      return source.id !== target.id && source.data.groupId === target.data.groupId;
    }
    
    return false;
  }, [mergedConfig]);
  
  // 拖拽开始处理
  const handleDragStart = useCallback((source: any) => {
    const dragItem: DragItem = {
      id: source.data.id as string,
      type: source.data.type as 'tag' | 'group',
      data: source.data,
    };
    
    setDragState(prev => ({
      ...prev,
      isDragging: true,
      draggedItem: dragItem,
      dropTarget: null,
      canDrop: false,
    }));
    
    triggerHapticFeedback();
  }, [triggerHapticFeedback]);
  
  // 拖拽悬停处理
  const handleDragOver = useCallback((source: any, target: any) => {
    if (!target) {
      setDragState(prev => ({
        ...prev,
        dropTarget: null,
        canDrop: false,
      }));
      return;
    }
    
    const dragItem: DragItem = {
      id: source.data.id as string,
      type: source.data.type as 'tag' | 'group',
      data: source.data,
    };
    
    const dropTarget = {
      id: target.data.groupId || target.data.id,
      type: target.data.type as 'group' | 'tag',
      data: target.data,
    };
    
    const canDrop = canDropOn(dragItem, dropTarget);
    
    setDragState(prev => ({
      ...prev,
      dropTarget,
      canDrop,
    }));
  }, [canDropOn]);
  
  // 拖拽放置处理
  const handleDrop = useCallback(async (source: any, location: any) => {
    const targetData = location.current.dropTargets[0]?.data;
    
    if (!targetData || !dragState.draggedItem) {
      setDragState(prev => ({
        ...prev,
        isDragging: false,
        draggedItem: null,
        dropTarget: null,
        canDrop: false,
      }));
      return;
    }
    
    const sourceData = source.data;
    
    try {
      // 处理标签移动到分组
      if (sourceData.type === 'tag' && targetData.type === 'group') {
        await tagOperations.moveTagToGroup(sourceData.id, targetData.groupId);
        triggerHapticFeedback();
        
        // 触发移动后的视觉反馈
        const element = document.querySelector(`[data-tag-id="${sourceData.id}"]`);
        if (element instanceof HTMLElement) {
          triggerPostMoveFlash(element);
        }
      }
      
      // 处理分组重排序
      else if (sourceData.type === 'group' && targetData.type === 'group') {
        if (!mergedConfig.enableGroupReordering) return;
        
        // 获取最近的边缘信息
        const closestEdge = extractClosestEdge(targetData);
        
        // 使用当前的分组数据
        const sourceIndex = tagGroups.findIndex((group: TagGroupData) => group.id === sourceData.id);
        const targetIndex = tagGroups.findIndex((group: TagGroupData) => group.id === targetData.id);
        
        if (sourceIndex < 0 || targetIndex < 0) return;
        
        // 计算重排序后的数据（先计算，用于乐观更新和持久化）
        const reorderedGroups = reorderWithEdge({
          list: tagGroups,
          startIndex: sourceIndex,
          indexOfTarget: targetIndex,
          closestEdgeOfTarget: closestEdge,
          axis: 'vertical',
        });
        
        // 乐观更新：先更新 UI
        flushSync(() => {
          setTagGroups(reorderedGroups);
        });
        
        // 持久化排序到数据库
        try {
          const groupIds = reorderedGroups.map((group: TagGroupData) => group.id);
          await tagOperations.reorderGroups(groupIds);
        } catch (error) {
          console.error('持久化分组排序失败:', error);
          // 回滚 UI 状态
          setTagGroups(tagGroups);
        }
        
        triggerHapticFeedback();
        
        // 触发移动后的视觉反馈
        const element = document.querySelector(`[data-group-id="${sourceData.id}"]`);
        if (element instanceof HTMLElement) {
          triggerPostMoveFlash(element);
        }
      }
      
      // 处理标签重排序（同分组内）
      else if (sourceData.type === 'tag' && targetData.type === 'tag') {
        if (!mergedConfig.enableTagReordering) return;
        
        // 确保两个标签在同一个分组内
        if (sourceData.groupId !== targetData.groupId) return;
        
        // 获取最近的边缘信息
        const closestEdge = extractClosestEdge(targetData);
        
        // 获取该分组内的所有标签
        const currentGroup = tagGroups.find((group: TagGroupData) => group.id === sourceData.groupId);
        if (!currentGroup) return;
        
        const tagsInGroup = currentGroup.tags;
        const sourceIndex = tagsInGroup.findIndex((tag: any) => tag.id === sourceData.id);
        const targetIndex = tagsInGroup.findIndex((tag: any) => tag.id === targetData.id);
        
        if (sourceIndex < 0 || targetIndex < 0) return;
        
        // 计算重排序后的标签（先计算，用于乐观更新和持久化）
        const reorderedTags = reorderWithEdge({
          list: tagsInGroup,
          startIndex: sourceIndex,
          indexOfTarget: targetIndex,
          closestEdgeOfTarget: closestEdge,
          axis: 'vertical',
        });
        
        // 计算更新后的分组数据
        const updatedGroups = tagGroups.map((group: TagGroupData) => {
          if (group.id === sourceData.groupId) {
            return { ...group, tags: reorderedTags };
          }
          return group;
        });
        
        // 乐观更新：先更新 UI
        flushSync(() => {
          setTagGroups(updatedGroups);
        });
        
        // 持久化排序到数据库
        try {
          const tagIds = reorderedTags.map((tag: any) => tag.id);
          await tagOperations.reorderTags(sourceData.groupId, tagIds);
        } catch (error) {
          console.error('持久化标签排序失败:', error);
          // 回滚 UI 状态
          setTagGroups(tagGroups);
        }
        
        triggerHapticFeedback();
        
        // 触发移动后的视觉反馈
        const element = document.querySelector(`[data-tag-id="${sourceData.id}"]`);
        if (element instanceof HTMLElement) {
          triggerPostMoveFlash(element);
        }
      }
      
    } catch (error) {
      console.error('拖拽操作失败:', error);
    } finally {
      setDragState({
        isDragging: false,
        draggedItem: null,
        dropTarget: null,
        canDrop: false,
      });
    }
  }, [dragState.draggedItem, tagOperations, tagGroups, setTagGroups, mergedConfig]);
  
  // 拖拽结束处理
  const handleDragEnd = useCallback(() => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      dropTarget: null,
      canDrop: false,
    });
  }, []);
  
  // 设置全局拖拽监听器
  useEffect(() => {
    return monitorForElements({
      onDragStart: ({ source }) => {
        handleDragStart(source);
      },
      onDrop: ({ source, location }) => {
        handleDrop(source, location);
      },
    });
  }, [handleDragStart, handleDrop]);
  
  // 手动触发拖拽状态更新的方法
  const updateDragState = useCallback((updates: Partial<DragState>) => {
    setDragState(prev => ({ ...prev, ...updates }));
  }, []);
  
  // 重置拖拽状态
  const resetDragState = useCallback(() => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      dropTarget: null,
      canDrop: false,
    });
  }, []);
  
  // 检查特定元素是否正在被拖拽
  const isBeingDragged = useCallback((id: string, type: 'tag' | 'group') => {
    return dragState.isDragging && 
           dragState.draggedItem?.id === id && 
           dragState.draggedItem?.type === type;
  }, [dragState.isDragging, dragState.draggedItem]);
  
  // 检查特定元素是否是放置目标
  const isDropTarget = useCallback((id: string, type: 'tag' | 'group') => {
    return dragState.dropTarget?.id === id && dragState.dropTarget?.type === type;
  }, [dragState.dropTarget]);
  
  // 获取拖拽样式类名
  const getDragStyles = useCallback((id: string, type: 'tag' | 'group') => {
    const classes: string[] = [];
    
    if (isBeingDragged(id, type)) {
      classes.push('dragging', 'opacity-60', 'transform', 'rotate-2');
    }
    
    if (isDropTarget(id, type)) {
      if (dragState.canDrop) {
        classes.push('drop-target-valid', 'border-green-400', 'bg-green-50');
      } else {
        classes.push('drop-target-invalid', 'border-red-400', 'bg-red-50');
      }
    }
    
    return classes.join(' ');
  }, [isBeingDragged, isDropTarget, dragState.canDrop]);
  
  return {
    // 状态
    dragState,
    isDragging: dragState.isDragging,
    draggedItem: dragState.draggedItem,
    canDrop: dragState.canDrop,
    
    // 方法
    updateDragState,
    resetDragState,
    isBeingDragged,
    isDropTarget,
    getDragStyles,
    
    // 事件处理器
    handlers: {
      onDragStart: handleDragStart,
      onDragOver: handleDragOver,
      onDrop: handleDrop,
      onDragEnd: handleDragEnd,
    },
    
    // 配置
    config: mergedConfig,
  };
};

// 辅助 Hook：用于获取拖拽状态的样式
export const useDragStyles = (id: string, type: 'tag' | 'group') => {
  const { getDragStyles } = useDragAndDrop();
  return getDragStyles(id, type);
};

// 辅助 Hook：用于检查拖拽状态
export const useDragStatus = (id: string, type: 'tag' | 'group') => {
  const { isBeingDragged, isDropTarget, dragState } = useDragAndDrop();
  
  return {
    isDragging: isBeingDragged(id, type),
    isDropTarget: isDropTarget(id, type),
    canDrop: dragState.canDrop,
    globalDragActive: dragState.isDragging,
  };
};

/*
 * 实现说明：
 * 
 * 1. 分组重排序和标签重排序功能已实现，使用 @atlaskit 的实用工具：
 *    - extractClosestEdge: 获取拖拽目标的最近边缘
 *    - reorderWithEdge: 基于边缘位置重新排序列表
 *    - triggerPostMoveFlash: 添加移动后的视觉反馈
 *    - flushSync: 确保 DOM 同步更新
 * 
 * 2. 当前实现特点：
 *    - 仅更新前端状态（Zustand store）
 *    - 支持触觉反馈（移动端）
 *    - 提供丰富的视觉反馈
 *    - 支持配置开关各项拖拽功能
 * 
 * 3. 持久化改进（可选）：
 *    如果需要将拖拽排序结果持久化到数据库，可以：
 *    - 在 tagService 中添加 reorderGroups(groupIds: string[]) 方法
 *    - 在 tagService 中添加 reorderTags(groupId: string, tagIds: string[]) 方法
 *    - 在数据库 schema 中添加 order/position 字段
 *    - 在拖拽完成后调用相应的持久化方法
 * 
 * 4. 性能优化（可选）：
 *    - 可以添加防抖延迟，避免频繁的状态更新
 *    - 可以考虑使用 React.memo 优化组件渲染
 *    - 对于大量标签的情况，可以考虑虚拟化列表
 */
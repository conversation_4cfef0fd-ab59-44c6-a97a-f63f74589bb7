import {ResizeHandlers} from '@/components/ui/ResizeHandlers';
import {checkEdgeSnapByMousePosition, CustomResizableBox, PanelPosition, SnapPreview} from '@/pages/workspace/Panel';
import React, {FC, JSX, useState} from "react";
import Draggable, {DraggableData, DraggableEvent,} from "react-draggable";
import {ResizeCallbackData} from "react-resizable";

export const DragAndResize: FC<{
    panelPosition: PanelPosition,
    setPanelPosition: (position: PanelPosition) => void,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    className: string,
    panelClassName: string,
    innerElement: JSX.Element,
    // PanelWrapper: JSX.Element,
    PanelWrapper: any,
    zIndex: number,
    extraEvent?: any,
    handlePanelClick?: () => void,
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = (
    {
        panelPosition,
        className = '',
        panelClassName = '',
        innerElement = <></>,
        PanelWrapper,
        zIndex = 10,
        extraEvent = {},
        handlePanelClick = () => {
        },
        otherPanels = [],
        setPanelPosition,
        setPanelsPosition,
        setDragPanel,
        getAdjacentPanels
    }
) => {
    const SIDEBAR_WIDTH = 64; // 侧边栏宽度常量
    // 添加拖拽方向状态
    const [dragDirection, setDragDirection] = useState<'left' | 'right' | null>(null);
    const [lastMouseX, setLastMouseX] = useState<number>(0);

    // 面板拖拽开始处理函数
    const handleChatDragStart = (e: DraggableEvent) => {
        e.preventDefault()
        handlePanelClick?.()

        // 记录初始鼠标位置
        const mouseX = (e as MouseEvent).clientX || 0;
        setLastMouseX(mouseX);
        setDragDirection(null);

        setDragPanel(true);
    };

    // 处理调整大小开始
    const handleResizeStart = (
        e: React.SyntheticEvent,
        data: ResizeCallbackData
    ) => {
        if (panelPosition.isSnapped) {
            setPanelPosition({
                ...panelPosition,
                isSnapped: false,
                originalSize: undefined
            });
            // 不恢复原始大小，保持当前大小
        }
        handlePanelClick?.()
        extraEvent?.handlePdfResizeStart?.()
    };

    // 添加贴边预览状态
    const [snapPreview, setSnapPreview] = useState<SnapPreview>({
        visible: false,
        edge: null,
        previewPosition: {x: 0, y: 0, width: 0, height: 0},
        snapPosition: {x: 0, y: 0, width: 0, height: 0},
        opacity: 1
    });

    // 处理聊天面板拖拽
    const handleChatDrag = (e: DraggableEvent, data: DraggableData) => {
        console.log('handleChatDrag', snapPreview)
        e.preventDefault()
        const {x, y} = data;
        const panelWidth = panelPosition.width;
        const panelHeight = panelPosition.height;
        const minVisibleWidth = 100;

        // 只限制水平方向，垂直方向由Draggable的bounds属性控制
        const boundedX = Math.max(-panelWidth + minVisibleWidth, Math.min(x, window.innerWidth - minVisibleWidth));

        // 获取鼠标位置
        const mouseX = (e as MouseEvent).clientX || 0;
        const mouseY = (e as MouseEvent).clientY || 0;

        // 检测拖拽方向
        if (Math.abs(mouseX - lastMouseX) > 5) { // 添加一定阈值避免微小移动
            const newDirection = mouseX > lastMouseX ? 'right' : 'left';
            setDragDirection(newDirection);
            setLastMouseX(mouseX);
            console.log('handleChatDrag DragDirection', newDirection)
        }
        const {leftAdjacentPanels, rightAdjacentPanels} = getAdjacentPanels(otherPanels)
        // 使用鼠标位置检查是否需要贴边
        const snapInfo = checkEdgeSnapByMousePosition(mouseX, mouseY, panelWidth, panelHeight, leftAdjacentPanels, rightAdjacentPanels, dragDirection);
        console.log("snapInfo", snapInfo)
        if (snapInfo) {
            // 显示贴边预览
            setSnapPreview({
                visible: true,
                edge: snapInfo.edge as "left" | "right" | "top" | "bottom" | "panel-left" | "panel-right",
                previewPosition: snapInfo.previewPosition,
                snapPosition: snapInfo.snapPosition,
                opacity: 0.5
            });
        } else {
            // 隐藏贴边预览
            setSnapPreview({
                visible: false,
                edge: null,
                previewPosition: {x: 0, y: 0, width: 0, height: 0},
                snapPosition: {x: 0, y: 0, width: 0, height: 0},
                opacity: 0.5
            });
        }

        setPanelPosition({
            ...panelPosition,
            x: boundedX,
            y: y // 使用原始y值，不做额外限制
        });
    };

    // 处理聊天面板拖拽结束
    const handleChatDragStop = () => {
        console.log('handleChatDragStop', snapPreview)
        setDragPanel(false)
        const {leftAdjacentPanels, rightAdjacentPanels} = getAdjacentPanels(otherPanels)
        // 如果有贴边预览，应用贴边效果
        if (snapPreview.visible) {
            // 保存原始大小
            const originalSize = {
                width: panelPosition.width,
                height: panelPosition.height,
            };

            // 移动panel
            if (snapPreview.edge === "panel-left") {
                for (const panel of leftAdjacentPanels) {
                    if (panel.x >= snapPreview.snapPosition.x) {
                        switch (panel.type) {
                            case 'chat':
                                setPanelsPosition.chat(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x + snapPreview.snapPosition.width,
                                    }
                                })
                                break
                            case 'pdf':
                                setPanelsPosition.pdf(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x + snapPreview.snapPosition.width,
                                    }
                                })
                                break
                            case 'note':
                                setPanelsPosition.note(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x + snapPreview.snapPosition.width,
                                    }
                                })
                        }
                    }
                }
            } else {
                for (const panel of rightAdjacentPanels) {
                    if (panel.x + panel.width <= snapPreview.snapPosition.x + snapPreview.snapPosition.width) {
                        switch (panel.type) {
                            case 'chat':
                                setPanelsPosition.chat(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x - snapPreview.snapPosition.width,
                                    }
                                })
                                break
                            case 'pdf':
                                setPanelsPosition.pdf(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x - snapPreview.snapPosition.width,
                                    }
                                })
                                break
                            case 'note':
                                setPanelsPosition.note(prev => {
                                    return {
                                        ...prev,
                                        x: prev.x - snapPreview.snapPosition.width,
                                    }
                                })
                        }
                    }
                }
            }

            // 应用贴边效果 - 使用snapPosition而不是previewPosition
            setPanelPosition({
                x: snapPreview.snapPosition.x,
                y: snapPreview.snapPosition.y,
                width: snapPreview.snapPosition.width,
                height: snapPreview.snapPosition.height,
                isSnapped: true,
                originalSize,
            });

        }
        // 隐藏贴边预览
        setSnapPreview((prev) => ({...prev, visible: false, opacity: 1}));
    };

    // 处理面板大小调整
    const handleResize = (
        event: React.SyntheticEvent,
        {size, handle}: ResizeCallbackData
    ) => {
        const {width, height} = size;
        const newPosition = {...panelPosition};

        // 根据调整手柄的位置更新面板位置
        if (handle.includes("w")) {
            // 左侧调整
            const deltaWidth = panelPosition.width - width;
            newPosition.x = panelPosition.x + deltaWidth;
        }

        if (handle.includes("n")) {
            // 顶部调整
            const deltaHeight = panelPosition.height - height;
            newPosition.y = panelPosition.y + deltaHeight;
        }

        // 更新尺寸
        newPosition.width = width;
        newPosition.height = height;

        setPanelPosition(newPosition);
    };

    return (
        <>
            {/* 贴边预览蒙版 */}
            {snapPreview.visible && (
                <div
                    style={{
                        position: 'fixed',
                        left: snapPreview.previewPosition.x,
                        top: snapPreview.previewPosition.y,
                        width: snapPreview.previewPosition.width,
                        height: snapPreview.previewPosition.height,
                        background: 'rgba(24, 144, 255, 0.3)', // 半透明蓝色
                        zIndex: 99999, // 确保贴边预览在最顶层
                        pointerEvents: 'none',
                        borderRadius: '4px',
                        border: '2px solid #1890ff',
                    }}
                />
            )}

            <Draggable
                handle=".drag-handle"
                position={{x: panelPosition.x, y: panelPosition.y}}
                onStart={handleChatDragStart}
                onDrag={handleChatDrag}
                onStop={handleChatDragStop}
                bounds={{top: 0, bottom: window.innerHeight - panelPosition.height}} // 只限制顶部不能超出视口
            >
                <PanelWrapper
                    style={{
                        ...panelStyle,
                        zIndex: zIndex,
                        opacity: snapPreview.opacity
                    }}
                    onClick={() => handlePanelClick?.()}
                >
                    <CustomResizableBox
                        width={panelPosition.width}
                        height={panelPosition.height}
                        onResize={handleResize}
                        onResizeStart={handleResizeStart}
                        minConstraints={[300, 300]}
                        maxConstraints={[window.innerWidth, window.innerHeight]}
                        resizeHandles={panelPosition.resizeHandles ? panelPosition.resizeHandles.filter((handle) => ['se', 'sw', 'ne', 'nw'].includes(handle)) : ['se', 'sw', 'ne', 'nw']}
                        className={className}
                    >
                        <div
                            style={{
                                width: '100%',
                                height: '100%',
                                position: "relative",
                            }}
                        >
                            <div className="h-full">
                                {
                                    innerElement
                                }
                            </div>
                            <ResizeHandlers
                                panelClassName={panelClassName}
                                currentSize={{width: panelPosition.width, height: panelPosition.height}}
                                setSize={() => {
                                }}
                                position={panelPosition}
                                setPosition={setPanelPosition}
                                minSize={{width: 300, height: 300}}
                                customRightEdge={true}
                                customBottomEdge={true}
                                customLeftEdge={true}
                                customTopEdge={true}
                                showTopEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('n') : true}
                                showBottomEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('s') : true}
                                showLeftEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('w') : true}
                                showRightEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('e') : true}
                                {
                                    ...extraEvent
                                }
                            />
                        </div>
                    </CustomResizableBox>
                </PanelWrapper>
            </Draggable>
        </>
    )
}

const panelStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
}
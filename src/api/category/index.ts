import { httpRequest } from '../index'
import { CreateCategoryType, CategoryListType, UpdateCategoryType, DeleteCategoryType } from './types.d'
// 新建分类
export const createCategory = ({ cname }: CreateCategoryType) => httpRequest.post<CreateCategoryType>('cate', { cname })

// 获取分类列表
export const getCategoryList = ({ page, page_size }: CategoryListType) => httpRequest.get<CategoryListType[]>('cate/list', {
  params: {
    page,
    page_size
  }
})
// 更新分类
export const updateCategory = ({ cid, cname }: UpdateCategoryType) => httpRequest.patch<UpdateCategoryType>('cate', { cid, cname })


// 删除分类
export const deleteCategory = ({ cid }: DeleteCategoryType) => httpRequest.delete<DeleteCategoryType>('cate', { data: { cid } })

export * from './types.d'
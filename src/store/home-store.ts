import { create } from "zustand";
import type { SelectProps, MenuProps } from 'antd'
import { CategoryType, ProjectType } from '@/components/homepage/response-type'
export const useHomeStore = create<{
  searching: boolean, // 是否正在搜索
  categories: CategoryType[],
  projects: ProjectType[],
  createProjectModalVisible: boolean,
  selectCategoryId: string | number,
  layoutType: 'grid' | 'list',
  currentCategory: CategoryType | null,
  searchList: ProjectType[],
  categorySelectList: Required<MenuProps>['items'] | Required<SelectProps>['options'],
  recycleBinList: Array<ProjectType & { checked: boolean }>,
  openReCyclcModal: boolean
  selectProjectIds: Array<string | number>,
  isRestore: string,


  setSearching: (searching: boolean) => void,
  setCategories: (categories: CategoryType[]) => void,
  addCategory: (category: CategoryType) => void,
  removeCategory: (cid: string | number) => void,
  modifyCategory: (category: CategoryType) => void,
  toggleCategory: (oldCid: string | number, newCid: string | number) => void,
  setProjects: (projects: ProjectType[]) => void,
  addProject: (project: ProjectType) => void,
  removeProjectByWId: (wid: string | number) => void,
  removeProjectByCid: (cid: string | number) => void,
  modifyProject: (project: ProjectType) => void,
  setSearchList: (list: ProjectType[]) => void,
  setSelectCategoryId: (cid: string | number) => void,
  setCreateProjectModalVisible: (visible: boolean) => void,
  setlayoutType: (type: 'grid' | 'list') => void,
  setCurrentCategory: (category: CategoryType | null) => void,
  setCategorySelectList: (list: Required<MenuProps>['items'] | Required<SelectProps>['options']) => void
  setRecycleBinList: (list: Array<ProjectType & { checked: boolean }>) => void,
  updateRecycleBinList: (ids: Array<string | number>) => void,
  completeDeleteProject: (ids: Array<string | number>) => void,
  openRecycleBinModal: (open: boolean) => void,
  setSelectProjectIds: (ids: Array<string | number>) => void,
  setIsRestore: (isRestore: string) => void
  removeProjectSyncCategory: (wid: string | number) => void,
  restoreProjectSyncCategory: (wid_length: number) => void,
}>((set) => ({
  searching: false,
  categories: [],
  projects: [],
  recycleBinList: [],
  selectCategoryId: '-1',
  layoutType: 'grid',
  createProjectModalVisible: false,
  currentCategory: null,
  categorySelectList: [],
  searchList: [],
  openReCyclcModal: false,
  selectProjectIds: [],
  isRestore: "",

  setSearching: (searching) => set({ searching }),
  setCategories: (categories: CategoryType[]) => set({ categories }),
  addCategory: (category: CategoryType) => set((state) => ({ categories: [...state.categories, category] })),
  removeCategory: (cid: string | number) => set((state) => ({ categories: state.categories.filter((c) => c.cid !== cid) })),
  modifyCategory: (category: CategoryType) => set((state) => ({ categories: state.categories.map((c) => (c.cid === category.cid ? category : c)) })),
  toggleCategory: (oldCid: string | number, newCid: string | number) => set((state) => {
    const oldCategory = state.categories.find((c) => c.cid === oldCid)
    const newCategory = state.categories.find((c) => c.cid === newCid)
    if (oldCategory && newCategory) {
      oldCategory.wnum--;
      newCategory.wnum++;
      return {
        categories: state.categories.map((item) => {
          if (item.cid === oldCid) return oldCategory
          if (item.cid === newCid) return newCategory
          return item
        })
      }
    }
    return { categories: state.categories }
  }),
  setProjects: (projects: ProjectType[]) => set({ projects }),
  addProject: (project: ProjectType) => set((state) => ({ projects: [...state.projects, project] })),
  modifyProject: (project: ProjectType) => set((state) => ({ projects: state.projects.map((p) => (p.wid === project.wid ? project : p)) })),
  removeProjectByWId: (wid: string | number) => set((state) => ({ projects: state.projects.filter((p) => p.wid !== wid) })),
  removeProjectByCid: (cid: string | number) => set((state) => ({ projects: state.projects.filter((p) => p.cid !== cid) })),
  setSearchList: (list) => set({ searchList: list }),
  setSelectCategoryId: (cid: string | number) => set({ selectCategoryId: cid }),
  setCreateProjectModalVisible: (visible: boolean) => set({ createProjectModalVisible: visible }),
  setlayoutType: (type) => set({ layoutType: type }),
  setCurrentCategory: (category: CategoryType | null) => set({ currentCategory: category }),
  setCategorySelectList: (list) => set({ categorySelectList: list }),
  setRecycleBinList: (list) => set({ recycleBinList: list }),
  updateRecycleBinList: (ids: Array<string | number>) => set((state) => ({
    recycleBinList: state.recycleBinList.map((item) => ({
      ...item,
      checked: ids.some(id => id === item.wid) ? !item.checked : item.checked
    }))
  })),
  completeDeleteProject: (ids: Array<string | number>) => set((state) => ({
    recycleBinList: state.recycleBinList.filter((item) => !ids.some(id => id === item.wid))
  })),
  openRecycleBinModal: (open) => set({ openReCyclcModal: open }),
  setSelectProjectIds: (ids) => set({ selectProjectIds: ids }),
  setIsRestore: (isRestore) => set({ isRestore }),
  removeProjectSyncCategory: (cid: string | number) => set((state) => {
    return {
      categories: state.categories.map(item => {
        if (item.cid === cid) {
          return {
            ...item,
            wnum: item.wnum - 1
          }
        } else if (item.cid === "-1") {
          return {
            ...item,
            wnum: item.wnum - 1
          }
        }
        return item
      })
    }
  }),
  restoreProjectSyncCategory: (wid_length: number) => set((state) => {
    return {
      categories: state.categories.map(item => {
        if (item.cid === '0') {
          return {
            ...item,
            wnum: item.wnum + wid_length
          }
        } else if (item.cid === "-1") {
          return {
            ...item,
            wnum: item.wnum + wid_length
          }
        }
        return item
      })
    }
  }),
}))
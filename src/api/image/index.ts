import { httpRequest } from '../index'
import { UploadImageToOssType, UploadCanvasSnapshotType } from './types.d'
// 2. 获取OSS上传图片签名
export const getOssSignatureForImage = () =>
  httpRequest.post('/ws/img');
// 3. 上传图片到OSS
export const uploadImageToOss = ({
  fileName,
  file,
  ossInfo,
}: UploadImageToOssType) => {

  try {
    const formData = new FormData();
    formData.append('key', fileName);
    formData.append('policy', ossInfo.policy);
    formData.append('OSSAccessKeyId', ossInfo.accessid);
    formData.append('signature', ossInfo.signature);
    formData.append('file', file as File);
    formData.append('success_action_status', '200');
    // 修改请求配置
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', ossInfo.host, true);
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(xhr.status);
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };
      xhr.onerror = () => {
        reject(new Error('Upload failed'));
      };
      xhr.send(formData);
    });
  } catch (error) {
    throw error;
  }
}
export const uploadPreview = (data: UploadCanvasSnapshotType) => httpRequest.post('/ws/preview', data)

export * from './types.d'
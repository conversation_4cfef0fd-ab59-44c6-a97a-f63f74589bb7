import {ReactNode, useState} from "react";
import {CopyToClipboard} from "react-copy-to-clipboard";
import styled from "styled-components";

interface CopyableContentProps {
  content: string;
  iconPosition?: "top-right" | "bottom-right" | "right-center";
  className?: string;
  children?: ReactNode;
}
const CopyContainer = styled.div`
    &:hover > :last-child {
    opacity: 1;
  }

    & > :last-child {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
  //
`;

export function CopyableContent({
  content,
  iconPosition = "bottom-right",
  className = "",
  children,
}: CopyableContentProps) {
  const [isCopied, setIsCopied] = useState(false);

  const positionClasses = {
    "top-right": "top-3 right-3",
    "right-center": "right-3 top-1/2 -translate-y-1/2",
    "bottom-right": "bottom-3 right-3",
  };

  return (
    <CopyContainer className={`relative ${className}`}>
      {children || content}
      <CopyToClipboard
        text={content}
        onCopy={() => {
          setIsCopied(true);
          setTimeout(() => {
            setIsCopied(false);
          }, 1500);
        }}
      >
        <button
          className={`absolute ${positionClasses[iconPosition]} rounded hover:bg-gray-100 p-1 transition-colors`}
          aria-label={isCopied ? "已复制" : "复制"}
        >
          {isCopied ? "✓" : "📋"}
        </button>
      </CopyToClipboard>
    </CopyContainer>
  );
}

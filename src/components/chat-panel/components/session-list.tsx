import { useState, useEffect, useRef } from "react";
import type { InputRef } from "antd/es/input";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { SessionItem } from "./session-item";
import { useSessionGroups } from "../hooks/use-session-groups";
import { Empty } from "antd";
import styled from "styled-components";

const EmptyContainer = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #959dcd;
`;

const SessionListContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
`;

const SessionGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
`;

const DateTitle = styled.div`
  padding: 8px 12px 4px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  margin-bottom: 4px;
`;

export const SessionList = () => {
  const { sessions } = useChatStore();
  const { newSessions } = useSessionGroups(sessions);
  const [activeId, setActiveId] = useState<string>("");
  const [isEditId, setIsEditId] = useState<string>("");
  const [newSessionTitle, setNewSessionTitle] = useState<string>("");
  const editSessionInputRef = useRef<InputRef>(null);
  
  // 失焦函数
  const handleClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest(".active")) {
      setActiveId("");
    }
  };
  
  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (editSessionInputRef.current) {
      editSessionInputRef.current.focus();
    }
  }, [editSessionInputRef.current]);

  if (!newSessions.length) {
    return (
      <EmptyContainer>
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
          description="暂无历史会话"
          style={{ color: '#959dcd' }}
        />
      </EmptyContainer>
    );
  }

  return (
    <SessionListContainer>
      {newSessions.map((item, index) => (
        <SessionGroup key={index + `${crypto.randomUUID()}`}>
          <DateTitle>{item.dateTitle}</DateTitle>
          {item.sessions.map((session) => (
            <SessionItem
              key={session.sid}
              session={session}
              isEdit={isEditId === session.sid}
              isActive={activeId === session.sid}
              editRef={editSessionInputRef}
              onSetActive={setActiveId}
              onSetEdit={setIsEditId}
              onSetTitle={setNewSessionTitle}
              newTitle={newSessionTitle}
            />
          ))}
        </SessionGroup>
      ))}
    </SessionListContainer>
  );
};

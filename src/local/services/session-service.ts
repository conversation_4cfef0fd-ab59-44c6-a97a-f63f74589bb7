import {getDatabase} from '../db';
import {generateId} from '@/local';

export interface CreateReq {
    wid: string;
    question: string;
}

export interface CreateResp {
    sid: string;
}

export interface UpdateReq {
    sid: string;
    title: string;
}

export interface DeleteReq {
    sid: string;
}

export interface ListReq {
    wid: string;
    page: number;
    page_size: number;
}

export interface ListResp {
    list: Array<{
        sid: string;
        title: string;
        datetime: number;
    }>;
    total: number;
    page_count: number;
}

export interface DetailReq {
    sid: string;
    rid: number;
    page_size: number;
}

export interface DetailResp {
    chat: Array<{
        rid: number;
        question: string;
        answer: string;
        refs: Array<{
            type: number;
            id: string;
            title: string;
            content: string;
            page: number;
        }>;
    }>;
}

export interface ChatReq {
    wid: string;
    sid: string;
    refs: Array<{
        type: number;
        id: string;
        title: string;
        content: string;
        page?: number;
    }>;
    question: string;
    answer: string;
    rid?: number;
}

export class SessionService {
    /**
     * 创建会话
     */
    async create(req: CreateReq): Promise<CreateResp> {
        const db = await getDatabase();
        const sid = generateId();
        const now = Math.floor(Date.now() / 1000);

        // 去除title两边的空格
        req.question = req?.question?.trim?.();
        // if (req.question.length === 0) {
        //     throw new Error('问题不能为空');
        // }

        // 问题可以为空，title不能为 undefined
        // 创建会话
        await db.sessions.insert({
            id: sid,
            wid: req.wid,
            title: req.question?.length > 32 ? req.question.substring(0, 32) : req.question || '',
            last_at: now
        });

        return {sid};
    }

    /**
     * 更新会话
     */
    async update(req: UpdateReq): Promise<void> {
        const db = await getDatabase();
        const session = await db.sessions.findOne({
            selector: {id: req.sid}
        }).exec();

        if (!session) {
            throw new Error('会话不存在');
        }

        await session.update({
            $set: {
                title: req.title,
                last_at: Math.floor(Date.now() / 1000)
            }
        });
    }

    /**
     * 删除会话
     */
    async delete(req: DeleteReq): Promise<void> {
        const db = await getDatabase();

        // 删除会话
        await db.sessions.find({
            selector: {id: req.sid}
        }).remove();

        // 删除相关的轮次
        await db.rounds.find({
            selector: {sid: req.sid}
        }).remove();
    }

    /**
     * 获取会话列表
     */
    async list(req: ListReq): Promise<ListResp> {
        const db = await getDatabase();
        const skip = (req.page - 1) * req.page_size;
        const limit = req.page_size;

        // 查询总数
        const total = await db.sessions.find({
            selector: {wid: req.wid}
        }).exec().then((docs: any[]) => docs.length);

        // 查询列表
        const sessions = await db.sessions.find({
            selector: {wid: req.wid},
            sort: [{last_at: 'desc'}],
            skip,
            limit
        }).exec();

        const list = sessions.map((session: { id: any; title: any; last_at: any; }) => ({
            sid: session.id,
            title: session.title,
            datetime: session.last_at
        }));

        return {
            list,
            total,
            page_count: Math.ceil(total / req.page_size)
        };
    }

    /**
     * 保存对话
     */
    async saveChat(req: ChatReq): Promise<void> {
        console.log("saveChat", req)
        const db = await getDatabase();

        // 更新会话最后活动时间
        const session = await db.sessions.findOne({
            selector: {id: req.sid}
        }).exec();

        if (!session) {
            throw new Error('会话不存在');
        }

        await session.update({
            $set: {
                last_at: Math.floor(Date.now() / 1000)
            }
        });

        let nextRid = req.rid;
        if (!req.rid || req.rid === 0) {
            const maxRid = await db.rounds.findOne({
                selector: {sid: req.sid},
                sort: [{rid: 'desc'}],
            }).exec().then((doc: any) => doc ? doc.rid : 0);
            nextRid = maxRid + 1;
        } else {
            // 删除当前轮次以下的记录
            await db.rounds.find({
                selector: {sid: req.sid, rid: {$gte: req.rid}},
            }).remove();
        }

        // 创建轮次记录
        await db.rounds.insert({
            id: generateId(),
            sid: req.sid,
            rid: nextRid,
            question: req.question,
            answer: req.answer,
            refs: req.refs
        });
    }

    /**
     * 获取会话详情
     */
    async detail(req: DetailReq): Promise<DetailResp> {
        const db = await getDatabase();

        // 查询轮次
        let query: any = {
            sid: req.sid
        };

        // 如果指定了rid，则查询该rid之前的记录
        if (req.rid > 0) {
            query.rid = {$lt: req.rid};
        }

        const rounds = await db.rounds.find({
            selector: query,
            sort: [{rid: 'desc'}],
            limit: req.page_size
        }).exec();

        const chat = rounds.map((round: { rid: any; question: any; answer: any; refs: any; }) => ({
            rid: round.rid,
            question: round.question,
            answer: round.answer,
            refs: round.refs
        }));

        return {chat};
    }
} 
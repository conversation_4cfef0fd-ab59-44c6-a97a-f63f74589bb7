import {getDatabase} from '../db';
import {categoryService, generateId} from '@/local';

export interface CreateReq {
    cid: string;
    title: string;
    attachs?: Array<{
        fname: string;
        content: Blob;
    }>;
}

export interface CreateResp {
    wid: string;
}

export interface UpdateReq {
    wid: string;
    title: string;
    cid: string;
}

export interface ListReq {
    cid: string;
    page: number;
    page_size: number;
    keyword: string;
    from: number;
}

export interface ListResp {
    list: Array<{
        wid: string;
        title: string;
        cid: string;
        preview: string;
        last_at: number;
        del_at: number;
        type: number;
    }>;
    total: number;
    page_count: number;
}

export interface SignResp {
    policy: string;
}

export interface AttachReq {
    wid: string;
    attachs: Array<{
        fname: string;
        content: Blob;
    }>;
}

export interface AttachResp {
    list: Array<{
        aid: string;
        url: string;
        fname: string;
    }>;
}

export interface PreviewReq {
    wid: string;
    file: Blob;
}

export interface AttachListReq {
    wid: string;
}

export interface AttachListResp {
    list: Array<{
        aid: string;
        path: string;
        url: string;
        fname: string;
        marks: Array<{
            mid: string;
            aid: string;
            nid?: string;
            mark: string;
            color: string;
            mark_type?: string;
            content?: string;
        }>;
    }>;
}

export interface UpdateMarkReq {
    wid: string;
    mid: string;
    mark: string;
    color: string;
}

export interface DeleteReq {
    wid: string;
}

export interface DeleteAttachReq {
    aid: string;
    type: number;
}

export interface RestoreReq {
    wids: string[];
}

export interface CleanReq {
    wids: string[];
}

export interface NoteUpdateReq {
    wid: string;
    content: string;
}

export interface NoteContentReq {
    wid: string;
}

export interface NoteContentResp {
    content: string;
}


// 添加文本插入请求接口
export interface InsertTextReq {
    wid: string;
    aid: string;
    text: string;
    mark: string;
    color?: string;
}

// 添加文本插入响应接口
export interface InsertTextResp {
    mid: string;
}

// 定义工作区状态常量
export const NodeStateDelete = 2; // 节点删除状态

// 新增的文件上传请求接口（支持文件夹结构）
export interface UploadFilesReq {
    wid: string;
    files: File[];
    parent_id?: string;
}

// 新增的文件上传响应接口
export interface UploadFilesResp {
    list: Array<{
        aid: string;
        url: string;
        filename: string;
    }>;
}

export class WorkspaceService {
    /**
     * 检查标题是否已存在
     * @param title 标题
     * @param excludeId 排除的工作区ID（用于更新时排除自身）
     * @returns 如果存在返回true，否则返回false
     */
    private async isTitleExists(title: string, excludeId?: string): Promise<boolean> {
        try {
            const db = await getDatabase();

            // 确保数据库和集合已初始化
            if (!db || !db.workspaces) {
                console.error('数据库或workspaces集合未初始化');
                return false;
            }

            console.log('检查标题是否存在:', title, '排除ID:', excludeId);

            // 使用索引优化查询：利用['title', 'del_id']复合索引
            // 查询条件：标题相同且未删除的工作区
            const query = db.workspaces.find({
                selector: {
                    title: title,
                    del_id: '0' // 只检查未删除的工作区
                }
            });

            // 如果提供了排除ID，则需要进一步过滤
            if (excludeId) {
                const docs = await query.exec();
                console.log('查询结果(排除ID):', docs.length);
                return docs.some((doc: any) => doc.id !== excludeId);
            } else {
                // 不需要排除ID时，直接检查是否有结果
                const docs = await query.exec();
                console.log('查询结果:', docs.length);
                return docs.length > 0;
            }
        } catch (error) {
            console.error('检查标题是否存在时出错:', error);
            // 出错时返回false，允许创建
            return false;
        }
    }

    /**
     * 创建工作区
     */
    async create(req: CreateReq): Promise<CreateResp> {
        try {
            console.log('创建工作区请求:', req);
            if (req.title.length > 30) {
                throw new Error('标题长度不能超过30个字符');
            }
            // 检查标题是否已存在
            const exists = await this.isTitleExists(req.title);
            if (exists) {
                throw new Error('已存在同名工作区，请使用其他名称');
            }

            const db = await getDatabase();

            // 确保数据库和集合已初始化
            if (!db || !db.workspaces) {
                throw new Error('数据库或workspaces集合未初始化');
            }

            const wid = generateId();
            const now = Math.floor(Date.now() / 1000);

            console.log('创建工作区:', wid, req.title);

            // 创建工作区
            await db.workspaces.insert({
                id: wid,
                title: req.title,
                cate_id: req.cid,
                last_at: now,
                del_id: '0', // 确保del_id是字符串类型
                del_at: 0,
                create_at: now,
                update_at: now
            });

            // 如果有附件，创建附件
            if (req.attachs && req.attachs.length > 0) {
                console.log(`处理 ${req.attachs.length} 个附件`);

                for (const attach of req.attachs) {
                    const aid = generateId();

                    console.log(`创建附件: ${aid}, 文件名: ${attach.fname}`);

                    // 创建附件记录（兼容新的数据结构）
                    const now = Math.floor(Date.now() / 1000);
                    const attachDoc = await db.attachments.insert({
                        id: aid,
                        wid,
                        file_name: attach.fname,
                        type: 'file', // 新增字段
                        parent_id: 'root', // 新增字段，默认放在根目录
                        order: 0, // 新增字段，默认排序
                        mime_type: 'application/pdf', // 新增字段
                        create_at: now, // 新增字段
                        update_at: now // 新增字段
                    });
                    await attachDoc.putAttachment({
                        id: aid,
                        data: attach.content,
                        type: 'application/pdf',
                    })
                }
            }

            // 更新分类下的工作区数量
            if (req.cid && req.cid !== '0') {
                await categoryService.updateWorkspaceCount(req.cid);
            }

            console.log('工作区创建成功:', wid);
            return {wid};
        } catch (error) {
            // 捕获并重新抛出错误，确保前端能收到正确的错误信息
            console.error('创建工作区失败:', error);
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('创建工作区失败');
            }
        }
    }

    /**
     * 更新工作区
     */
    async update(req: UpdateReq): Promise<void> {
        try {
            if (req.title.length > 30) {
                throw new Error('标题长度不能超过30个字符');
            }

            // 检查标题是否已存在（排除自身）
            const exists = await this.isTitleExists(req.title, req.wid);
            if (exists) {
                throw new Error('已存在同名工作区，请使用其他名称');
            }

            const db = await getDatabase();
            const workspace = await db.workspaces.findOne({
                selector: {id: req.wid, del_id: '0'}
            }).exec();

            if (!workspace) {
                throw new Error('工作区不存在');
            }
            // 分类是否存在
            if (req.cid && req.cid !== '0') {
                const category = await db.categories.findOne({
                    selector: {id: req.cid}
                }).exec();
                if (!category) {
                    throw new Error('分类不存在');
                }
            }

            const oldCateId = workspace.cate_id;
            const now = Math.floor(Date.now() / 1000);

            await workspace.update({
                $set: {
                    title: req.title,
                    cate_id: req.cid,
                    last_at: now,
                    update_at: now
                }
            });

            // 如果分类发生变化，更新分类下的工作区数量
            if (oldCateId !== req.cid) {
                if (req.cid && req.cid !== '0') {
                    await categoryService.updateWorkspaceCount(req.cid);
                }
                if (oldCateId && oldCateId !== '0') {
                    await categoryService.updateWorkspaceCount(oldCateId);
                }
            }
        } catch (error) {
            // 捕获并重新抛出错误，确保前端能收到正确的错误信息
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('更新工作区失败');
            }
        }
    }

    /**
     * 获取工作区列表
     */
    async list(req: ListReq): Promise<ListResp> {
        const db = await getDatabase();
        const skip = (req.page - 1) * req.page_size;
        const limit = req.page_size;

        // 构建查询条件
        const selector: any = {};

        // 分类筛选
        if (req.cid && req.cid !== '-1') {
            selector.cate_id = req.cid;
        }

        // 删除状态筛选
        if (req.from === 0) {
            selector.del_id = '0'; // 未删除
        } else if (req.from === 1) {
            selector.del_id = {$ne: '0'}; // 已删除
        }

        // 关键词搜索
        if (req.keyword) {
            selector.title = {$regex: new RegExp(req.keyword, 'i')}
        }

        // 查询总数
        const total = await db.workspaces.find({
            selector
        }).exec().then((docs: any[]) => docs.length);

        // 查询列表
        const workspaces = await db.workspaces.find({
            selector,
            sort: [{last_at: 'desc'}],
            skip,
            limit
        }).exec();

        // 使用 Promise.all 处理异步操作
        const list = await Promise.all(workspaces.map(async (ws: any) => {
            let preview = null;
            try {
                const attach = await ws.getAttachment(ws.id);
                if (attach) {
                    const URL = window.URL || window.webkitURL;
                    const data = await attach.getData()
                    preview = URL.createObjectURL(data);
                }
            } catch (error) {
                console.error('获取附件失败:', error);
            }

            return {
                wid: ws.id,
                title: ws.title,
                cid: ws.cate_id,
                preview: preview,
                last_at: ws.last_at,
                del_at: ws.del_at,
                type: ws.type || (ws.title && ws.title.length > 30 ? 1 : 0) // 根据标题长度设置类型
            };
        }));

        return {
            list,
            total,
            page_count: Math.ceil(total / req.page_size)
        };
    }

    /**
     * 删除工作区（软删除）
     */
    async delete(req: DeleteReq): Promise<void> {
        const db = await getDatabase();
        const workspace = await db.workspaces.findOne({
            selector: {id: req.wid}
        }).exec();

        if (!workspace) {
            throw new Error('工作区不存在');
        }

        const cateId = workspace.cate_id;
        const now = Math.floor(Date.now() / 1000);

        // 更新工作区状态
        await workspace.update({
            $set: {
                del_id: workspace.id, // 设置删除ID为自身ID
                cate_id: '0', // 清空分类ID
                del_at: now,
                last_at: now
            }
        });

        // 如果有分类，更新分类下的工作区数量
        if (cateId && cateId !== '0') {
            await categoryService.updateWorkspaceCount(cateId);
        }
    }

    /**
     * 获取PDF上传策略
     */
    async pdf(): Promise<SignResp> {
        // 本地实现不需要上传策略，返回空
        return {policy: ''};
    }

    /**
     * 获取图片上传策略
     */
    async img(): Promise<SignResp> {
        // 本地实现不需要上传策略，返回空
        return {policy: ''};
    }

    /**
     * 添加附件
     */
    async attach(req: AttachReq): Promise<AttachResp> {
        try {
            const db = await getDatabase();
            const list = [];

            // 工作区是否存在
            if (!(await db.workspaces.findOne({
                selector: {id: req.wid}
            }).exec())) {
                throw new Error('工作区不存在');
            }

            // 保存附件
            for (const attach of req.attachs) {
                const aid = generateId();

                // 创建附件记录（兼容新的数据结构）
                const now = Math.floor(Date.now() / 1000);
                const attachDoc = await db.attachments.insert({
                    id: aid,
                    wid: req.wid,
                    file_name: attach.fname,
                    type: 'file', // 新增字段
                    parent_id: 'root', // 新增字段，默认放在根目录
                    order: 0, // 新增字段，默认排序
                    mime_type: 'application/pdf', // 新增字段
                    create_at: now, // 新增字段
                    update_at: now // 新增字段
                });
                await attachDoc.putAttachment({
                    id: aid,
                    data: attach.content,
                    type: 'application/pdf',
                })
                const URL = window.URL || window.webkitURL
                const attachUrl = URL.createObjectURL(attach.content)
                list.push({
                    aid,
                    url: attachUrl,
                    fname: attach.fname
                });
            }

            return {list};
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('添加附件失败');
            }
        }
    }

    /**
     * 删除附件
     */
    async deleteAttach(req: DeleteAttachReq): Promise<void> {
        let nodeIds: string[] = [];
        try {
            const db = await getDatabase();
            const attachment = await db.attachments.findOne({
                selector: {id: req.aid}
            }).exec();

            if (!attachment) {
                throw new Error('附件不存在');
            }

            // 如果是类型2，先查找相关节点ID
            if (req.type === 2) {
                const marks = await db.marks.find({
                    selector: {attach_id: req.aid}
                }).exec();

                nodeIds = marks.map((mark: any) => mark.node_id).filter(Boolean);
            }

            // 开始事务操作
            // 1. 删除附件
            await attachment.remove();
            
            // 2. 删除相关标记
            await db.marks.find({
                selector: {attach_id: req.aid}
            }).remove();

            // 3. 如果是类型2，还需要删除相关的节点和连接
            if (req.type === 2 && nodeIds.length > 0) {
                // 删除节点
                    await db.nodes.find({
                        selector: {id: {$in: nodeIds}}
                    }).remove();

                    // 删除节点连接
                    await db.edges.find({
                        selector: {
                            $or: [
                                {nid: {$in: nodeIds}},
                                {pid: {$in: nodeIds}}
                            ]
                        }
                    }).remove();

                console.log('附件删除成功，同时删除了相关节点:', nodeIds);
            }

            // 只有在所有数据库操作都成功后才触发事件
            if (req.type === 2 && nodeIds.length > 0) {
                // 触发节点删除事件，通知画布更新
                const { NodeEvents } = await import('../services/node-service');
                NodeEvents.emit('nodesDeleted', nodeIds);
            }
            
        } catch (error) {
            console.error('删除附件失败:', error);
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('删除附件失败');
            }
        }
    }

    /**
     * 设置工作区预览图
     */
    async preview(req: PreviewReq): Promise<void> {
        const db = await getDatabase();
        const workspace = await db.workspaces.findOne({
            selector: {id: req.wid}
        }).exec();

        if (!workspace) {
            throw new Error('工作区不存在');
        }

        await workspace.putAttachment({
            id: workspace.id,
            data: req.file,
            type: req.file.type,
        })
    }

    /**
     * 获取附件列表
     */
    async attachList(req: AttachListReq): Promise<AttachListResp> {
        try {
            const db = await getDatabase();

            // 检查数据库和集合是否已初始化
            if (!db) {
                console.error('数据库未初始化');
                return {list: []};
            }

            if (!db.attachments) {
                console.error('attachments集合未初始化');
                return {list: []};
            }

            // 查询附件
            const attachments = await db.attachments.find({
                selector: {wid: req.wid}
            }).exec();

            const list = [];

            for (const attachment of attachments) {
                try {
                    // 只处理文件类型的附件，跳过文件夹
                    if (attachment.type === 'folder') {
                        continue;
                    }

                    let attachUrl = '';
                    
                    // 安全地获取附件的二进制数据
                    try {
                        const attach = attachment.getAttachment(attachment.id);
                        if (attach) {
                            const value = await attach.getData();
                            if (value) {
                                // 把 Blob 对象转成 ObjectURL，以便在页面显示
                                const URL = window.URL || window.webkitURL;
                                attachUrl = URL.createObjectURL(value);
                            }
                        }
                    } catch (binaryError) {
                        console.warn(`获取附件 ${attachment.id} 的二进制数据失败:`, binaryError);
                        // 如果有file_path，使用file_path作为URL
                        if (attachment.file_path) {
                            attachUrl = attachment.file_path;
                        }
                    }

                    // 查询标记
                    let formattedMarks = [];
                    if (db.marks) {
                        try {
                    const marks = await db.marks.find({
                        selector: {attach_id: attachment.id}
                    }).exec();

                    console.log(`附件 ${attachment.id} 有 ${marks.length} 个标记`);

                    // 将marks转换为API需要的格式
                            formattedMarks = marks.map((mark: any) => ({
                            mid: mark.id,
                            aid: mark.attach_id,
                            nid: mark.node_id || '',
                            mark: mark.mark || '',
                            color: mark.color || '',
                            mark_type: mark.mark_type || '',
                            content: mark.content || ''
                            }));
                        } catch (markError) {
                            console.warn(`获取附件 ${attachment.id} 的标记失败:`, markError);
                        }
                    } else {
                        console.warn('marks集合未初始化');
                    }

                    list.push({
                        aid: attachment.id,
                        path: attachment.file_path || '',
                        url: attachUrl,
                        fname: attachment.file_name,
                        marks: formattedMarks
                    });

                } catch (attachError) {
                    console.error(`处理附件 ${attachment.id} 时出错:`, attachError);
                    // 继续处理下一个附件，不中断整个流程
                }
            }

            return {list};
        } catch (error) {
            console.error('获取附件列表失败:', error);
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('获取附件列表失败');
            }
        }
    }

    /**
     * 更新标记
     */
    async updateMark(req: UpdateMarkReq): Promise<void> {
        const db = await getDatabase();
        const mark = await db.marks.findOne({
            selector: {id: req.mid}
        }).exec();

        if (!mark) {
            throw new Error('标记不存在');
        }
        let markObj
        try {
            markObj = JSON.parse(req.mark)
        } catch (error) {
            throw new Error('高亮格式错误');
        }
        const mark_type = markObj.type
        const content = markObj.content.text || markObj.content.image
        delete markObj.type
        delete markObj.content
        await mark.update({
            $set: {
                mark: req.mark,
                color: req.color,
                mark_type: mark_type,
                content: content
            }
        });
    }

    /**
     * 恢复工作区
     */
    async restore(req: RestoreReq): Promise<void> {
        try {
            const db = await getDatabase();
            const now = Math.floor(Date.now() / 1000);

            for (const wid of req.wids) {
                const workspace = await db.workspaces.findOne({
                    selector: {id: wid}
                }).exec();

                if (workspace) {
                    let newTitle = workspace.title
                    if (newTitle.length <= 30) {
                        newTitle = `${workspace.title}${workspace.id}`;
                    }

                    await workspace.update({
                        $set: {
                            del_id: '0',
                            del_at: 0,
                            title: newTitle,
                            update_at: now,
                            last_at: now
                        }
                    });
                }
            }
        } catch (error) {
            // 捕获并重新抛出错误，确保前端能收到正确的错误信息
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('恢复工作区失败');
            }
        }
    }

    /**
     * 彻底删除工作区
     */
    async clean(req: CleanReq): Promise<void> {
        try {
            const db = await getDatabase();

            for (const wid of req.wids) {
                // 先备份工作区数据
                const workspace = await db.workspaces.findOne({
                    selector: {id: wid}
                }).exec();

                if (workspace) {
                    // 删除工作区
                    await workspace.remove();

                    // 删除相关附件
                    await db.attachments.find({
                        selector: {wid}
                    }).remove();

                    // 删除相关节点
                    await db.nodes.find({
                        selector: {wid}
                    }).remove();

                    // 删除相关边
                    await db.edges.find({
                        selector: {wid}
                    }).remove();

                    // TODO：删除相关会话
                    await db.sessions.find({
                        selector: {wid}
                    }).remove();

                    // 删除相关标记
                    await db.marks.find({
                        selector: {wid}
                    }).remove();
                }
            }
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('彻底删除工作区失败');
            }
        }
    }

    /**
     * 更新笔记内容
     */
    async noteUpdate(req: NoteUpdateReq): Promise<void> {
        try {
            const db = await getDatabase();
            const workspace = await db.workspaces.findOne({
                selector: {id: req.wid}
            }).exec();

            if (!workspace) {
                throw new Error('工作区不存在');
            }

            // 更新工作区最后修改时间
            await workspace.update({
                $set: {
                    last_at: Math.floor(Date.now() / 1000)
                }
            });

            // 检查是否存在笔记记录
            const note = await db.notes.findOne({
                selector: {wid: req.wid}
            }).exec();

            if (note) {
                // 更新现有笔记
                await note.update({
                    $set: {
                        content: req.content
                    }
                });
            } else {
                // 创建新笔记
                await db.notes.insert({
                    id: generateId(),
                    wid: req.wid,
                    content: req.content
                });
            }
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('更新笔记内容失败');
            }
        }
    }

    /**
     * 获取笔记内容
     */
    async noteContent(req: NoteContentReq): Promise<NoteContentResp> {
        try {
            const db = await getDatabase();

            // 查询笔记表
            const note = await db.notes.findOne({
                selector: {wid: req.wid}
            }).exec();

            if (note) {
                return {
                    content: note.content
                };
            }

            // 如果笔记表没有记录，返回空内容
            return {
                content: ''
            };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('获取笔记内容失败');
            }

            // 返回空内容作为默认值
            return {
                content: ''
            };
        }
    }

    // 创建文本插入
    async insertText(req: InsertTextReq): Promise<InsertTextResp> {
        try {
            const db = await getDatabase();
            if (!db || !db.marks) {
                console.error('数据库或marks集合未初始化');
                throw new Error('数据库未准备好');
            }

            // 工作区是否存在
            const workspace = await db.workspaces.findOne({
                selector: {id: req.wid}
            }).exec();

            if (!workspace) {
                throw new Error('工作区不存在');
            }

            // 附件是否存在
            const attachment = await db.attachments.findOne({
                selector: {id: req.aid}
            }).exec();

            if (!attachment) {
                throw new Error('附件不存在');
            }

            const now = Math.floor(Date.now() / 1000);
            const id = generateId();

            // 解析position字符串为对象
            let positionObj;
            try {
                positionObj = JSON.parse(req.mark);
                // 假设前端已经在mark中包含了type和content
                console.log('解析mark成功:', positionObj);
            } catch (e) {
                console.error('解析mark失败:', e);
                positionObj = req.mark;
            }

            // 创建mark记录
            const markData: any = {
                id: id,
                attach_id: req.aid,
                wid: req.wid,
                mark: typeof positionObj === 'string' ? positionObj : JSON.stringify(positionObj),
                color: req.color || 'FF0000',
                create_at: now,
                update_at: now
            };

            // 从position中提取type和content（如果存在）
            if (typeof positionObj === 'object') {
                if (positionObj.type) {
                    markData.mark_type = positionObj.type;
                }

                if (positionObj.content && positionObj.content.text) {
                    markData.content = positionObj.content.text;
                } else if (req.text) {
                    // 兼容旧版本：如果position中没有content但请求中有text，则使用请求中的text
                    markData.content = req.text;
                }
            } else {
                // 兼容旧版本：如果无法解析position，则使用请求中的text和默认type
                markData.mark_type = 'text-insert';
                markData.content = req.text;
            }

            await db.marks.insert(markData);
            console.log('文本插入标记创建成功:', id);

            return {
                mid: id
            };
        } catch (error) {
            console.error('插入文本失败:', error);
            throw error;
        }
    }

    /**
     * 上传文件（支持文件夹结构）
     * 这个方法是对原有 attach 方法的增强版，支持指定父文件夹和更好的版本冲突处理
     */
    async uploadFiles(req: UploadFilesReq): Promise<UploadFilesResp> {
        try {
            const db = await getDatabase();
            const list = [];
            
            // 验证工作区是否存在
            const workspace = await db.workspaces.findOne({
                selector: { id: req.wid }
            }).exec();
            
            if (!workspace) {
                throw new Error('工作区不存在');
            }
            
            // 验证父文件夹是否存在（如果指定了非root的父文件夹）
            if (req.parent_id && req.parent_id !== 'root') {
                const parentFolder = await db.attachments.findOne({
                    selector: { 
                        id: req.parent_id,
                        type: 'folder'
                    }
                }).exec();
                
                if (!parentFolder) {
                    throw new Error('指定的父文件夹不存在');
                }
            }
            
            // 获取父文件夹下的最大order值
            const maxOrder = await this.getMaxOrderInParent(req.wid, req.parent_id || 'root');
            
            // 处理每个文件
            for (let i = 0; i < req.files.length; i++) {
                const file = req.files[i];
                const aid = generateId();
                const now = Math.floor(Date.now() / 1000);
                
                try {
                    // 创建文件记录
                    const attachDoc = await db.attachments.insert({
                        id: aid,
                        wid: req.wid,
                        file_name: file.name,
                        type: 'file',
                        parent_id: req.parent_id || 'root',
                        order: maxOrder + i + 1, // 使用递增的order值
                        size: file.size,
                        mime_type: file.type || 'application/pdf',
                        file_path: `file_${Date.now()}_${file.name}`, // 临时路径
                        create_at: now,
                        update_at: now
                    });
                    
                    // 保存文件内容到附件
                    await attachDoc.putAttachment({
                        id: aid,
                        data: file,
                        type: file.type || 'application/pdf',
                    });
                    
                    // 重新获取文档以避免版本冲突
                    const updatedDoc = await db.attachments.findOne({
                        selector: { id: aid }
                    }).exec();
                    
                    if (!updatedDoc) {
                        console.error(`无法找到更新后的文件记录: ${aid}`);
                        continue;
                    }
                    
                    // 创建 Blob URL
                    const URL = window.URL || window.webkitURL;
                    const blobUrl = URL.createObjectURL(file);
                    
                    // 更新文件路径为 Blob URL
                    await updatedDoc.update({
                        $set: {
                            file_path: blobUrl
                        }
                    });
                    
                    // 添加到结果列表
                    list.push({
                        aid,
                        url: blobUrl,
                        filename: file.name
                    });
                    
                } catch (fileError) {
                    console.error(`处理文件 ${file.name} 失败:`, fileError);
                    // 继续处理下一个文件
                }
            }
            
            return { list };
            
        } catch (error) {
            console.error('上传文件失败:', error);
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('上传文件失败');
            }
        }
    }
    
    /**
     * 获取指定父文件夹下的最大order值
     * 从 folderService 移植过来的辅助方法
     */
    private async getMaxOrderInParent(wid: string, parent_id: string): Promise<number> {
        const db = await getDatabase();
        
        const items = await db.attachments.find({
            selector: {
                wid,
                parent_id
            },
            sort: [{ order: 'desc' }],
            limit: 1
        }).exec();

        return items.length > 0 ? items[0].order : 0;
    }
    
    /**
     * 获取文件内容
     * 从文件记录中获取附件内容并创建 Blob URL
     */
    async getFileContent(aid: string): Promise<string> {
        try {
            const db = await getDatabase();
            
            const fileDoc = await db.attachments.findOne({
                selector: { id: aid }
            }).exec();
            
            if (!fileDoc) {
                throw new Error('文件不存在');
            }
            
            // 如果已有有效的 file_path 且是 Blob URL，直接返回
            if (fileDoc.file_path && fileDoc.file_path.startsWith('blob:')) {
                return fileDoc.file_path;
            }
            
            // 尝试获取附件数据
            try {
                const attachment = await fileDoc.getAttachment(aid);
                if (!attachment) {
                    throw new Error('附件不存在');
                }
                
                const data = await attachment.getData();
                if (!data) {
                    throw new Error('附件数据为空');
                }
                
                // 创建 Blob URL
                const URL = window.URL || window.webkitURL;
                const blobUrl = URL.createObjectURL(data);
                
                // 重新获取文档以避免版本冲突
                const updatedDoc = await db.attachments.findOne({
                    selector: { id: aid }
                }).exec();
                
                if (updatedDoc) {
                    await updatedDoc.update({
                        $set: {
                            file_path: blobUrl
                        }
                    });
                }
                
                return blobUrl;
                
            } catch (attachError) {
                console.error('获取附件数据失败:', attachError);
                
                // 如果有 file_path，返回现有路径
                if (fileDoc.file_path) {
                    return fileDoc.file_path;
                }
                
                throw new Error('无法获取文件内容');
            }
            
        } catch (error) {
            console.error('获取文件内容失败:', error);
            if (error instanceof Error) {
                throw error;
            } else {
                throw new Error('获取文件内容失败');
            }
        }
    }
    
    /**
     * 批量获取文件内容
     * 为多个文件获取附件内容并创建 Blob URL
     */
    async getFilesContent(aids: string[]): Promise<Map<string, string>> {
        const result = new Map<string, string>();
        
        for (const aid of aids) {
            try {
                const url = await this.getFileContent(aid);
                result.set(aid, url);
            } catch (error) {
                console.error(`获取文件 ${aid} 内容失败:`, error);
                // 继续处理下一个文件
            }
        }
        
        return result;
    }
} 
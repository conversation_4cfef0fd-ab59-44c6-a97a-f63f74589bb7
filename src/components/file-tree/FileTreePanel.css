/* react-complex-tree 基础样式覆盖 */
.rct-file-tree-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 深色主题 */
.rct-file-tree-panel.dark {
  background: #1e1e1e;
  border-color: #333;
  color: #cccccc;
}

/* 头部工具栏 */
.rct-file-tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  min-height: 40px;
}

.rct-file-tree-panel.dark .rct-file-tree-header {
  background: #252526;
  border-bottom-color: #333;
}

.rct-header-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.rct-file-tree-panel.dark .rct-header-title {
  color: #cccccc;
}

.rct-header-actions {
  display: flex;
  gap: 4px;
}

.rct-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
}

.rct-action-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.rct-action-button.active {
  background: #3b82f6;
  color: white;
}

.rct-file-tree-panel.dark .rct-action-button:hover {
  background: #333;
  color: #cccccc;
}

/* 搜索栏 */
.rct-search-container {
  position: relative;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
}

.rct-file-tree-panel.dark .rct-search-container {
  border-bottom-color: #333;
}

.rct-search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

.rct-search-input {
  width: 100%;
  padding: 6px 8px 6px 28px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  outline: none;
  background: white;
}

.rct-search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.rct-file-tree-panel.dark .rct-search-input {
  background: #2d2d30;
  border-color: #333;
  color: #cccccc;
}

.rct-file-tree-panel.dark .rct-search-input:focus {
  border-color: #007acc;
}

/* 文件树容器 */
.rct-tree-container {
  flex: 1;
  overflow: auto;
  padding: 4px 0;
}

/* react-complex-tree 组件样式覆盖 */
.rct-tree-item-li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.rct-tree-item-li-selected {
  background: rgba(59, 130, 246, 0.1);
}

.rct-tree-item-li-hidden {
  opacity: 0.6;
  font-style: italic;
}

.rct-tree-item-title-container {
  display: flex;
  align-items: center;
  min-height: 32px;
  padding: 2px 4px;
  margin: 1px 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
}

.rct-tree-item-title-container:hover {
  background: #f3f4f6;
}

.rct-tree-item-title-container-selected {
  background: #3b82f6;
  color: white;
}

.rct-file-tree-panel.dark .rct-tree-item-title-container:hover {
  background: #2d2d30;
}

.rct-file-tree-panel.dark .rct-tree-item-title-container-selected {
  background: #007acc;
}

.rct-tree-item-button {
  display: flex;
  align-items: center;
  width: 100%;
  border: none;
  background: transparent;
  padding: 0;
  font-size: 13px;
  text-align: left;
  cursor: pointer;
  color: inherit;
  gap: 6px;
}

.rct-tree-item-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.rct-tree-item-icon {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.rct-tree-item-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rct-tree-item-star {
  font-size: 12px;
  flex-shrink: 0;
}

.rct-tree-item-hidden {
  color: #9ca3af;
  font-size: 16px;
  line-height: 1;
  flex-shrink: 0;
}

/* 上下文菜单 */
.rct-context-menu {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 4px 0;
  min-width: 160px;
  z-index: 1000;
}

.rct-file-tree-panel.dark .rct-context-menu {
  background: #2d2d30;
  border-color: #333;
  color: #cccccc;
}

.rct-context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.rct-context-menu-item:hover {
  background: #f3f4f6;
}

.rct-file-tree-panel.dark .rct-context-menu-item:hover {
  background: #3e3e42;
}

.rct-context-menu-separator {
  margin: 4px 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

.rct-file-tree-panel.dark .rct-context-menu-separator {
  border-top-color: #333;
}

.rct-context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* react-complex-tree 默认样式覆盖 */
.rct-tree {
  font-family: inherit !important;
  color: inherit !important;
}

.rct-tree-item-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rct-tree-item-arrow svg {
  transition: transform 0.15s ease;
}

.rct-tree-item-arrow[data-expanded="true"] svg {
  transform: rotate(90deg);
}

/* 拖拽相关样式 */
.rct-tree-item-dragging {
  opacity: 0.5;
}

.rct-tree-item-drag-over {
  background: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
}

/* 滚动条样式 */
.rct-tree-container::-webkit-scrollbar {
  width: 6px;
}

.rct-tree-container::-webkit-scrollbar-track {
  background: transparent;
}

.rct-tree-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.rct-tree-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.rct-file-tree-panel.dark .rct-tree-container::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.rct-file-tree-panel.dark .rct-tree-container::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rct-file-tree-panel {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .rct-search-container {
    padding: 6px 8px;
  }
  
  .rct-file-tree-header {
    padding: 6px 8px;
  }
  
  .rct-tree-item-title-container {
    padding: 6px;
    margin: 1px 2px;
  }
}

/* 确保 react-complex-tree 的样式不冲突 */
.rct-tree ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.rct-tree li {
  margin: 0;
  padding: 0;
} 
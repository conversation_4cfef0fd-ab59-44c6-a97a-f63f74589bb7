import { useCallback } from "react";
import { usePanelOpenStore } from "@/store/panel-open-store.tsx";
import { useChatStore } from "@/store/workerspace-store/chat-store.ts";
import { getNodeHighlight, usePdfStore } from "@/store/pdf-store.ts";
import { useFlowStore } from "@/store/flow-store.ts";
import { message } from "antd";
import { ChatResponseRefs } from "@/components/chat-panel/types";
import { messageError } from "@/components/message/hooks";

// enum nodeType {
//     pdf
// }

const useNodeChat = () => {
    // 面板展示
    const { toggleChatPanel } = usePanelOpenStore((state) => ({
        toggleChatPanel: state.toggleChatPanel,
    }))
    // chat
    const { addFooterRef, updateCurrentChat, updateChatList } = useChatStore((state) => ({
        addFooterRef: state.addFooterRef,
        updateCurrentChat: state.updateCurrentChat,
        updateChatList: state.updateChatList,
    }))
    // AI聊天
    const handleChat = useCallback((id: string) => {
        // 打开聊天面板
        toggleChatPanel(true);
        const currentChatRid = useChatStore.getState().currentChatRid
        const footerRefs = useChatStore.getState().footerRefs
        const currentChat = useChatStore.getState().currentChat
        const nodeData = useFlowStore.getState().nodes.find((item) => item.id === id)!.data;
        const highlights = getNodeHighlight([id])
        const highlight = highlights.length > 0 ? highlights[0] : null
        
        const nodeRef = {
            id: id,
            title: nodeData.title ? nodeData.title + ".node" : "未命名.node",
            content: nodeData.content,
            type: 2
        }

        if (currentChatRid === -1) {
            if (footerRefs.length >= 10) {
                message.error("最多只能添加10个页脚引用");
                return;
            }
            const item = footerRefs.find((item) => item.id === nodeRef.id);
            const pdfItem = footerRefs.find(item => item.id === highlight?.aid);

            if (!item) {
                // 节点不在当前的集合中，且是高亮，需要添加引用信息
                if (!pdfItem && highlight) {
                    const nodeInfoWithRef = {
                        ...nodeRef,
                        refInfo: {
                            type: 1,
                            id: highlight.aid,
                            title: usePdfStore.getState().pdfs.get(highlight.aid)?.filename || "",
                            url: usePdfStore.getState().pdfs.get(highlight.aid)?.url,
                            content: ""
                        }
                    }
                    addFooterRef(nodeInfoWithRef);
                    return
                }

                addFooterRef(nodeRef)
            } else {
                messageError("重复引用!")
            }
        } else {
            const tempRefs: ChatResponseRefs = currentChat?.refs || [];
            if (tempRefs.length >= 10) {
                message.error("最多只能添加10个引用");
                return;
            }
            const item = tempRefs.find((item) => item.id === nodeRef.id);
            const pdfItem = tempRefs.find(item => item.id === highlight?.aid);
            if (!item) {
                // 追加附件
                let nodeWidthRef: any = {
                    ...nodeRef
                }
                if (!pdfItem && highlight) {
                    nodeWidthRef = {
                        ...nodeRef,
                        refInode: {
                            type: 1,
                            id: highlight.aid,
                            title: usePdfStore.getState().pdfs.get(highlight.aid)?.filename || "",
                            url: usePdfStore.getState().pdfs.get(highlight.aid)?.url,
                            content: "",
                        }
                    }
                }
                // 添加高亮
                tempRefs.push(nodeWidthRef);
                // 更新当前聊天和会话详情
                if (currentChat) {
                    currentChat.refs = tempRefs;
                    updateCurrentChat(currentChat);
                    updateChatList(currentChat);
                }
            } else {
                messageError("重复引用!")
            }
        }
    }, [toggleChatPanel, addFooterRef, updateCurrentChat, updateChatList]);
    return {
        handleChat
    }
}

export { useNodeChat }
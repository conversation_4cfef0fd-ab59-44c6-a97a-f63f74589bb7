import {useCallback} from "react";
import {usePanelOpenStore} from "@/store/panel-open-store.tsx";
import {useFlowStore} from "@/store/flow-store.ts";
import {getNodeHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {useNoteStore} from "@/store/note-store.ts";
import { messageError } from "@/components/message/hooks";

const useNodeNote = () => {
    // 面板展示
    const {toggleNotePanel} = usePanelOpenStore((state) => ({
        toggleNotePanel: state.toggleNotePanel,
    }))
    // note
    const {setInsertContent} = useNoteStore((state) => ({
        setInsertContent: state.setInsertContent
    }))
    // 记事本
    const handleCopyToNotebook = useCallback(async (ids: string[]) => {
        if (ids.length === 0) {
            return
        }
        // 显示笔记本面板
        const currentNoteState = usePanelOpenStore.getState().notePanelOpen
        if (!currentNoteState) {
            toggleNotePanel(true)
        }
        const nodes = useFlowStore.getState().nodes.filter(node => ids.includes(node.id))
        const highlights = getNodeHighlight(ids)
        const highlightsMap = new Map(highlights.map(highlight => [highlight.nid, highlight]))
        let content = ''

        const hasContent = nodes.some(node => !!node.data.content || !!node.data.title)
        if (!hasContent) {
            messageError('当前节点为空')
            return
        }

        nodes.forEach(node => {
            const nodeTitle = node.data.title?.trim()
            const nodeContent = node.data.content?.trim()
            if (nodeTitle && nodeTitle.length > 0) {
                content += `\n## ${nodeTitle}\n`
            }
            if (nodeContent && nodeContent.length > 0) {
                content += `\n${nodeContent}\n `
            }
            const highlight = highlightsMap.get(node.id)
            if (!highlight) {
                content += `\n (来源: 引用自节点) `
                return
            }
            const pdfFilename = usePdfStore.getState().pdfs.get(highlight.aid)?.filename || '';
            const pageNumber = highlight.position.boundingRect?.pageNumber;
            if (pageNumber) {
            content += `（来源：《${pdfFilename}》 第${pageNumber}页 ）`
            } else {
                content += `（来源：《${pdfFilename}》）`
            }
        })
        setInsertContent(content)
    }, [toggleNotePanel, setInsertContent])
    return {
        handleCopyToNotebook
    }
}

export {useNodeNote}
import {RxJsonSchema} from 'rxdb';

export interface TagGroupDocType {
    id: string; // 标签组ID
    title: string; // 标签组标题
    sort_order: number; // 排序顺序
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const tagGroupSchema: RxJsonSchema<TagGroupDocType> = {
    title: 'tagGroup schema',
    version: 0,
    description: '标签组模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        title: {
            type: 'string',
            maxLength: 200
        },
        sort_order: {
            type: 'number',
            minimum: 0,
            default: 0
        },
        create_at: {
            type: 'number',
            minimum: 0
        },
        update_at: {
            type: 'number',
            minimum: 0
        }
    },
    required: ['id', 'title', 'sort_order'],
    indexes: ['sort_order']
}; 
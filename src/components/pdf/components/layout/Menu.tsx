import {FileOutlined, FolderAddOutlined, HomeOutlined, SearchOutlined, UploadOutlined} from "@ant-design/icons";
import {IconFontPdf} from "@/common/constant.ts";
import {comparePdfBasicInfo, selectPdfBasicInfo, usePdfStore} from "@/store/pdf-store.ts";
import {useCallback, useEffect, useRef, useState} from "react";
import {usePdfUpload} from "@/components/pdf/hooks/pdf-upload.ts";
import {useMenu} from "@/components/pdf/hooks/menu.tsx";
import {DeletePdf} from "@/components/pdf/components/popup/DeletePdf.tsx";
import {HeadlessFileTreePanel} from "@/components/file-tree";
import "@/components/file-tree/HeadlessFileTreePanel.css";

// 导入自定义 hooks
import {useFileManager} from "@/components/pdf/hooks/use-file-manager";
import {useFileTree} from "@/components/pdf/hooks/use-file-tree";
import {useFileOperations} from "@/components/pdf/hooks/use-file-operations";
import {useResize} from "@/components/pdf/hooks/use-resize";

export const Menu = () => {
    const pdfs = usePdfStore(selectPdfBasicInfo, comparePdfBasicInfo);
    const {activeAid, showMenu} = usePdfStore((state) => ({
        activeAid: state.activeAid,
        showMenu: state.showMenu,
    }));
    
    // 删除弹窗状态
    const [openConfirmModal, setOpenConfirmModal] = useState(false);
    const [deleteAid, setDeleteAid] = useState("");
    const [deleteNode, setDeleteNode] = useState(false);
    const [deleting, setDeleting] = useState(false);
    

    
    // 搜索状态
    const [searchString, setSearchString] = useState('');
    
    // 使用自定义 hooks
    const {
        folders,
        files,
        dbReady,
        dataLoading,
        setDataLoading,
        currentWorkspaceId,
        targetFolderId,
        setTargetFolderId,
        loadData,
        addFiles,
        addFolder,
        removeFolder,
        removeFile
    } = useFileManager();
    
    // 显示当前选中的文件夹
    const selectedFolderName = useCallback(() => {
        if (targetFolderId === 'root') {
            return '所有文档';
        }
        const folder = folders.find(f => f.id === targetFolderId);
        return folder ? folder.file_name : '所有文档';
    }, [targetFolderId, folders]);
    
    // 获取文件夹路径
    const getFolderPath = useCallback((folderId: string): {id: string, name: string}[] => {
        const path: {id: string, name: string}[] = [];
        
        // 添加根目录
        if (folderId === 'root') {
            return [{id: 'root', name: '所有文档'}];
        }
        
        // 查找当前文件夹
        let currentFolder = folders.find(f => f.id === folderId);
        if (!currentFolder) {
            return [{id: 'root', name: '所有文档'}];
        }
        
        // 添加当前文件夹
        path.unshift({id: currentFolder.id, name: currentFolder.file_name});
        
        // 查找父文件夹
        let parentId = currentFolder.parent_id;
        while (parentId && parentId !== 'root') {
            const parent = folders.find(f => f.id === parentId);
            if (!parent) break;
            
            path.unshift({id: parent.id, name: parent.file_name});
            parentId = parent.parent_id;
        }
        
        // 添加根目录
        path.unshift({id: 'root', name: '所有文档'});
        
        return path;
    }, [folders]);
    
    // 使用文件树 hook
    const fileTreeData = useFileTree(folders, files, pdfs);
    
    // 包装loadData函数，确保UI更新
    const handleLoadData = useCallback(async () => {
        await loadData();
        // 不再强制重新渲染，让组件自己处理状态更新
    }, [loadData]);
    
    // 更新文件树（不再强制重新渲染）
    const updateFileTree = useCallback(() => {
        // 触发数据重新加载，但不强制重新渲染组件
        loadData();
    }, [loadData]);
    
    // 使用文件操作 hook
    const {
        handleFileSelect: originalHandleFileSelect,
        handleFileCreate,
        handleFileDelete,
        handleFileRename
    } = useFileOperations(
        currentWorkspaceId, 
        handleLoadData, 
        setTargetFolderId,
        updateFileTree,
        addFolder,
        removeFolder,
        removeFile,
        folders // 传递folders参数
    );
    
    // 包装handleFileSelect，处理刷新请求
    const handleFileSelect = useCallback((node: any) => {
        // 检查是否是刷新请求
        if (node.id === 'refresh' && node.data?.refresh) {
            console.log('收到刷新文件树请求');
            // 强制重新加载数据以确保文件树是最新的
            handleLoadData().catch(err => console.error('刷新数据失败:', err));
            return;
        }
        
        // 否则调用原始的处理函数
        originalHandleFileSelect(node);
    }, [originalHandleFileSelect, handleLoadData]);
    
    // 使用菜单操作 hook
    const {onMenuClick, onMenuDelete} = useMenu();
    
    // 使用拖拽调整宽度的 hook
    const {width: menuWidth, handleMouseDown: handleMouseDownResize} = useResize();
    
    // 上传相关
    const {uploadFiles} = usePdfUpload();
    const inputRef = useRef<HTMLInputElement>(null);

    const handleUpload = useCallback(() => {
        if (inputRef.current) {
            inputRef.current.click();
        }
    }, []);
    
    // 创建新文件夹
    const handleCreateFolder = useCallback(() => {
        // 不再直接调用handleFileCreate
        // 而是让HeadlessFileTreePanel组件处理新建文件夹的交互
        // 这里我们需要触发一个事件，让HeadlessFileTreePanel组件知道需要创建文件夹
        // 但由于我们没有直接访问HeadlessFileTreePanel组件内部状态的方法
        // 所以我们可以通过右键菜单的方式来实现
        
        // 创建一个自定义事件，通知HeadlessFileTreePanel组件开始创建文件夹
        const event = new CustomEvent('ht-create-folder', { 
            detail: { 
                path: targetFolderId === 'root' ? '/所有文档' : `/${selectedFolderName()}`,
                parentId: targetFolderId // 直接传递目标文件夹ID
            } 
        });
        document.dispatchEvent(event);
    }, [targetFolderId, selectedFolderName]);

    // 监听文件删除，清理相关状态
    const previousPdfsSize = useRef(pdfs.size);
    useEffect(() => {
        if (pdfs.size < previousPdfsSize.current) {
            if (deleteAid && !pdfs.has(deleteAid)) {
                setDeleteAid("");
                setDeleteNode(false);
            }
        }
        previousPdfsSize.current = pdfs.size;
    }, [pdfs.size, deleteAid]);

    // 处理面包屑导航点击
    const handleBreadcrumbClick = useCallback((folderId: string) => {
        setTargetFolderId(folderId);
    }, [setTargetFolderId]);

    // 获取当前文件夹路径
    const folderPath = getFolderPath(targetFolderId);

    // 处理搜索
    const handleSearch = useCallback((value: string) => {
        setSearchString(value);
        // 这里可以添加搜索逻辑，如果需要的话
    }, []);

    return (
        <div className="bg-[#E0E2F4] h-[100%] relative" 
            style={{
                width: `${menuWidth}px`, 
                display: showMenu ? "block" : "none"
            }}
        >
            <div className="h-full flex flex-col">
                <div className="w-full p-3 flex justify-between items-center border-b border-gray-300">
                    <span className="font-bold">PDF文件管理器</span>
                </div>
                
                {/* 搜索框 */}
                <div className="px-3 py-2 border-b border-gray-200">
                    <div className="relative">
                        <input
                            type="text"
                            placeholder="搜索文件..."
                            value={searchString}
                            onChange={(e) => handleSearch(e.target.value)}
                            className="w-full pl-8 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <SearchOutlined className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="px-3 py-2 flex justify-end border-b border-gray-200">
                    <div className="flex">
                        <button 
                            className="p-1 rounded-md hover:bg-white hover:bg-opacity-50 transition-colors"
                            onClick={handleCreateFolder}
                            title="新建文件夹"
                        >
                            <FolderAddOutlined className="text-[#3D56BA]" />
                        </button>
                        <button 
                            className="p-1 ml-1 rounded-md hover:bg-white hover:bg-opacity-50 transition-colors"
                            onClick={handleUpload}
                            title="上传文件"
                        >
                            <UploadOutlined className="text-[#3D56BA]" />
                        </button>
                    </div>
                </div>
                
                <div className="flex-1 overflow-hidden">
                    {!dbReady ? (
                        <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
                            <span className="text-sm">正在初始化数据库...</span>
                        </div>
                    ) : dataLoading ? (
                        <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
                            <span className="text-sm">正在加载文件数据...</span>
                        </div>
                    ) : !currentWorkspaceId ? (
                        <div className="flex flex-col items-center justify-center h-32 text-gray-500 p-4">
                            <FileOutlined className="text-2xl mb-2" />
                            <span className="text-sm">未找到工作区ID</span>
                            <div className="text-xs mt-2 text-center">
                                请确保URL中包含wid参数
                            </div>
                        </div>
                    ) : fileTreeData.length > 0 ? (
                            <HeadlessFileTreePanel
                                key={`file-tree-${files.length}-${folders.length}`} 
                                initialData={fileTreeData}
                                onFileSelect={handleFileSelect}
                                onFileCreate={handleFileCreate}
                                onFileDelete={(path) => handleFileDelete(path, folders, pdfs)}
                                onFileRename={(oldPath, newPath) => handleFileRename(oldPath, newPath, folders)}
                                showHiddenFiles={false}
                                searchable={false} // 禁用内部搜索，使用外部搜索
                                className="h-full"
                                searchTerm={searchString} // 传递搜索词到组件
                                currentWorkspaceId={currentWorkspaceId} // 传递工作区ID
                            />
                    ) : (
                        <div className="flex flex-col items-center justify-center h-32 text-gray-500 p-4">
                            <FileOutlined className="text-2xl mb-2" />
                            <span className="text-sm">暂无PDF文件</span>
                            <div className="text-xs mt-2 text-center space-y-1">
                                <div>💡 使用提示：</div>
                                <div>• 右键空白区域创建文件夹</div>
                                <div>• 点击右上角按钮上传PDF</div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="border-t border-gray-300 p-3">
                    <div className="w-full flex px-3 py-2 gap-2 items-center cursor-pointer text-[#3D56BA] hover:bg-white hover:bg-opacity-50 rounded transition-colors"
                         onClick={handleUpload}>
                        <IconFontPdf type="icon-plus"/>
                        <span>上传PDF文件 {targetFolderId !== 'root' && `到 "${selectedFolderName()}"`}</span>
                    </div>
                    <input 
                        ref={inputRef} 
                        type="file" 
                        className="hidden" 
                        accept="application/pdf"
                        onChange={async (e) => {
                            if (e.target.files && e.target.files.length > 0) {
                                try {
                                    setDataLoading(true);
                                    
                                    // 使用增量更新方式上传文件
                                    const result = await uploadFiles(e.target.files, targetFolderId, addFiles);
                                    
                                    if (result.success) {
                                        // 上传成功后，由于使用了增量更新，不需要强制刷新
                                        // 文件树会自动重新渲染
                                        console.log('文件上传成功，文件树将自动更新');
                                    } else {
                                        alert(`上传失败: ${result.error}`);
                                    }
                                } catch (error) {
                                    console.error('上传失败:', error);
                                    alert('上传失败，请重试');
                                } finally {
                                    setDataLoading(false);
                                    e.target.value = '';
                                }
                            }
                        }} 
                        multiple
                    />
                </div>
            </div>

            <div
                className="w-1 absolute top-0 bottom-0 cursor-col-resize bg-transparent z-[100] hover:bg-[#1890ff] active:bg-opacity-25"
                style={{left: `${menuWidth - 2}px`}}
                onMouseDown={handleMouseDownResize}
            />
            
            <DeletePdf 
                isModalOpen={openConfirmModal} 
                onChange={setDeleteNode}
                loading={deleting}
                handleCancel={() => {
                    if (!deleting) {
                        setOpenConfirmModal(false);
                    }
                }}
                handleOk={async () => {
                    if (deleting) return;
                    
                    setDeleting(true);
                    try {
                        // 从PDF store中移除文件
                        usePdfStore.getState().removePdfs(deleteAid);
                        
                        // 从文件列表中移除
                        removeFile(deleteAid);
                        
                        // 调用API删除
                        await onMenuDelete(deleteAid, deleteNode);
                        
                        setOpenConfirmModal(false);
                        setDeleteAid("");
                        setDeleteNode(false);
                    } catch (error) {
                        console.error('删除失败:', error);
                    } finally {
                        setDeleting(false);
                    }
                }}
            />
        </div>
    );
};
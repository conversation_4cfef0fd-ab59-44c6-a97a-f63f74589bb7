import {getOssSignatureForImage, uploadPreview} from "@/api/image";
import {ChatPanel} from "@/components/chat-panel";
import FlowPanel from "@/components/flow/FlowPanel";
import {Sidebar, SidebarEventType} from "@/components/flow/Sidebar";
import {NotePanelMain} from "@/components/note/NotePanel";
import {PdfPanelMain} from "@/components/pdf/PdfPanel.tsx";
import {usePanelOpenStore} from "@/store/panel-open-store";
import {useFlowStore} from "@/store/workerspace-store/flow-store";
import {useHighlighterStore} from "@/store/workerspace-store/highlighter-store";
import {useWorkerSpaceStore} from "@/store/workerspace-store/store";
import {message} from "antd";
import * as htmlToImage from 'html-to-image';
import React, {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import 'react-resizable/css/styles.css';
import {useLocation} from "react-router-dom";
import styled from "styled-components";
import {PanelPosition, SnapPreview} from './Panel';
import {ResizeDivider} from "@/components/ui/ResizeDivider.tsx";
import {TagPanel} from "@/components/tag-management";
import {GlobalWindowManager} from "@/components/pdf/components/draggable-tabs/GlobalWindowManager.tsx";

const OuterContainer = styled.div`
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: row;
`;

const PanelContainer = styled.div`
    flex: 1;
    min-width: 800px;
    height: 100vh;
    position: relative;
    overflow: hidden;
`;

// 控制面板
const ControlPanelContainer = styled.div`
    position: fixed;
    left: 10px;
    bottom: 10px;
    width: 50px;
    height: 50px;
    border-radius: 30px;
    font-size: 10px;
    cursor: pointer;
    z-index: 1000;
    border: 2px solid transparent;
    background-image: linear-gradient(to bottom, #CCD4F7 0%, #CDD2E9 100%),
    linear-gradient(to bottom, #3D56BA 4%, #AAB6E4 62%);
    background-origin: padding-box, border-box;
    background-clip: padding-box, border-box;
`;

// 添加一个FlowPanelContainer来包裹FlowPanel
// const FlowPanelContainer = styled.div`
//   position: relative;
//   width: 100%;
//   height: 100%;
//   z-index: 1;
// `;

// 添加半透明蒙版组件
const SnapPreviewOverlay = styled.div<{
    position: { x: number; y: number; width: number; height: number };
}>`
    position: absolute;
    left: ${(props) => props.position.x}px;
    top: ${(props) => props.position.y}px;
    width: ${(props) => props.position.width}px;
    height: ${(props) => props.position.height}px;
    background-color: rgba(0, 120, 215, 0.3);
    border: 2px solid rgba(0, 120, 215, 0.8);
    border-radius: 8px;
    z-index: 999;
    pointer-events: none; // 确保蒙版不会影响鼠标事件
    transition: all 0.1s ease-out;
`;

// 1. 将截图逻辑独立出来
const captureAndUploadSnapshot = async (
    workerspaceRef: HTMLDivElement | null,
    wid: string | null,
) => {
    if (!workerspaceRef || !wid) return;

    try {
        const blob = await htmlToImage.toBlob(workerspaceRef, {
            onImageErrorHandler: (error) => {
                console.log('Error converting image:', error);
            },
        });

        if (!blob) {
            throw new Error("Failed to create image blob");
        }

        // 上传图片
        const res = await uploadPreview({
            wid,
            file: blob,
        });

        if ((res as any).code === 0) {
            console.log("预览图上传成功" + new Date(Date.now()).toLocaleString());
        }

        // 6. 创建文件
        // const file = new File([blob], `workspace_${Date.now()}.png`, {
        //   type: "image/png",
        // });
        //
        // // 7. 获取签名并上传
        // const imageOssSignature = localStorage.getItem("image_oss_signature");
        // if (!imageOssSignature) return;
        //
        // const policy = JSON.parse(imageOssSignature);
        // const fileName = policy.dir + "/" + crypto.randomUUID() + ".png";
        //
        // // 8. 上传到 OSS
        // await uploadImageToOss({
        //   fileName,
        //   file,
        //   ossInfo: policy,
        // });

    } catch (error) {
        console.error("截图失败:", error);
    }
};

/**
 * 创建增强版截图功能:
 * 1. 用户活动检测 - 只在用户停止操作后才执行截图，避免影响用户体验
 * 2. 浏览器空闲调度 - 使用requestIdleCallback在浏览器空闲时执行截图，降低对性能的影响
 * 3. 节流控制 - 设置最小截图时间间隔，避免过于频繁的截图
 * 4. 自动清理 - 提供完整的资源清理机制
 */
const createEnhancedCaptureAndUpload = (workerspaceRefGetter: () => HTMLDivElement | null, widGetter: () => string | null) => {
    // 用户活动状态
    let isUserActive = false;
    let userActivityTimeout: ReturnType<typeof setTimeout> | null = null;
    let lastCaptureTime = 0;
    const inactivityThreshold = 10000; // 10秒无活动视为不活跃
    const captureInterval = 20000; // 两次截图之间的最小间隔
    let idleCallbackId: number | ReturnType<typeof setTimeout> | null = null;
    let pendingCapture = false; // 同时表示是否有待处理的截图请求和是否正在进行截图
    let isIdleCallbackSupported = 'requestIdleCallback' in window;

    // 用户活动监测
    const setupUserActivityDetection = () => {
        const userActivityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];

        // 用户活动处理函数
        const handleUserActivity = () => {
            isUserActive = true;

            // 清除之前的超时
            if (userActivityTimeout) {
                clearTimeout(userActivityTimeout);
            }

            // 设置新的超时，用户停止活动后将状态设为不活跃
            userActivityTimeout = setTimeout(() => {
                isUserActive = false;
                // 如果之前有请求未执行，并且当前没有正在进行的截图，尝试执行
                if (pendingCapture) {
                    const now = Date.now();
                    const intervalElapsed = now - lastCaptureTime > captureInterval;
                    if (intervalElapsed) {
                        scheduleCaptureWhenIdle();
                    }
                }
            }, inactivityThreshold);
        };

        // 添加所有活动事件监听
        userActivityEvents.forEach(eventType => {
            window.addEventListener(eventType, handleUserActivity, {passive: true});
        });

        // 初始化时设为活跃状态
        handleUserActivity();

        // 返回移除事件监听的函数
        return () => {
            userActivityEvents.forEach(eventType => {
                window.removeEventListener(eventType, handleUserActivity);
            });
            cleanupCallbacks();
        };
    };

    // 在浏览器空闲时执行截图
    const scheduleCaptureWhenIdle = () => {
        // 如果已经有排队的请求，不再添加新的
        if (idleCallbackId !== null || pendingCapture) return;

        // 根据浏览器支持选择合适的API
        if (isIdleCallbackSupported) {
            idleCallbackId = (window as any).requestIdleCallback(
                () => {
                    idleCallbackId = null;
                    executeCapture();
                },
                {timeout: 2000} // 最多等待2秒
            );
        } else {
            // 降级方案：使用setTimeout
            idleCallbackId = setTimeout(() => {
                idleCallbackId = null;
                executeCapture();
            }, 100);
        }
    };

    // 执行截图
    const executeCapture = async () => {
        // 如果已经在执行截图，直接返回
        if (pendingCapture) {
            return;
        }

        pendingCapture = true; // 标记为正在执行截图
        const currentRef = workerspaceRefGetter();
        const currentWid = widGetter();

        if (currentRef && currentWid) {
            // 执行截图
            await captureAndUploadSnapshot(currentRef, currentWid);

            lastCaptureTime = Date.now();
        }
        pendingCapture = false;
    };

    // 清理定时器和回调
    const cleanupCallbacks = () => {
        if (idleCallbackId !== null) {
            if (isIdleCallbackSupported) {
                (window as any).cancelIdleCallback(idleCallbackId);
            } else {
                clearTimeout(idleCallbackId);
            }
            idleCallbackId = null;
        }

        if (userActivityTimeout !== null) {
            clearTimeout(userActivityTimeout);
            userActivityTimeout = null;
        }
    };

    // 初始化用户活动检测，返回清理函数
    const cleanup = setupUserActivityDetection();

    // 暴露给外部的截图函数
    return {
        captureAndUpload: (force = false) => {
            // 如果强制执行，忽略所有条件
            if (force) {
                const currentRef = workerspaceRefGetter();
                const currentWid = widGetter();
                return captureAndUploadSnapshot(currentRef, currentWid);
            }

            // 如果正在执行中，直接返回
            if (pendingCapture) {
                return;
            }

            // 检查是否达到执行条件
            const now = Date.now();
            const intervalElapsed = now - lastCaptureTime > captureInterval;
            const currentRef = workerspaceRefGetter();
            const currentWid = widGetter();
            if (!currentRef || !currentWid) return;

            // 判断是否可以立即执行
            if (!isUserActive && intervalElapsed) {
                scheduleCaptureWhenIdle();
            }
            // 如果条件不满足，等待用户变为不活跃时自动检查条件
        },
        cleanup: () => {
            cleanup();
            cleanupCallbacks();
        }
    };
};


enum panel {
    note = 'note',
    pdf = "pdf",
    chat = 'chat'
}

type panelTypes = keyof typeof panel
const SIDEBAR_WIDTH = 64
export const WorkerSpace = memo(() => {
    const wid = useWorkerSpaceStore(state => state.wid);
    const setWid = useWorkerSpaceStore(state => state.setWid);
    const workerspaceRef = useWorkerSpaceStore(state => state.workerspaceRef);
    const setWorkerspaceRef = useWorkerSpaceStore(state => state.setWorkerspaceRef);
    const wRef = useRef<HTMLDivElement>(null);
    const [isWelt, setIsWelt] = useState(false)

    const [showButtons, setShowButtons] = useState(false);
    const {
        notePanelOpen,
        pdfPanelOpen,
        chatPanelOpen,
        tagPanelOpen,
        togglePdfPanel,
        toggleChatPanel,
        toggleNotePanel,
        toggleTagPanel
    } = usePanelOpenStore();
    const location = useLocation();
    // 添加一个 ref 来跟踪当前的 wid
    const currentWid = useRef<string | null>(null);

    // 获取签名
    useEffect(() => {
        // 获取签名并保存到localStorage
        const fetchAndSaveSignatures = async () => {
            try {
                // 获取图片签名
                const imageOssSignature = await getOssSignatureForImage();
                if ((imageOssSignature as any).code === 0) {
                    localStorage.setItem(
                        "image_oss_signature",
                        imageOssSignature.data.policy
                    );
                }

            } catch (error) {
                console.error("获取签名失败:", error);
                message.error("获取上传签名失败");
            }
        };
        fetchAndSaveSignatures();
        // 设置定时器，每小时更新一次签名
        const intervalId = setInterval(fetchAndSaveSignatures, 60 * 50 * 1000);

        return () => clearInterval(intervalId);
    }, []);

    // 获取工作区ID
    useEffect(() => {
        // 使用 URLSearchParams 更安全地获取参数
        const searchParams = new URLSearchParams(location.search);
        const newWid = searchParams.get("wid");

        // 只有当 wid 发生变化时才执行清理和设置操作
        if (newWid && newWid !== currentWid.current) {
            // 1. 先清除旧数据
            useFlowStore.getState().clearNodes();
            useHighlighterStore.getState().clearHighlights();

            // 2. 更新 ref
            currentWid.current = newWid;

            // 3. 设置新的 wid
            setWid(newWid);
        }

        if (wRef.current) {
            setWorkerspaceRef(wRef.current);
        }

        // 组件卸载时的清理函数
        return () => {
            // 清除所有状态
            useFlowStore.getState().clearNodes();
            useHighlighterStore.getState().clearHighlights();
            setWid(null);
            currentWid.current = null;
            if (wRef.current) {
                setWorkerspaceRef(null);
            }
        };
    }, [location.search, setWid, setWorkerspaceRef]);

    // 创建增强版截图功能
    const screenshotManager = useRef<ReturnType<typeof createEnhancedCaptureAndUpload> | null>(null);

    // 每30s截图一次，但只在用户不活跃时
    useEffect(() => {
        // 初始化截图管理器
        if (!screenshotManager.current) {
            screenshotManager.current = createEnhancedCaptureAndUpload(
                () => workerspaceRef, // 获取当前的workerspaceRef
                () => wid // 获取当前的wid
            );
        }

        // 设置定时器
        const intervalId = setInterval(() => {
            if (wid && workerspaceRef && screenshotManager.current) {
                screenshotManager.current.captureAndUpload();
            }
        }, 30000);

        return () => {
            if (screenshotManager.current) {
                screenshotManager.current.cleanup();
                screenshotManager.current = null;
            }
            // 组件卸载时的清理函数
            clearInterval(intervalId);
        };
    }, [wid, workerspaceRef]);

    const handleButtonClick = useCallback(
        (type: string) => {
            switch (type) {
                case "pdf":
                    togglePdfPanel(!pdfPanelOpen);
                    break;
                case "chat":
                    toggleChatPanel(!chatPanelOpen);
                    break;
                case "notepad":
                    toggleNotePanel(!notePanelOpen);
                    break;
            }
        },
        [
            pdfPanelOpen,
            chatPanelOpen,
            notePanelOpen,
            togglePdfPanel,
            toggleChatPanel,
            toggleNotePanel,
        ]
    );
    // 为每个面板添加状态管理
    const [notePanelSize, setNotePanelSize] = useState({
        width: window.innerWidth / 3, // 宽度为屏幕的1/3
        height: window.innerHeight, // 高度为整个屏幕高度
    });
    const [pdfPanelSize, setPdfPanelSize] = useState({
        width: window.innerWidth / 3,
        height: window.innerHeight
    });
    const [chatPanelSize, setChatPanelSize] = useState({
        width: window.innerWidth / 4, // 宽度为屏幕的1/4
        height: window.innerHeight, // 高度为整个屏幕高度
    });

    // 添加面板位置状态
    const [notePanelPosition, setNotePanelPosition] = useState<PanelPosition>({
        x: 0, // 从左侧开始
        y: 0, // 从顶部开始
        width: window.innerWidth / 3, // 宽度为屏幕的1/3
        height: window.innerHeight, // 高度为整个屏幕高度
        isSnapped: false, // 设置为已贴边状态
        resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'],
    });
    const [pdfPanelPosition, setPdfPanelPosition] = useState<PanelPosition>({
        x: (window.innerWidth - SIDEBAR_WIDTH) * 2 / 3, // 设置为右半屏起始位置
        y: 0, // 从顶部开始
        width: (window.innerWidth - SIDEBAR_WIDTH) / 3, // 宽度为屏幕一半
        height: window.innerHeight, // 高度为整个屏幕高度
        isSnapped: false, // 设置为已贴边状态
        resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'],
    });
    const [chatPanelPosition, setChatPanelPosition] = useState<PanelPosition>({
        x: (window.innerWidth - SIDEBAR_WIDTH) * 0.5, // 位置在屏幕宽度的3/4处
        y: 0, // 从顶部开始
        width: (window.innerWidth - SIDEBAR_WIDTH) / 4, // 宽度为屏幕的1/4
        height: window.innerHeight, // 高度为整个屏幕高度
        isSnapped: true, // 设置为已贴边状态
        resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'],
    });
    const [flowPanelPosition, setFlowPanelPosition] = useState<PanelPosition>({
        x: 0,
        y: 0,
        width: window.innerWidth - 64,
        height: 0,
        isSnapped: true
    })
    const [dragPanel, setDragPanel] = useState("")
    // 添加贴边预览状态
    const [snapPreview, setSnapPreview] = useState<SnapPreview>({
        visible: false,
        edge: null,
        previewPosition: {x: 0, y: 0, width: 0, height: 0},
        snapPosition: {x: 0, y: 0, width: 0, height: 0},
        opacity: 1,
        // position: { x: 0, y: 0, width: 0, height: 0 }
    });
    // 窗口大小变化时更新位置
    useEffect(() => {
        const handleResize = () => {
            // 更新右侧面板的位置
            if (!pdfPanelPosition.isSnapped) {
                setPdfPanelPosition((prev) => ({
                    ...prev,
                    x: window.innerWidth - 420,
                }));

            }
            if (!chatPanelPosition.isSnapped) {
                setChatPanelPosition((prev) => ({
                    ...prev,
                    x: window.innerWidth - 420,
                    y: window.innerHeight - 520,
                }));
            }
        };

        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [pdfPanelPosition.isSnapped, chatPanelPosition.isSnapped]);

    // 添加z-index状态管理
    const [zIndexes, setZIndexes] = useState({
        note: 10,
        pdf: 10,
        chat: 10
    });

    // 提升面板层级的函数
    const bringToFront = (panelType: panelTypes) => {
        setZIndexes(prev => {
            const highestZ = Math.max(prev.note, prev.pdf, prev.chat);
            return {
                ...prev,
                [panelType]: highestZ + 1
            };
        });
    };

    // 修改点击处理函数，使面板在点击时提升到最上层
    const handlePanelClick = (type: panelTypes) => {
        bringToFront(type)
    }

    // 添加监听面板打开状态的useEffect
    useEffect(() => {
        // 当笔记面板打开时，提高其z-index
        if (notePanelOpen) {
            bringToFront(panel.note);
        }
    }, [notePanelOpen]);

    useEffect(() => {
        // 当PDF面板打开时，提高其z-index
        if (pdfPanelOpen) {
            bringToFront(panel.pdf);
        }
    }, [pdfPanelOpen]);

    useEffect(() => {
        // 当聊天面板打开时，提高其z-index
        if (chatPanelOpen) {
            bringToFront(panel.chat);
        }
    }, [chatPanelOpen]);

    // 添加重置面板状态的函数
    const resetPanelStates = useCallback(() => {
        // 重置面板打开状态
        togglePdfPanel(true); // PDF面板默认打开
        toggleNotePanel(false);
        toggleChatPanel(false);
        toggleTagPanel(false);

        // 重置面板大小
        setNotePanelSize({
            width: window.innerWidth / 3,
            height: window.innerHeight,
        });
        setPdfPanelSize({
            width: window.innerWidth / 3,
            height: window.innerHeight
        });
        setChatPanelSize({
            width: window.innerWidth / 4, // 宽度为屏幕的1/4
            height: window.innerHeight, // 高度为整个屏幕高度
        });

        // 重置面板位置
        setNotePanelPosition({
            x: 10,
            y: 0,
            width: window.innerWidth / 3,
            height: window.innerHeight,
            isSnapped: false,
        });
        setPdfPanelPosition({
            x: window.innerWidth * 2 / 3,
            y: 0,
            width: window.innerWidth / 3,
            height: window.innerHeight,
            isSnapped: false,
        });
        setChatPanelPosition({
            x: window.innerWidth * 3 / 4, // 位置在屏幕宽度的3/4处
            y: 0, // 从顶部开始
            width: window.innerWidth / 4, // 宽度为屏幕的1/4
            height: window.innerHeight, // 高度为整个屏幕高度
            isSnapped: false, // 设置为已贴边状态
        });

        // 重置z-index
        setZIndexes({
            note: 10,
            pdf: 11, // PDF面板默认在最上层
            chat: 10
        });

        // 重置贴边预览状态
        setSnapPreview({
            visible: false,
            edge: null,
            previewPosition: {x: 0, y: 0, width: 0, height: 0},
            snapPosition: {x: 0, y: 0, width: 0, height: 0},
            opacity: 1
        });
    }, [
        togglePdfPanel,
        toggleNotePanel,
        toggleChatPanel,
        setNotePanelSize,
        setPdfPanelSize,
        setChatPanelSize,
        setNotePanelPosition,
        setPdfPanelPosition,
        setChatPanelPosition,
        setZIndexes,
        setSnapPreview
    ]);

    // 添加在页面卸载时重置面板状态的useEffect
    useEffect(() => {
        // 返回清理函数，在组件卸载时执行
        return () => {
            resetPanelStates();
        };
    }, [resetPanelStates]);

    // 添加导航到home的函数
    const navigateToHome = useCallback(() => {
        // 如果记事本面板打开，先处理可能的未保存内容
        if (notePanelOpen) {
            // 触发记事本关闭事件，让记事本组件处理保存逻辑
            const homeNavRef = {current: true}; // 创建一个引用来标记是home导航

            // 添加一次性事件监听器，在记事本关闭后再导航到home
            const handleNotepadClose = () => {
                window.removeEventListener('notepad-close', handleNotepadClose);
                window.location.href = "/home"; // 使用直接的URL导航确保在处理完后跳转
            };

            window.addEventListener('notepad-close', handleNotepadClose);

            // 触发记事本的关闭事件，这将启动保存检查流程
            const closeEvent = new CustomEvent('vditor-close');
            window.dispatchEvent(closeEvent);
        } else {
            // 如果记事本没有打开，直接导航
            window.location.href = "/home";
        }
    }, [notePanelOpen]);

    // 添加事件监听（恢复原始的事件监听）
    useEffect(() => {
        const handleNotepadClose = () => {
            toggleNotePanel(false);
        };

        window.addEventListener('notepad-close', handleNotepadClose);
        return () => {
            window.removeEventListener('notepad-close', handleNotepadClose);
        };
    }, [toggleNotePanel]);

    // 新增：当前激活的面板
    const [activePanel, setActivePanel] = useState<SidebarEventType>('home');

    // 侧边栏切换逻辑
    const handleSidebarSelect = useCallback((type: SidebarEventType) => {
        setActivePanel(type);
        if (type === 'home') {
            navigateToHome();
            return;
        }
        if (type === 'note') toggleNotePanel(!notePanelOpen);
        if (type === 'chat') toggleChatPanel(!chatPanelOpen);
        if (type === 'pdf') togglePdfPanel(!pdfPanelOpen);
        if (type === 'tag') toggleTagPanel(!tagPanelOpen);
    }, [navigateToHome, toggleNotePanel, toggleChatPanel, togglePdfPanel, toggleTagPanel, notePanelOpen, chatPanelOpen, pdfPanelOpen, tagPanelOpen]);

    const panels = useMemo(() => [
        {
            x: pdfPanelPosition.x,
            y: pdfPanelPosition.y,
            width: pdfPanelPosition.width,
            height: pdfPanelPosition.height,
            isOpen: pdfPanelOpen,
            type: 'pdf'
        },
        {
            x: chatPanelPosition.x,
            y: chatPanelPosition.y,
            width: chatPanelPosition.width,
            height: chatPanelPosition.height,
            isOpen: chatPanelOpen,
            type: 'chat'
        },
        {
            x: notePanelPosition.x,
            y: notePanelPosition.y,
            width: notePanelPosition.width,
            height: notePanelPosition.height,
            isOpen: notePanelOpen,
            type: 'note'
        },
    ], [
        pdfPanelPosition.x,
        pdfPanelPosition.y,
        pdfPanelPosition.width,
        pdfPanelPosition.height,
        pdfPanelOpen,
        chatPanelPosition.x,
        chatPanelPosition.y,
        chatPanelPosition.width,
        chatPanelPosition.height,
        chatPanelOpen,
        notePanelPosition.x,
        notePanelPosition.y,
        notePanelPosition.width,
        notePanelPosition.height,
        notePanelOpen
    ])

    const setPanelsPosition = {
        [panel.pdf]: setPdfPanelPosition,
        [panel.note]: setNotePanelPosition,
        [panel.chat]: setChatPanelPosition,
        "flow": setFlowPanelPosition,
    }
    // 获取所有贴边面板
    const getAdjacentPanels = (otherPanels: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>) => {
        // 过滤出打开的面板并按x坐标排序（不修改原数组）
        const openPanels = otherPanels
            .filter(panel => panel.isOpen)
            .sort((a, b) => a.x - b.x);

        console.log('openPanels', openPanels);

        // 获取左侧连续紧贴的面板
        const getLeftAdjacentPanels = () => {
            const leftPanels = [];
            let currentX = 0; // 从左边界开始

            // 添加虚拟的左边界面板
            leftPanels.push({
                x: 0,
                y: 0,
                width: 0,
                height: window.innerHeight,
                isOpen: true,
                type: 'left'
            });

            // 检查每个面板是否与当前位置紧贴
            for (const panel of openPanels) {
                if (panel.x === currentX) {
                    leftPanels.push(panel);
                    currentX = panel.x + panel.width;
                } else {
                    break; // 一旦发现不连续的面板，就停止
                }
            }

            return leftPanels;
        };

        // 获取右侧连续紧贴的面板
        const getRightAdjacentPanels = (usedPanels: Set<string>) => {
            const rightPanels = [];
            let currentX = window.innerWidth - SIDEBAR_WIDTH; // 从右边界开始

            // 添加虚拟的右边界面板
            rightPanels.push({
                x: window.innerWidth - SIDEBAR_WIDTH,
                y: 0,
                width: 0,
                height: window.innerHeight,
                isOpen: true,
                type: 'right'
            });

            // 从右往左检查每个面板是否紧贴（排除已被左侧使用的面板）
            for (let i = openPanels.length - 1; i >= 0; i--) {
                const panel = openPanels[i];

                // 跳过已被左侧使用的面板
                if (usedPanels.has(panel.type)) {
                    continue;
                }

                if (panel.x + panel.width === currentX) {
                    rightPanels.unshift(panel);
                    currentX = panel.x;
                } else if (panel.x + panel.width < currentX) {
                    break;
                }
            }

            return rightPanels;
        };

        const leftAdjacentPanels = getLeftAdjacentPanels();

        // 创建已使用面板的集合（排除虚拟边界面板）
        const usedPanelTypes = new Set(
            leftAdjacentPanels
                .filter(panel => panel.type !== 'left')
                .map(panel => panel.type)
        );

        const rightAdjacentPanels = getRightAdjacentPanels(usedPanelTypes);

        console.log('leftAdjacentPanels', leftAdjacentPanels);
        console.log('rightAdjacentPanels', rightAdjacentPanels);
        console.log('usedPanelTypes', usedPanelTypes);

        return {leftAdjacentPanels, rightAdjacentPanels}
    }

    // 生成分割线数据
    const generateDividers = () => {
        const dividers: Array<{
            id: string;
            x: number;
            y: number;
            height: number;
            leftPanel: any;
            rightPanel: any;
            setLeftPosition: any;
            setRightPosition: any;
        }> = [];

        const {
            leftAdjacentPanels,
            rightAdjacentPanels
        } = getAdjacentPanels(panels.filter(panel => panel.type !== dragPanel));
        console.log("generateDividers", leftAdjacentPanels, rightAdjacentPanels)
        // 为左侧连续面板之间添加分割线（跳过虚拟左边界面板）
        for (let i = 1; i < leftAdjacentPanels.length - 1; i++) {
            const leftPanel = leftAdjacentPanels[i];
            const rightPanel = leftAdjacentPanels[i + 1];

            // 跳过虚拟边界面板
            if (leftPanel.type === 'left' || rightPanel.type === 'left') continue;

            const dividerX = leftPanel.x + leftPanel.width;

            dividers.push({
                id: `${leftPanel.type}-${rightPanel.type}`,
                x: dividerX,
                y: 0,
                height: window.innerHeight,
                leftPanel: leftPanel.type === 'note' ? notePanelPosition :
                    leftPanel.type === 'pdf' ? pdfPanelPosition :
                        chatPanelPosition,
                rightPanel: rightPanel.type === 'note' ? notePanelPosition :
                    rightPanel.type === 'pdf' ? pdfPanelPosition :
                        chatPanelPosition,
                setLeftPosition: leftPanel.type === 'note' ? setNotePanelPosition :
                    leftPanel.type === 'pdf' ? setPdfPanelPosition :
                        setChatPanelPosition,
                setRightPosition: rightPanel.type === 'note' ? setNotePanelPosition :
                    rightPanel.type === 'pdf' ? setPdfPanelPosition :
                        setChatPanelPosition
            });
        }

        // 为右侧连续面板之间添加分割线（跳过虚拟右边界面板）
        for (let i = 0; i < rightAdjacentPanels.length - 1; i++) {
            const leftPanel = rightAdjacentPanels[i];
            const rightPanel = rightAdjacentPanels[i + 1];

            // 跳过虚拟边界面板
            if (leftPanel.type === 'right' || rightPanel.type === 'right') continue;

            const dividerX = leftPanel.x + leftPanel.width;

            dividers.push({
                id: `${leftPanel.type}-${rightPanel.type}`,
                x: dividerX,
                y: 0,
                height: window.innerHeight,
                leftPanel: leftPanel.type === 'note' ? notePanelPosition :
                    leftPanel.type === 'pdf' ? pdfPanelPosition :
                        chatPanelPosition,
                rightPanel: rightPanel.type === 'note' ? notePanelPosition :
                    rightPanel.type === 'pdf' ? pdfPanelPosition :
                        chatPanelPosition,
                setLeftPosition: leftPanel.type === 'note' ? setNotePanelPosition :
                    leftPanel.type === 'pdf' ? setPdfPanelPosition :
                        setChatPanelPosition,
                setRightPosition: rightPanel.type === 'note' ? setNotePanelPosition :
                    rightPanel.type === 'pdf' ? setPdfPanelPosition :
                        setChatPanelPosition
            });
        }

        return dividers;
    };

    useEffect(() => {
        console.log("useEffect setFlowPanelPosition")
        const {
            leftAdjacentPanels,
            rightAdjacentPanels
        } = getAdjacentPanels(panels.filter(panel => panel.type !== dragPanel))
        const leftX = leftAdjacentPanels.length > 0 ? leftAdjacentPanels[leftAdjacentPanels.length - 1].x + leftAdjacentPanels[leftAdjacentPanels.length - 1].width : 0
        const rightX = rightAdjacentPanels.length > 0 ? rightAdjacentPanels[0].x : window.innerWidth - SIDEBAR_WIDTH
        // flow位置
        setFlowPanelPosition(prev => {
            return {
                ...prev,
                x: leftX,
                width: rightX - leftX
            }
        })
        // 边
        if (leftAdjacentPanels.length > 1) {
            for (let i = 1; i <= leftAdjacentPanels.length - 1; i++) {
                const panelType = leftAdjacentPanels[i].type as keyof typeof setPanelsPosition;
                if (i === leftAdjacentPanels.length - 1) {
                    setPanelsPosition[panelType]((prev: PanelPosition) => ({
                        ...prev,
                        resizeHandles: ['n', 's', 'e', 'ne', 'se'],
                    }));
                } else {
                    setPanelsPosition[panelType]((prev: PanelPosition) => ({
                        ...prev,
                        resizeHandles: ['n', 's'],
                    }));
                }
            }
        }
        if (rightAdjacentPanels.length > 1) {
            for (let i = 0; i <= rightAdjacentPanels.length - 2; i++) {
                const panelType = rightAdjacentPanels[i].type as keyof typeof setPanelsPosition;
                if (i === 0) {
                    setPanelsPosition[panelType]((prev: PanelPosition) => ({
                        ...prev,
                        resizeHandles: ['n', 's', 'w', 'nw', 'sw'],
                    }));
                } else {
                    setPanelsPosition[panelType]((prev: PanelPosition) => ({
                        ...prev,
                        resizeHandles: ['n', 's'],
                    }));
                }
            }
        }

        const leftPanels = leftAdjacentPanels.map(panel => panel.type)
        const rightPanels = rightAdjacentPanels.map(panel => panel.type)
        const otherPanels = panels.filter(panel => panel.type !== dragPanel && !leftPanels.includes(panel.type) && !rightPanels.includes(panel.type))
        // 恢复边
        for (const panel of otherPanels) {
            const panelType = panel.type as keyof typeof setPanelsPosition;
            setPanelsPosition[panelType]((prev: PanelPosition) => ({
                ...prev,
                resizeHandles: ['n', 's', 'w', 'e', 'nw', 'ne', 'sw', 'se'],
            }));
        }
    }, [panels, dragPanel]);
    return (
        <OuterContainer>
            <Sidebar active={activePanel}
                     onSelect={handleSidebarSelect}
                     noteOpen={notePanelOpen}
                     chatOpen={chatPanelOpen}
                     pdfOpen={pdfPanelOpen}/>
            <PanelContainer ref={wRef}>
                {/* 移除PanelGroup，使用自定义布局 */}
                {/*<FlowPanelContainer>*/}
                <FlowPanel panelPosition={flowPanelPosition}/>
                {/*</FlowPanelContainer>*/}
                {/* 贴边预览蒙版 */}
                {snapPreview.visible && (
                    <SnapPreviewOverlay position={snapPreview.previewPosition}/>
                )}

                {/* 笔记面板 */}
                {notePanelOpen && (
                    <NotePanelMain
                        handlePanelClick={() => handlePanelClick(panel.note)}
                        zIndex={zIndexes.note}
                        panelPosition={{
                            ...notePanelPosition
                        }}
                        setPanelPosition={setNotePanelPosition}
                        setPanelsPosition={setPanelsPosition}
                        setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'note' : "")}
                        getAdjacentPanels={getAdjacentPanels}
                        otherPanels={panels.filter((p: any) => p.type !== 'note' && p.isOpen)}
                    />
                )}

                {/* PDF面板 */}
                {pdfPanelOpen && (
                    <PdfPanelMain
                        handlePanelClick={() => handlePanelClick(panel.pdf)}
                        zIndex={zIndexes.pdf}
                        panelPosition={{
                            ...pdfPanelPosition
                        }}
                        setPanelPosition={setPdfPanelPosition}
                        setPanelsPosition={setPanelsPosition}
                        setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'pdf' : "")}
                        getAdjacentPanels={getAdjacentPanels}
                        otherPanels={panels.filter((p: any) => p.type !== 'pdf' && p.isOpen)}
                    />
                )}

                {/* 聊天面板 */}
                {chatPanelOpen && (
                    <ChatPanel
                        handlePanelClick={() => handlePanelClick(panel.chat)}
                        zIndex={zIndexes.chat}
                        panelPosition={{
                            ...chatPanelPosition
                        }}
                        setPanelPosition={setChatPanelPosition}
                        setPanelsPosition={setPanelsPosition}
                        setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'chat' : "")}
                        getAdjacentPanels={getAdjacentPanels}
                        otherPanels={panels.filter((p: any) => p.type !== 'chat' && p.isOpen)}
                    />
                )}

                {/* 面板分割线 */}
                {generateDividers().map((divider) => (
                    <ResizeDivider
                        key={divider.id}
                        x={divider.x}
                        y={divider.y}
                        height={divider.height}
                        leftPanel={divider.leftPanel}
                        rightPanel={divider.rightPanel}
                        setLeftPanelPosition={divider.setLeftPosition}
                        setRightPanelPosition={divider.setRightPosition}
                        minLeftWidth={200}
                        minRightWidth={200}
                    />
                ))}

                {/* 标签管理 */}
                {tagPanelOpen && <TagPanel onClose={() => toggleTagPanel(false)} zIndex={1001}/>}

                {/* 全局窗口管理器 - 在最外层渲染独立窗口 */}
                <GlobalWindowManager/>
            </PanelContainer>
        </OuterContainer>
    );
});

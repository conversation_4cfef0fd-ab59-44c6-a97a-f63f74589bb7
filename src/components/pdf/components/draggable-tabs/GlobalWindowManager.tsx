import React, {useCallback} from 'react';
import {usePdfStore} from '@/store/pdf-store';
import {FloatingWindow} from './FloatingWindow';
import {DragItem, DropResult, TabItem} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";

/**
 * 全局窗口管理器
 */
export const GlobalWindowManager: React.FC = () => {
    const {
        windows,
        updateWindow,
        closeWindow,
        removeTabFromWindow,
        moveTabBetweenWindows,
        createWindow,
        addTabToWindow,
        setTabItems,
        setActiveAid,
        dragTabWindowId,
    } = usePdfStore((state) => ({
        windows: state.windows,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
        moveTabBetweenWindows: state.moveTabBetweenWindows,
        createWindow: state.createWindow,
        addTabToWindow: state.addTabToWindow,
        setTabItems: state.setTabItems,
        setActiveAid: state.setActiveAid,
        dragTabWindowId: state.dragTabWindowId,
    }));
    const {dragEnd, checkIfOutsideContainer} = useDragTab()

    // 窗口标签点击
    const handleWindowTabClick = useCallback((windowId: string, tabKey: string) => {
        updateWindow(windowId, {activeTabKey: tabKey});
    }, [updateWindow]);

    // 窗口标签关闭
    const handleWindowTabClose = useCallback((windowId: string, tabKey: string) => {
        removeTabFromWindow(windowId, tabKey);
    }, [removeTabFromWindow]);

    // 窗口标签重排序
    const handleWindowTabsReorder = useCallback((windowId: string, tabs: TabItem[]) => {
        updateWindow(windowId, {tabs});
    }, [updateWindow]);

    // 窗口标签拖拽结束
    const handleWindowTabDragEnd = useCallback((windowId: string, item: DragItem, result: DropResult | null, monitor) => {
        dragEnd(item, result)
    }, [closeWindow, dragEnd]);

    // 标签合并到窗口
    const handleTabMerge = useCallback((sourceId: string, targetWindowId: string, tabItem: TabItem) => {
        // 检查源是否为窗口ID
        if (windows.has(sourceId)) {
            // 从源窗口移动到目标窗口
            moveTabBetweenWindows(sourceId, targetWindowId, tabItem.key);
        } else {
            // 从主标签页移动到窗口
            const currentTabItems = usePdfStore.getState().tabItems || [];
            const newTabItems = currentTabItems.filter((item) => item?.key !== tabItem.key);
            setTabItems(newTabItems);

            const addTabToWindow = usePdfStore.getState().addTabToWindow;
            addTabToWindow(targetWindowId, tabItem);

            // 更新活动标签
            setActiveAid(tabItem.key);
        }
    }, [windows, moveTabBetweenWindows, setTabItems, setActiveAid]);

    // 渲染窗口内容
    const renderWindowContent = useCallback((window: any) => {
        const activeTab = window.tabs.find((tab: TabItem) => tab.key === window.activeTabKey);

        if (activeTab?.children) {
            return activeTab.children;
        }

        // 默认内容
        return (
            <div className="w-full h-full flex items-center justify-center text-gray-500">
                {window.activeTabKey ? `内容: ${window.activeTabKey}` : '无内容'}
            </div>
        );
    }, []);

    // 如果没有窗口，不渲染任何内容
    if (windows.size === 0) {
        return null;
    }
    return (
        <>
            {Array.from(windows.values()).map((window) => (
                <FloatingWindow
                    key={window.id}
                    window={window}
                    isDraggingTab={window.id === dragTabWindowId}
                    onWindowUpdate={updateWindow}
                    onWindowClose={closeWindow}
                    onTabClick={handleWindowTabClick}
                    onTabClose={handleWindowTabClose}
                    onTabsReorder={handleWindowTabsReorder}
                    onTabDragEnd={handleWindowTabDragEnd}
                    onTabMerge={handleTabMerge}
                >
                    {renderWindowContent(window)}
                </FloatingWindow>
            ))}
        </>
    );
};

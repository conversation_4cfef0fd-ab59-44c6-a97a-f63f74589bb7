# 🚀 数据库使用快速指南

## 简单开始

### 1. 基本初始化

在你的组件中使用数据库：

```typescript
import React, { useEffect, useState } from 'react';
import { getDatabase, folderService } from '@/local';

function MyComponent() {
    const [dbReady, setDbReady] = useState(false);

    useEffect(() => {
        const initDatabase = async () => {
            try {
                await getDatabase();
                    setDbReady(true);
                console.log('✅ 数据库初始化成功');
            } catch (error) {
                console.error('❌ 数据库初始化失败:', error);
            }
        };

        initDatabase();
    }, []);

    if (!dbReady) {
        return <div>正在初始化数据库...</div>;
    }

    return <div>应用内容</div>;
}
```

### 2. 文件夹操作

```typescript
import { folderService } from '@/local';

// 创建文件夹
const createFolder = async () => {
    const result = await folderService.createFolder({
        wid: 'workspace_123',
        name: '新文件夹',
        parent_id: 'root'
    });
    console.log('文件夹创建成功:', result.id);
};

// 创建文件记录
const createFile = async (file: File) => {
    const result = await folderService.createFile({
        wid: 'workspace_123',
        file_name: file.name,
        parent_id: 'root',
        size: file.size,
        mime_type: file.type,
        file_path: `/uploads/${file.name}`
    });
    console.log('文件记录创建成功:', result.id);
};

// 获取文件列表
const getFiles = async () => {
    const files = await folderService.getList({
        wid: 'workspace_123',
        type: 'file'
    });
    console.log('文件列表:', files);
};

// 获取文件夹列表
const getFolders = async () => {
    const folders = await folderService.getList({
        wid: 'workspace_123',
        type: 'folder'
    });
    console.log('文件夹列表:', folders);
};
```

### 3. 完整的PDF上传示例

```typescript
import { folderService } from '@/local';

const handlePdfUpload = async (file: File, targetFolderId = 'root') => {
    try {
        // 创建文件记录
        const result = await folderService.createFile({
            wid: 'your_workspace_id',
            file_name: file.name,
            parent_id: targetFolderId,
            size: file.size,
            mime_type: file.type,
            file_path: `/uploads/${file.name}`
        });

        console.log('✅ PDF文件记录创建成功:', result);
        return result;
        
    } catch (error) {
        console.error('❌ PDF上传失败:', error);
        throw error;
    }
};
```

## 📊 数据结构

### 文件夹/文件数据格式

```typescript
interface AttachmentDocType {
    id: string;              // 文件/文件夹ID
    wid: string;             // 工作区ID
    file_name: string;       // 文件名/文件夹名
    type: 'file' | 'folder'; // 类型
    parent_id: string;       // 父文件夹ID (根目录为'root')
    order: number;           // 排序顺序
    size?: number;           // 文件大小（字节）
    mime_type?: string;      // MIME类型
    file_path?: string;      // 文件路径
    create_at: number;       // 创建时间（Unix时间戳）
    update_at: number;       // 更新时间（Unix时间戳）
}
```

## 🔧 常用操作

### 文件夹管理

```typescript
// 重命名文件夹
await folderService.rename({
    id: 'folder_id',
    new_name: '新名称'
});

// 移动文件到其他文件夹
await folderService.move({
    id: 'file_id',
    new_parent_id: 'target_folder_id'
});

// 批量更新排序
await folderService.updateOrder({
    items: [
        { id: 'item1', order: 1 },
        { id: 'item2', order: 2 }
    ]
});

// 删除文件夹（会递归删除所有子项）
await folderService.delete({ id: 'folder_id' });
```

### 获取文件夹路径

```typescript
// 获取面包屑导航路径
const path = await folderService.getFolderPath('workspace_id', 'folder_id');
// 返回: [{ id: 'root', name: '根目录' }, { id: 'folder_id', name: '文件夹名' }]
```

### 搜索功能

```typescript
// 搜索文件和文件夹
const results = await folderService.search({
    wid: 'workspace_id',
    keyword: '搜索关键词',
    type: 'file' // 可选，只搜索文件
});
```

## 🚨 注意事项

1. **异步操作**: 所有数据库操作都是异步的，记得使用 `await`
2. **错误处理**: 建议用 `try/catch` 包装数据库操作
3. **工作区ID**: 所有操作都需要提供 `wid`（工作区ID）
4. **父文件夹**: 根目录的 `parent_id` 是 `'root'`
5. **排序**: `order` 值越小越靠前
6. **时间戳**: 创建和更新时间会自动生成

## 🎉 完成！

现在你可以使用完整的文件夹管理功能了：

- ✅ 创建文件夹和文件记录
- ✅ 获取文件列表
- ✅ 重命名和删除操作
- ✅ 拖拽排序
- ✅ 搜索功能

如果遇到问题，请检查浏览器控制台的错误信息。 
import { useCallback } from 'react';
import { useFlowStore } from '@/store/flow-store';
import { updateNodeSize } from '@/api/canvas';
import { validateNodeSize } from '@/components/flow/constants/node-defaults';

export const useNodeResize = () => {
    const { updateNodes } = useFlowStore((state) => ({
        updateNodes: state.updateNodes,
    }));

    // 处理节点实时大小调整（拖拽过程中）
    const handleNodeResizeRealtime = useCallback((nodeId: string, width: number, height: number) => {
        try {
            // 验证尺寸，确保不小于最小值
            const { width: validatedWidth, height: validatedHeight } = validateNodeSize(width, height);
            
            // 只更新本地状态，不调用API
            updateNodes([{
                id: nodeId,
                width: validatedWidth,
                height: validatedHeight,
                measured: {
                    width: validatedWidth,
                    height: validatedHeight,
                }
            }]);
            
        } catch (error) {
            console.error('节点实时大小调整失败:', error);
        }
    }, [updateNodes]);

    // 处理节点大小调整完成（拖拽结束时）
    const handleNodeResize = useCallback(async (nodeId: string, width: number, height: number) => {
        try {
            // 验证尺寸，确保不小于最小值
            const { width: validatedWidth, height: validatedHeight } = validateNodeSize(width, height);
            
            // 更新本地状态 - 同时更新显示尺寸和测量尺寸
            updateNodes([{
                id: nodeId,
                width: validatedWidth,
                height: validatedHeight,
                measured: {
                    width: validatedWidth,
                    height: validatedHeight,
                }
            }]);

            // 调用API保存节点尺寸到服务器
            await updateNodeSize({ 
                nid: nodeId, 
                width: validatedWidth, 
                height: validatedHeight 
            });
            
        } catch (error) {
            console.error('节点大小调整失败:', error);
        }
    }, [updateNodes]);

    return {
        handleNodeResize,
        handleNodeResizeRealtime,
    };
}; 
import {Tooltip} from "antd";
import {ZoomInOutlined, ZoomOutOutlined} from "@ant-design/icons";
import {usePdfStore} from "@/store/pdf-store.ts";
import {useCallback} from "react";

export const PdfZoom = ({aid}: { aid: string }) => {
    const {scale, updatePdfs} = usePdfStore((state) => ({
        scale: state.pdfs.get(aid)?.scale || 1,
        updatePdfs: state.updatePdfs,
    }))
    const zoomIn = useCallback(() => {
        // 限制缩放比例在0.5-2.0之间
        const pdf = usePdfStore.getState().pdfs.get(aid);
        if (!pdf) return;
        const newScale = Math.min(2, Math.max(0.5, pdf.scale + 0.1));
        updatePdfs({aid: aid, scale: newScale});
    }, [updatePdfs])
    const zoomOut = useCallback(() => {
        // 限制缩放比例在0.5-2.0之间
        const pdf = usePdfStore.getState().pdfs.get(aid);
        if (!pdf) return;
        const newScale = Math.max(0.5, Math.min(2, pdf.scale - 0.1));
        updatePdfs({aid: aid, scale: newScale});
    }, [updatePdfs])
    return (
        <div
            className="absolute right-5 top-[80%] transform -translate-y-1/2 flex flex-col bg-white rounded-[40px] shadow-[0_2px_8px_rgba(0,0,0,0.2)] p-2 z-[100] opacity-80 hover:opacity-100 transition-opacity duration-300 gap-2"
        >
            <Tooltip title="放大" placement="left">
                <div
                    className="w-9 h-6 flex items-center justify-center rounded-tl-[18px] rounded-tr-[18px] cursor-pointer transition-all duration-200 hover:bg-[rgba(205,210,233,0.6)]"
                    onClick={zoomIn}
                >
                    <ZoomInOutlined/>
                </div>
            </Tooltip>
            <div
                className="w-9 h-6 flex items-center justify-center text-xs text-gray-600 select-none border-t border-b border-gray-200">
                {`${Math.round(scale * 100)}%`}
            </div>
            <Tooltip title="缩小" placement="left">
                <div
                    className="w-9 h-6 flex items-center justify-center rounded-bl-[18px] rounded-br-[18px] cursor-pointer transition-all duration-200 hover:bg-[rgba(205,210,233,0.6)]"
                    onClick={zoomOut}
                >
                    <ZoomOutOutlined/>
                </div>
            </Tooltip>
        </div>
    )
}
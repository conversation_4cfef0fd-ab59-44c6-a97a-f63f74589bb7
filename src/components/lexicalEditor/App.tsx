import React from 'react';
import type { JSX } from 'react';
import { $convertFromMarkdownString } from '@lexical/markdown';
import { PLAYGROUND_TRANSFORMERS } from './plugins/MarkdownTransformers';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import {
    $isTextNode,
    DOMConversionMap,
    TextNode,
} from 'lexical';
import { SettingsContext, useSettings } from './context/SettingsContext';;
import { ToolbarContext } from './context/ToolbarContext';
import Editor, { FlowEditorWithHook, GlobalEditorWithHook, NodeEditorWithHook, NoteType, NoteTypeEnum } from './Editor';
import PlaygroundNodes from './nodes/PlaygroundNodes';
import { parseAllowedFontSize } from './plugins/ToolbarPlugin/fontSize';
import PlaygroundEditorTheme from './themes/PlaygroundEditorTheme';
import { parseAllowedColor } from './ui/ColorPicker';
// @ts-ignore
import Prism from 'prismjs';
// @ts-ignore
window.Prism = Prism;

function getExtraStyles(element: HTMLElement): string {
    // Parse styles from pasted input, but only if they match exactly the
    // sort of styles that would be produced by exportDOM
    let extraStyles = '';
    const fontSize = parseAllowedFontSize(element.style.fontSize);
    const backgroundColor = parseAllowedColor(element.style.backgroundColor);
    const color = parseAllowedColor(element.style.color);
    if (fontSize !== '' && fontSize !== '15px') {
        extraStyles += `font-size: ${fontSize};`;
    }
    if (backgroundColor !== '' && backgroundColor !== 'rgb(255, 255, 255)') {
        extraStyles += `background-color: ${backgroundColor};`;
    }
    if (color !== '' && color !== 'rgb(0, 0, 0)') {
        extraStyles += `color: ${color};`;
    }
    return extraStyles;
}

function buildImportMap(): DOMConversionMap {
    const importMap: DOMConversionMap = {};

    for (const [tag, fn] of Object.entries(TextNode.importDOM() || {})) {
        importMap[tag] = (importNode) => {
            const importer = fn(importNode);
            if (!importer) {
                return null;
            }
            return {
                ...importer,
                conversion: (element) => {
                    const output = importer.conversion(element);
                    if (
                        output === null ||
                        output.forChild === undefined ||
                        output.after !== undefined ||
                        output.node !== null
                    ) {
                        return output;
                    }
                    const extraStyles = getExtraStyles(element);
                    if (extraStyles) {
                        const { forChild } = output;
                        return {
                            ...output,
                            forChild: (child, parent) => {
                                const textNode = forChild(child, parent);
                                if ($isTextNode(textNode)) {
                                    textNode.setStyle(textNode.getStyle() + extraStyles);
                                }
                                return textNode;
                            },
                        };
                    }
                    return output;
                },
            };
        };
    }

    return importMap;
}

export const EditorWrapper = ({ children }: { children: React.ReactNode }): JSX.Element => {

    const initialConfig = {
        editorState: () => {
            return $convertFromMarkdownString('', PLAYGROUND_TRANSFORMERS)
        },
        html: { import: buildImportMap() },
        namespace: 'Playground',
        nodes: [...PlaygroundNodes],
        onError: (error: Error) => {
            throw error;
        },
        theme: PlaygroundEditorTheme,
    };

    return (
        <SettingsContext>
            <LexicalComposer initialConfig={initialConfig}>
                {/* <SharedHistoryContext> */}
                {/* <TableContext> */}
                <ToolbarContext>
                    <div className="editor-shell h-full" >
                        {children}
                    </div>
                </ToolbarContext>
                {/* </TableContext> */}
                {/* </SharedHistoryContext> */}
            </LexicalComposer>
        </SettingsContext>
    );
}

export const GlobalEditor = (props: any) => {
    return <EditorWrapper>
        <GlobalEditorWithHook {...props} />
    </EditorWrapper>
}

export const FlowEditor = (props: any) => {
    return <EditorWrapper>
        <FlowEditorWithHook {...props} />
    </EditorWrapper>
}

export const NodeEditor = (props: any) => {
    return <EditorWrapper>
        <NodeEditorWithHook {...props} />
    </EditorWrapper>
}
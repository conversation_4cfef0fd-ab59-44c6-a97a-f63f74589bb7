# React Flow Helper Lines 对齐辅助线功能

## 功能概述

Helper Lines（对齐辅助线）是一个增强用户体验的功能，当拖拽节点时会自动显示对齐辅助线，帮助用户精确对齐节点。

## 功能特性

### 1. 对齐类型
- **左边缘对齐**：节点的左边缘与其他节点的左边缘对齐
- **右边缘对齐**：节点的右边缘与其他节点的右边缘对齐
- **水平中心对齐**：节点的水平中心线与其他节点的水平中心线对齐
- **顶部对齐**：节点的顶部与其他节点的顶部对齐
- **底部对齐**：节点的底部与其他节点的底部对齐
- **垂直中心对齐**：节点的垂直中心线与其他节点的垂直中心线对齐
- **边缘相邻对齐**：节点的一边与其他节点的相邻边对齐

### 2. 视觉反馈
- **红色虚线**：显示对齐辅助线，带有动画效果
- **自动吸附**：当节点接近对齐位置时自动吸附到正确位置
- **吸附距离**：5像素的吸附阈值，确保精确对齐

### 3. 性能优化
- **实时计算**：拖拽时实时计算对齐位置
- **自动清理**：拖拽结束后自动清除辅助线
- **单一对齐**：X轴和Y轴分别只显示一条最优的对齐线

## 技术实现

### 核心文件结构
```
src/components/flow/
├── hooks/
│   └── use-helper-lines.ts       # 辅助线逻辑
├── components/
│   └── HelperLinesRenderer.tsx   # 辅助线渲染组件
├── hooks/
│   └── node-drag.ts             # 集成辅助线的拖拽逻辑
├── FlowPanel.tsx                # 主组件集成
└── style/
    └── index.css                # 辅助线样式
```

### 使用方法

1. **Hook 使用**
```typescript
const { helperLines, calculateHelperLines, clearHelperLines } = useHelperLines();
```

2. **拖拽集成**
```typescript
const onNodeDrag = useCallback((_, node) => {
  const snappedPosition = calculateHelperLines(node);
  // 使用吸附后的位置更新节点
}, [calculateHelperLines]);
```

3. **渲染集成**
```tsx
<HelperLinesRenderer helperLines={helperLines} />
```

## 配置选项

### 吸附距离调整
在 `use-helper-lines.ts` 中修改 `SNAP_DISTANCE` 常量：
```typescript
const SNAP_DISTANCE = 5; // 像素
```

### 样式自定义
在 `style/index.css` 中修改辅助线样式：
```css
.react-flow__helper-line {
  stroke: #ff6b6b;        /* 线条颜色 */
  stroke-width: 1;        /* 线条宽度 */
  stroke-dasharray: 4 4;  /* 虚线样式 */
  opacity: 0.8;           /* 透明度 */
}
```

## 使用场景

1. **流程图绘制**：精确对齐流程节点
2. **思维导图**：整齐排列主题节点
3. **架构图设计**：规范化组件布局
4. **原型设计**：快速对齐界面元素

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **性能考虑**：大量节点时可能影响拖拽性能，建议超过100个节点时考虑优化
2. **视觉层级**：辅助线的 z-index 为 10，确保在节点上方显示
3. **触摸设备**：在移动设备上可能需要调整吸附距离

## 未来改进

1. **网格对齐**：支持网格吸附功能
2. **多节点对齐**：支持多选节点的批量对齐
3. **智能距离**：根据缩放级别动态调整吸附距离
4. **对齐历史**：记住常用的对齐位置
import { createWithEqualityFn } from 'zustand/traditional'
import { shallow } from "zustand/shallow";
import { SessionType, ChatResponseType } from '@/components/chat-panel/types/index'
import { SSEClientInstance } from '@/tools/sse-client'
import { TagRefType, PdfRef, NodeRef } from '@/components/chat-panel/types'
import _ from 'lodash'


enum operate {
  del = 'del',
  add = 'add',
  update = 'update'
}

type operateType = keyof typeof operate

type ChatStoreType = {
  userInput: string
  activeSessionId: string
  footerQuestion: string
  footerRefs: TagRefType[]
  pdfRefs: PdfRef[]
  nodeRefs: NodeRef[]
  sessionId: string | number
  sessions: SessionType[],
  chatList: ChatResponseType[],
  openHistory: boolean;
  responseStream: string
  sseClient: SSEClientInstance | null
  currentChat: ChatResponseType | null

  currentChatRid: number | string
  isFetchStream: boolean
  session_detail_list: {
    sid: string,
    chatList: ChatResponseType[]
  }[]
  currentQuestionInStream: any

  setCurrentQuestionInStream: (currentQuestionInStream: any) => void
  setActiveSessionId: (sessionId: string) => void
  setUserInput: (input: string) => void
  setCurrentChat: (chat: ChatResponseType | null) => void
  updateCurrentChat: (chat: ChatResponseType) => void
  setCurrentChatRid: (rid: number | string) => void
  addFooterRef: (ref: TagRefType) => void
  deleteFooterRef: (id: string) => void
  setFooterRefs: (refs: TagRefType[]) => void
  setPdfRefs: (pdfRefs: PdfRef[]) => void
  setNodeRefs: (nodeRefs: NodeRef[]) => void
  setSessionId: (sessionId: string | number) => void
  setSessions: (sessions: SessionType[]) => void
  addSession: (session: SessionType) => void
  removeSession: (sid: string) => void
  updateSession: (session: SessionType) => void
  setChatList: (chatList: ChatResponseType[]) => void
  addChat: (response: ChatResponseType[]) => void
  deleteChatCurrentWIdthAfter: (rid: string | number) => void
  updateChatList: (response: ChatResponseType) => void
  setOpenHistory: () => void
  setResponseStream: (chunk: string) => void
  setSseClient: (sseClient: SSEClientInstance | null) => void
  setIsFetchStream: (isFetchStream: boolean) => void
  cleanResponseStream: () => void
  cleanSession: () => void
  setSessionDetailList: (session_detail_list: {
    sid: string,
    chatList: ChatResponseType[]
  }[]) => void,
  addSessionDetail: (session_detail: {
    sid: string,
    chatList: ChatResponseType[]
  }) => void,
  updateSessionDetail: (session_detail: {
    sid: string,
    chatList: ChatResponseType[]
  }) => void
  removeSessionDetail: (sid: string) => void
  updateFooterRef: (ref: { id: string, title?: string }, type?: operateType) => void
  batchDelFooterRef: (id: string []) => void
}
export const useChatStore = createWithEqualityFn<ChatStoreType>((set, get) => ({

  activeSessionId: "",
  userInput: "",
  footerRefs: [],
  footerQuestion: "",
  pdfRefs: [],
  nodeRefs: [],
  sessionId: 0,
  sessions: [],
  chatList: [],
  openHistory: false,
  responseStream: "",
  sseClient: null,
  currentChat: null,
  currentChatRid: -1,
  isFetchStream: false,
  session_detail_list: [],
  currentQuestionInStream: null,

  setCurrentQuestionInStream: (currentQuestionInStream: any) => set(() => ({ currentQuestionInStream })),
  setActiveSessionId: (sessionId: string) => set(() => ({ activeSessionId: sessionId })),
  setUserInput: (input: string) => set(() => ({ userInput: input })),
  setCurrentChatRid: (rid: number | string) => set(() => ({ currentChatRid: rid })),
  setCurrentChat: (chat: ChatResponseType | null) => set(() => ({ currentChat: chat })),
  updateCurrentChat: (chat: ChatResponseType) => set((state) => ({
    currentChat: state.currentChat?.rid === chat.rid ? chat : state.currentChat
  })),
  addFooterRef: (ref: TagRefType) => set((state) => ({ footerRefs: [...state.footerRefs, ref] })),
  updateFooterRef: (targetRef, type = 'update' ) => set((state) => {
    if (type === operate.update) {
      const newFooterRefs =  state.footerRefs || []
      const updateRefs: TagRefType []  = newFooterRefs?.map((ref) => {
        if (targetRef.id === ref.id) {
          return {
            ...ref,
            title: targetRef?.title,
          }  as TagRefType
        }
        return ref
      })

      return {
        footerRefs: [...updateRefs]
      }

    }

    if (type === operate.del) {
      return {
        footerRefs: state.footerRefs.filter((ref) => ref.id !== targetRef.id)
      }
    }
    return {}
  }),
  batchDelFooterRef: (ids: string[]) => set((state) => ({ footerRefs: state.footerRefs.filter((ref) => !ids.includes(ref.id)) })),
  deleteFooterRef: (id: string) => set((state) => ({ footerRefs: state.footerRefs.filter((ref) => ref.id !== id) })),
  setFooterRefs: (refs: TagRefType[]) => set(() => ({ footerRefs: refs })),
  setPdfRefs: (pdfRefs: PdfRef[]) => set(() => ({ pdfRefs })),
  setNodeRefs: (nodeRefs: NodeRef[]) => set(() => ({ nodeRefs })),
  setOpenHistory: () => set((state) => ({ openHistory: !state.openHistory })),
  setChatList: (chatList: any) => set(() => {
    const uniqueSessionDetail = chatList.sort((a: ChatResponseType, b: ChatResponseType) => Number(a.rid) - Number(b.rid))
    return ({ chatList: [...uniqueSessionDetail] })
  }),
  addChat: (response: ChatResponseType[]) => set((state) => {
    const concatChatList = [...state.chatList, ...response].sort((a: ChatResponseType, b: ChatResponseType) => Number(a.rid) - Number(b.rid))
    return { chatList: concatChatList }
  }),
  deleteChatCurrentWIdthAfter: (rid: string | number) => set((state) => {
    const oldChatIndex = state.chatList.findIndex((chat) => chat.rid === rid)
    const beforeChat = state.chatList.slice(0, oldChatIndex)
    return ({ chatList: [...beforeChat] })
  }),
  updateChatList: (chat: ChatResponseType) => set((state) => ({
    chatList: state.chatList.map((response) => {
      if (response.rid === chat.rid) {
        return chat
      } else {
        return response
      }
    })
  })),
  setSessionId: (sessionId: string | number) => set(() => ({ sessionId })),
  setSessions: (sessions: SessionType[]) => set(() => ({ sessions })),
  addSession: (session: SessionType) => set((state) => ({ sessions: [...state.sessions, session] })),
  removeSession: (sid: string) => set((state) => ({ sessions: state.sessions.filter((session) => session.sid !== sid) })),
  updateSession: (new_session: SessionType) => set((state) => ({
    sessions: state.sessions.map((session) => {
      if (session.sid === new_session.sid) {
        return new_session
      } else {
        return session
      }
    })
  })),
  setResponseStream: (chunk: string) => set((state) => {
    return ({
      responseStream: state.responseStream + chunk,
    })
  }),
  setSseClient: (sseClient: SSEClientInstance | null) => set(() => ({ sseClient })),
  setIsFetchStream: (isFetchStream: boolean) => set(() => ({ isFetchStream })),
  cleanResponseStream: () => set(() => ({ responseStream: "" })),
  cleanSession: () => set((state) => ({
    sessionId: 0,
    footerQuestion: state.userInput,
    footerRefs: [],
    chatList: [],
    currentChat: null,
  })),
  setSessionDetailList: (session_detail_list: {
    sid: string,
    chatList: ChatResponseType[]
  }[]) => set(() => ({ session_detail_list })),
  addSessionDetail: (session_detail: { sid: string, chatList: ChatResponseType[] }) => set((state) => ({
    session_detail_list: [...state.session_detail_list, session_detail]
  })),
  updateSessionDetail: (session_detail: { sid: string, chatList: ChatResponseType[] }) => set((state) => ({
    session_detail_list: state.session_detail_list.map((session_detail_item) => {
      if (session_detail_item.sid === session_detail.sid) {
        return session_detail
      } else {
        return session_detail_item
      }
    })
  })),
  removeSessionDetail: (sid: string) => set((state) => ({
    session_detail_list: state.session_detail_list.filter((session_detail_item) => session_detail_item.sid !== sid)
  })),

}), shallow)

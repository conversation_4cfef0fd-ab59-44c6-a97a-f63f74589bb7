import { TagManagementPage } from "@/components/tag-management";

/**
 *
 * 暴露 zindex 在使用的地方看到层级
 *
 */

interface Props {
  zIndex: number;
  onClose: () => void;
}
export const TagPanel = ({ zIndex = 1001, onClose }: Props) => {
  return (
    <section
      style={{
        zIndex,
      }}
      className=" absolute left-0 right-0 top-0 bottom-0 z-[1001] bg-white"
    >
      <TagManagementPage onClose={onClose} />
    </section>
  );
};

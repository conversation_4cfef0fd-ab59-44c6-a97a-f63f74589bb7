import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { 
  hotkeysCoreFeature, selectionFeature, syncDataLoaderFeature, dragAndDropFeature,
  expandAllFeature, renamingFeature, searchFeature, FeatureImplementation, TreeInstance
} from "@headless-tree/core";
import { useTree } from "@headless-tree/react";
import { Folder, File, Plus, Search, ChevronRight, ChevronDown, Upload } from 'lucide-react';
import './HeadlessFileTreePanel.css';
import { useDragAndDrop } from './hooks/use-drag-and-drop';
import { useFolderCreation } from './hooks/use-folder-creation';
import { useDeleteOperations } from './hooks/use-delete-operations';
import { useRenameOperations } from './hooks/use-rename-operations';
import { useFileUpload } from './hooks/use-file-upload';
import { useTreeState } from './hooks/use-tree-state';

export interface FileTreeNode {
  id: string;
  title: string;
  subtitle?: string;
  isDirectory?: boolean;
  path?: string;
  size?: number;
  lastModified?: Date;
  isHidden?: boolean;
  children?: string[];
  data?: any;
  metadata?: {
    tags?: string[];
    color?: string;
    starred?: boolean;
  };
}

interface FileTreePanelProps {
  initialData?: FileTreeNode[];
  onFileSelect?: (node: FileTreeNode) => void;
  onFileCreate?: (path: string, type: 'file' | 'folder') => void;
  onFileDelete?: (path: string) => void;
  onFileRename?: (oldPath: string, newPath: string) => void;
  showHiddenFiles?: boolean;
  searchable?: boolean;
  className?: string;
  searchTerm?: string; // 添加外部搜索词属性
  currentWorkspaceId?: string; // 添加工作区ID
}

export const HeadlessFileTreePanel: React.FC<FileTreePanelProps> = ({
  initialData = [],
  onFileSelect,
  onFileCreate,
  onFileDelete,
  onFileRename,
  showHiddenFiles = false,
  searchable = true,
  className = '',
  searchTerm = '', // 默认为空字符串
  currentWorkspaceId = ''
}) => {
  const [searchString, setSearchString] = useState('');
  const [showHidden, setShowHidden] = useState(showHiddenFiles);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; node: FileTreeNode; } | null>(null);
  
  // 使用 ref 来跟踪状态变化
  const lastSavedState = useRef<{ expanded: string[]; selected: string[] }>({ expanded: [], selected: [] });
  


  // 使用文件上传Hook
  const { uploadFilesToFolder } = useFileUpload({
    currentWorkspaceId,
    onUploadComplete: (targetFolderId?: string) => {
      // 上传完成后刷新文件树
      if (onFileSelect) {
        onFileSelect({ id: 'refresh', title: 'refresh', path: '/refresh', isDirectory: false, data: { refresh: true } });
      }
      
      // 如果指定了目标文件夹，自动展开
      if (targetFolderId && targetFolderId !== 'root') {
        expandFolderAndSave(targetFolderId);
      }
    },
    onUploadError: (error) => {
      console.error('文件上传失败:', error);
    }
  });

  // 使用树状态管理Hook
  const {
    getSavedExpandedItems,
    saveExpandedItems,
    getSavedSelectedItems,
    saveSelectedItems
  } = useTreeState({ currentWorkspaceId });



  // 处理数据转换
  const processedData = useMemo(() => {
    return initialData.map(item => ({
      ...item,
      isHidden: item.isHidden || false
    }));
  }, [initialData]);

  // 构建树形数据结构
  const treeItems = useMemo(() => {
    const items: Record<string, FileTreeNode> = {};
    
    // 添加根节点
    const rootNode: FileTreeNode = {
      id: 'root',
      title: '所有文档',
      isDirectory: true,
      path: '/所有文档',
      children: []
    };
    items['root'] = rootNode;
    
    // 处理所有数据项
    processedData.forEach(item => {
      items[item.id] = item;
      
      // 如果是文件夹，确保有children数组
      if (item.isDirectory && !item.children) {
        item.children = [];
      }
    });
    
    // 构建多级父子关系
    const buildHierarchy = () => {
      // 创建文件夹映射，用于快速查找
      const folderMap = new Map<string, FileTreeNode>();
      processedData.forEach(item => {
        if (item.isDirectory) {
          folderMap.set(item.id, item);
        }
      });
      
      // 递归构建文件夹的父子关系
      const buildFolderChildren = (folderId: string): string[] => {
        const children: string[] = [];
        
        // 查找所有直接子文件夹
        processedData.forEach(item => {
          if (item.isDirectory && item.data?.folderInfo?.parent_id === folderId) {
            children.push(item.id);
            // 递归处理子文件夹
            const subChildren = buildFolderChildren(item.id);
            // 避免重复添加子项
            const existingChildren = item.children || [];
            const newChildren = subChildren.filter(childId => !existingChildren.includes(childId));
            item.children = [...existingChildren, ...newChildren];
          }
        });
        
        // 查找所有直接子文件
        processedData.forEach(item => {
          if (!item.isDirectory && item.data?.fileInfo?.parent_id === folderId) {
            children.push(item.id);
          }
        });
        
        return children;
      };
      
      // 处理所有顶级文件夹和文件
      processedData.forEach(item => {
        if (item.data?.folderInfo?.parent_id === 'root' || item.data?.fileInfo?.parent_id === 'root') {
          if (rootNode.children) {
            rootNode.children.push(item.id);
          }
        }
        
        // 为所有文件夹构建子树（无论是否为顶级）
        if (item.isDirectory) {
          const children = buildFolderChildren(item.id);
          // 避免重复添加子项
          const existingChildren = item.children || [];
          const newChildren = children.filter(childId => !existingChildren.includes(childId));
          item.children = [...existingChildren, ...newChildren];
        }
      });
    };
    
    buildHierarchy();
    
    return items;
  }, [processedData]);

  const rootId = 'root';

  // 构建功能数组，过滤掉 null 值
  const features: FeatureImplementation[] = useMemo(() => {
    const featureArray = [
      syncDataLoaderFeature, 
      selectionFeature, 
      hotkeysCoreFeature,
      dragAndDropFeature,
      expandAllFeature,
      searchable ? searchFeature : undefined,
      renamingFeature
    ];
    
    // 过滤掉 undefined 值
    return featureArray.filter((feature): feature is FeatureImplementation => feature !== undefined);
  }, [searchable]);

  // 获取保存的展开状态
  const savedExpandedItems = getSavedExpandedItems();
  const savedSelectedItems = getSavedSelectedItems();
  
  // 使用 headless-tree 的 useTree hook
  const tree = useTree<FileTreeNode>({
    initialState: { 
      expandedItems: savedExpandedItems.length > 0 
        ? savedExpandedItems 
        : Object.keys(treeItems).filter(key => treeItems[key].isDirectory && treeItems[key].id === 'root'), // 默认只展开根目录
      selectedItems: savedSelectedItems
    },
    rootItemId: rootId,
    getItemName: (item) => item.getItemData().title,
    isItemFolder: (item) => Boolean(item.getItemData().isDirectory),
    dataLoader: {
      getItem: (itemId) => treeItems[itemId],
      getChildren: (itemId) => treeItems[itemId]?.children || [],
    },
    indent: 20,
    features,
  });

  // 获取选中的项目
  const getSelectedItems = useCallback(() => {
    return tree.getItems()
      .filter(item => item.isSelected())
      .map(item => item.getItemData())
      .filter(node => node.id !== 'root'); // 过滤掉根节点
  }, [tree]);

  // 保存树状态
  const saveTreeState = useCallback(() => {
    const expandedItems = tree.getItems()
      .filter(item => item.isExpanded())
      .map(item => item.getId());
    
    const selectedItems = tree.getItems()
      .filter(item => item.isSelected())
      .map(item => item.getId());
    
    // 只有当状态真正发生变化时才保存
    const currentState = { expanded: expandedItems, selected: selectedItems };
    const lastState = lastSavedState.current;
    
    const hasExpandedChanged = JSON.stringify(currentState.expanded.sort()) !== JSON.stringify(lastState.expanded.sort());
    const hasSelectedChanged = JSON.stringify(currentState.selected.sort()) !== JSON.stringify(lastState.selected.sort());
    
    if (hasExpandedChanged || hasSelectedChanged) {
      saveExpandedItems(expandedItems);
      saveSelectedItems(selectedItems);
      lastSavedState.current = currentState;
    }
  }, [tree, saveExpandedItems, saveSelectedItems]);

  // 展开指定文件夹的函数
  const expandFolder = useCallback((folderId: string) => {
    // 延迟执行，确保数据已经更新
    setTimeout(() => {
      const folderItem = tree.getItems().find(item => item.getId() === folderId);
      if (folderItem && folderItem.getItemData().isDirectory && !folderItem.isExpanded()) {
        // 直接调用展开方法
        folderItem.getProps().onClick?.({} as React.MouseEvent);
      }
    }, 100);
  }, [tree]);

  // 展开文件夹并保存状态的函数
  const expandFolderAndSave = useCallback((folderId: string) => {
    expandFolder(folderId);
    // 延迟保存状态
    setTimeout(() => {
      saveTreeState();
    }, 200);
  }, [expandFolder, saveTreeState]);

  // 重新设置文件夹创建Hook，使用展开函数
  const folderCreationHook = useFolderCreation({
    onExpandParent: expandFolderAndSave
  });
  
  // 解构文件夹创建Hook的返回值
  const {
    creatingFolder,
    creatingFolderParentId,
    newFolderName,
    newFolderInputRef,
    startCreateFolder,
    cancelCreateFolder,
    handleNewFolderKeyDown,
    handleNewFolderBlur,
    setNewFolderName,
  } = folderCreationHook;

  // 使用拖拽Hook
  const {
    draggedItems,
    isDragging,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    isDropTarget,
    isBeingDragged,
  } = useDragAndDrop({
    onRefresh: () => {
      if (onFileSelect) {
        onFileSelect({ id: 'refresh', title: 'refresh', path: '/refresh', isDirectory: false, data: { refresh: true } });
      }
    },
    getSelectedItems,
    onExpandTarget: expandFolderAndSave
  });

  // 使用删除Hook
  const { handleDelete } = useDeleteOperations({
    onRefresh: () => {
      if (onFileSelect) {
        onFileSelect({ id: 'refresh', title: 'refresh', path: '/refresh', isDirectory: false, data: { refresh: true } });
      }
    },
    getSelectedItems
  });

  // 使用重命名Hook
  const {
    renamingItem,
    newName,
    renameInputRef,
    startRename,
    cancelRename,
    handleRenameKeyDown,
    handleRenameBlur,
    setNewName,
  } = useRenameOperations({
    onRefresh: () => {
      if (onFileSelect) {
        onFileSelect({ id: 'refresh', title: 'refresh', path: '/refresh', isDirectory: false, data: { refresh: true } });
      }
    }
  });

  // 获取文件图标
  const getFileIcon = useCallback((node: FileTreeNode) => {
    if (node.isDirectory) {
      return <Folder size={16} className="text-blue-500" />;
    }
    
    const extension = node.title.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf': return <File size={16} className="text-red-500" />;
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx': return <File size={16} className="text-yellow-500" />;
      case 'css':
      case 'scss':
      case 'sass': return <File size={16} className="text-blue-500" />;
      case 'html': return <File size={16} className="text-orange-500" />;
      case 'json': return <File size={16} className="text-green-500" />;
      case 'md': return <File size={16} className="text-purple-500" />;
      default: return <File size={16} className="text-gray-500" />;
    }
  }, []);





  // 处理右键菜单
  const handleContextMenu = useCallback((event: React.MouseEvent, node: FileTreeNode) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      node
    });
  }, []);

  // 处理空白区域右键菜单
  const handleTreeContainerContextMenu = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const isFileItem = target.closest('.ht-tree-item');
    
    if (!isFileItem) {
      handleContextMenu(event, treeItems[rootId]);
    }
  }, [handleContextMenu, rootId, treeItems]);

  // 处理箭头点击，展开/折叠文件夹
  const handleArrowClick = useCallback((e: React.MouseEvent, item: any) => {
    e.stopPropagation();
    item.getProps().onClick?.(e);
    
    // 延迟保存状态，确保状态已更新
    setTimeout(() => {
      saveTreeState();
    }, 100);
  }, [saveTreeState]);

  // 处理节点点击，同时触发选中状态和自定义回调
  const handleItemClick = useCallback((e: React.MouseEvent, item: any, node: FileTreeNode) => {
    // 如果正在创建文件夹，点击其他地方会取消创建
    if (creatingFolder) {
      cancelCreateFolder();
      return;
    }

    // 如果正在重命名，点击其他地方会取消重命名
    if (renamingItem) {
      cancelRename();
      return;
    }

    // 先触发 headless-tree 的选中状态
    item.getProps().onClick?.(e);
    
    // 然后触发自定义回调
    if (onFileSelect) {
      onFileSelect(node);
    }
  }, [onFileSelect, creatingFolder, cancelCreateFolder, renamingItem, cancelRename]);



  // 监听外部搜索词变化
  useEffect(() => {
    if (searchTerm !== searchString) {
      setSearchString(searchTerm);
      // 使用 headless-tree 的搜索功能
      const treeWithSearch = tree as unknown as { search?: (term: string) => void };
      if (treeWithSearch.search) {
        treeWithSearch.search(searchTerm);
      }
    }
  }, [searchTerm, tree, searchString]);



  // 监听刷新文件树事件
  useEffect(() => {
    const handleRefreshTreeEvent = () => {
      // 保存当前状态
      saveTreeState();
      
      if (onFileSelect) {
        onFileSelect({ id: 'refresh', title: 'refresh', path: '/refresh', isDirectory: false, data: { refresh: true } });
      }
    };

    document.addEventListener('ht-refresh-tree', handleRefreshTreeEvent as EventListener);

    return () => {
      document.removeEventListener('ht-refresh-tree', handleRefreshTreeEvent as EventListener);
    };
  }, [onFileSelect, saveTreeState]);

  // 在组件卸载时保存状态
  useEffect(() => {
    return () => {
      saveTreeState();
    };
  }, [saveTreeState]);

  // 监听键盘事件，支持 Delete 键删除和 F2 键重命名
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 检查是否按下了 Delete 键
      if (e.key === 'Delete' && !e.ctrlKey && !e.altKey && !e.metaKey) {
        e.preventDefault();
        
        // 获取选中的项目
        if (getSelectedItems) {
          const selectedItems = getSelectedItems();
          if (selectedItems.length > 0) {
            handleDelete(); // 删除所有选中的项目
          }
        }
      }
      
      // 检查是否按下了 F2 键（重命名）
      if (e.key === 'F2' && !e.ctrlKey && !e.altKey && !e.metaKey) {
        e.preventDefault();
        
        // 获取选中的项目
        if (getSelectedItems) {
          const selectedItems = getSelectedItems();
          if (selectedItems.length === 1) {
            // 只对单个选中的项目进行重命名
            startRename(selectedItems[0]);
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [getSelectedItems, handleDelete, startRename]);

  // 右键菜单组件
  const ContextMenu = () => {
    if (!contextMenu) return null;

    const handleCreateFolder = () => {
      // 传递当前文件夹的ID作为父文件夹ID
      const parentId = contextMenu.node.id === 'root' ? 'root' : contextMenu.node.id;
      startCreateFolder(contextMenu.node.path || '/', parentId);
      setContextMenu(null);
      
      // 如果不是在根目录创建，确保父文件夹展开
      if (parentId !== 'root') {
        expandFolderAndSave(parentId);
      } else {
        // 延迟保存状态
        setTimeout(() => {
          saveTreeState();
        }, 100);
      }
    };

    const handleUploadFiles = () => {
      // 直接调用文件上传Hook
      const targetFolderId = contextMenu.node.id === 'root' ? 'root' : contextMenu.node.id;
      uploadFilesToFolder(targetFolderId);
      setContextMenu(null);
      
      // 如果不是上传到根目录，确保目标文件夹展开
      if (targetFolderId !== 'root') {
        expandFolderAndSave(targetFolderId);
      } else {
        // 延迟保存状态
        setTimeout(() => {
          saveTreeState();
        }, 100);
      }
    };

    const handleContextMenuDelete = () => {
      // 使用新的删除Hook，支持多选删除且不弹窗确认
      handleDelete(contextMenu.node);
      setContextMenu(null);
      
      // 延迟保存状态
      setTimeout(() => {
        saveTreeState();
      }, 100);
    };

    const handleContextMenuRename = () => {
      // 使用新的重命名Hook，支持内联编辑
      startRename(contextMenu.node);
      setContextMenu(null);
      
      // 延迟保存状态
      setTimeout(() => {
        saveTreeState();
      }, 100);
    };

    return (
      <div
        className="fixed bg-white border border-gray-300 rounded shadow-lg z-50 py-1"
        style={{ left: contextMenu.x, top: contextMenu.y }}
      >
        <button
          className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm flex items-center"
          onClick={handleCreateFolder}
        >
          <Folder size={14} className="mr-2" />
          新建文件夹
        </button>
        <button
          className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm flex items-center"
          onClick={handleUploadFiles}
        >
          <Upload size={14} className="mr-2" />
          上传文件
        </button>
        <hr className="my-1 border-gray-200" />
        {contextMenu.node.id !== 'root' && (
          <>
            <button
              className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm flex items-center"
              onClick={handleContextMenuRename}
            >
              <File size={14} className="mr-2" />
              重命名
            </button>
            <button
              className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm text-red-600 flex items-center"
              onClick={handleContextMenuDelete}
            >
              <Plus size={14} className="mr-2 rotate-45" />
              删除
            </button>
          </>
        )}
      </div>
    );
  };

  return (
    <div className={`ht-file-tree-panel ${className}`}>
      {/* 搜索栏 */}
      {searchable && (
        <div className="ht-search-container">
          <div className="ht-search-input-wrapper">
            <Search size={16} className="ht-search-icon" />
            <input
              type="text"
              placeholder="搜索文件..."
              value={searchString}
              onChange={(e) => setSearchString(e.target.value)}
              className="ht-search-input"
            />
          </div>
        </div>
      )}

      {/* 文件树 */}
      <div
        {...tree.getContainerProps()}
        className="ht-tree-container"
        onContextMenu={handleTreeContainerContextMenu}
        onClick={() => {
          // 点击空白区域取消新建文件夹或重命名
          if (creatingFolder) {
            cancelCreateFolder();
          }
          if (renamingItem) {
            cancelRename();
          }
        }}
      >
        {/* 在根目录创建文件夹时的输入框 */}
        {creatingFolder && creatingFolderParentId === 'root' && (
          <div className="ht-tree-item ht-tree-item-new-folder" style={{ paddingLeft: `20px` }}>
            <div className="ht-tree-item-content">
              <div className="ht-tree-item-arrow"></div>
              <div className="ht-tree-item-icon">
                <Folder size={16} className="text-blue-500" />
              </div>
              <div className="ht-tree-item-title">
                <input
                  ref={newFolderInputRef}
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  onKeyDown={handleNewFolderKeyDown}
                  onBlur={handleNewFolderBlur}
                  placeholder="新文件夹名称..."
                  className="ht-tree-item-input"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          </div>
        )}

        {/* 树节点 */}
        {tree.getItems().map((item) => {
          const node = item.getItemData();
          const isSelected = item.isSelected();
          const isExpanded = item.isExpanded();
          const isFolder = item.getItemData().isDirectory;
          const level = item.getItemMeta().level;
          const itemId = item.getId();

          // 判断是否为拖拽目标
          const isDropTargetState = isDropTarget(itemId);
          const isBeingDraggedState = isBeingDragged(itemId);

          return (
            <React.Fragment key={itemId}>
              <div
                {...item.getProps()}
                className={`ht-tree-item ${isSelected ? 'ht-tree-item-selected' : ''} ${node.isHidden ? 'ht-tree-item-hidden' : ''} ${isDropTargetState ? 'ht-tree-item-drop-target' : ''} ${isBeingDraggedState ? 'ht-tree-item-dragging' : ''}`}
                style={{ paddingLeft: `${level * 20}px` }}
                onContextMenu={(e) => handleContextMenu(e, node)}
                onClick={(e) => handleItemClick(e, item, node)}
                draggable={node.id !== 'root'} // 根节点不可拖拽
                onDragStart={(e) => handleDragStart(e, node)}
                onDragEnd={handleDragEnd}
                onDragOver={(e) => handleDragOver(e, node)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, node)}
              >
                <div className="ht-tree-item-content">
                  <div
                    className="ht-tree-item-arrow"
                    onClick={(e) => isFolder && handleArrowClick(e, item)}
                  >
                    {isFolder && (isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />)}
                  </div>
                  <div className="ht-tree-item-icon">{getFileIcon(node)}</div>
                  <div className="ht-tree-item-title">
                    {renamingItem?.id === itemId ? (
                      <input
                        ref={renameInputRef}
                        type="text"
                        value={newName}
                        onChange={(e) => setNewName(e.target.value)}
                        onKeyDown={handleRenameKeyDown}
                        onBlur={handleRenameBlur}
                        className="ht-tree-item-input"
                        onClick={(e) => e.stopPropagation()}
                      />
                    ) : (
                      item.getItemName()
                    )}
                  </div>
                  {node.metadata?.starred && (<span className="ht-tree-item-star">⭐</span>)}
                  {node.isHidden && (<span className="ht-tree-item-hidden">•</span>)}
                </div>
              </div>
              
              {/* 在对应父文件夹下面显示新建文件夹输入框 */}
              {creatingFolder && creatingFolderParentId === node.id && (
                <div className="ht-tree-item ht-tree-item-new-folder" style={{ paddingLeft: `${(level + 1) * 20}px` }}>
                  <div className="ht-tree-item-content">
                    <div className="ht-tree-item-arrow"></div>
                    <div className="ht-tree-item-icon">
                      <Folder size={16} className="text-blue-500" />
                    </div>
                    <div className="ht-tree-item-title">
                      <input
                        ref={newFolderInputRef}
                        type="text"
                        value={newFolderName}
                        onChange={(e) => setNewFolderName(e.target.value)}
                        onKeyDown={handleNewFolderKeyDown}
                        onBlur={handleNewFolderBlur}
                        placeholder="新文件夹名称..."
                        className="ht-tree-item-input"
                        autoFocus
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                  </div>
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* 右键菜单 */}
      <ContextMenu />

      {/* 拖拽提示 */}
      {isDragging && draggedItems.length > 0 && (
        <div className="ht-drag-indicator">
          正在拖拽: {draggedItems.length === 1 
            ? draggedItems[0].title 
            : `${draggedItems.length} 个项目`
          }
        </div>
      )}
    </div>
  );
};

export default HeadlessFileTreePanel; 
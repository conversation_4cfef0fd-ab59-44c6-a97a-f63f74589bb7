import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
export const NotFound = () => {
  const [time, setTime] = useState(5);
  const navaigate = useNavigate();
  useEffect(() => {
    const timer = setInterval(() => {
      setTime((time) => time - 1);
      if (time === 0) {
        navaigate("/");
      }
    }, 1000);
    return () => clearInterval(timer);
  });
  return (
    <div className="w-full h-full flex justify-center items-center text-2xl text-gray-500">
      访问页面不存在，{time}秒去往
      <Link className="text-blue-500" to="/">
        主页
      </Link>
    </div>
  );
};

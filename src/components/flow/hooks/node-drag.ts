import {Edge, useReactFlow} from "@xyflow/react";
import {type MouseEvent as ReactMouseEvent, useCallback} from "react";
import {CustomNode, useFlowStore} from "@/store/flow-store.ts";
import {createLink, updateNodesPosition} from "@/api/canvas";
import {useEdgeConnect} from "@/components/flow/hooks/edge-connect.ts";
import {useNodeLayout} from "@/components/flow/hooks/node-layout.ts";
import {useHelperLines} from "@/components/flow/hooks/use-helper-lines.ts";

const useNodeDrag = () => {
    const {getIntersectingNodes} = useReactFlow();
    const {setNodes, setEdges} = useFlowStore((state) => ({
        setNodes: state.setNodes,
        setEdges: state.setEdges,
    }));
    // 连线
    const {isValidConnection} = useEdgeConnect()
    const {calculateNodePosition} = useNodeLayout()
    // Helper Lines
    const {helperLines, calculateHelperLines, clearHelperLines} = useHelperLines()
    
    // 计算节点距离
    const calculateDistance = useCallback((node1: CustomNode, node2: CustomNode) => {
        const dx = (node1.position.x + (node1.measured?.width ?? 0) / 2) - (node2.position.x + (node2.measured?.width ?? 0) / 2);
        const dy = (node1.position.y + (node1.measured?.height ?? 0) / 2) - (node2.position.y + (node2.measured?.height ?? 0) / 2);
        return Math.sqrt(dx * dx + dy * dy);
    }, []);
    
    // 获取最近的相交节点
    const getClosestNode = useCallback((node: CustomNode) => {
        const intersections = getIntersectingNodes(node).map((n) => n.id);
        if (intersections.length === 0) {
            return null;
        }
        const nodes = useFlowStore.getState().nodes.filter((n) => intersections.includes(n.id))
        const closestNode = nodes.reduce(
            (res, n) => {
                const d = calculateDistance(node, n);
                if (d < res.distance) {
                    res.distance = d;
                    res.node = n;
                }
                return res;
            },
            {
                distance: Number.MAX_VALUE,
                node: null as CustomNode | null,
            }
        );
        if (!closestNode.node) {
            return null
        }
        // const closeNodeIsSource = closestNode.node.position.x < node.position.x;
        const closeNodeIsSource = true
        return {
            closestEdge: {
                id: closeNodeIsSource ? `tmp-${node.id}-${closestNode.node.id}` : `tmp-${closestNode.node.id}-${node.id}`,
                source: closeNodeIsSource ? closestNode.node.id : node.id,
                target: closeNodeIsSource ? node.id : closestNode.node.id,
            } as Edge,
            closestNode
        };
    }, []);

    const onNodeDrag = useCallback(
        (_: ReactMouseEvent, node: CustomNode) => {
            // 计算辅助线和吸附位置
            const snappedPosition = calculateHelperLines(node);
            
            // 更新节点位置为吸附后的位置
            const updatedNode = {
                ...node,
                position: snappedPosition,
            };

            const {closestEdge, closestNode} = getClosestNode(updatedNode) || {};
            let nextEdges = useFlowStore.getState().edges.filter((e) => !e.className?.includes('temp'));
            if (
                closestEdge &&
                !nextEdges.find(
                    (ne: Edge) =>
                        ne.source === closestEdge.source && ne.target === closestEdge.target,
                ) &&
                isValidConnection(closestEdge)
            ) {
                // 隐藏所有父连线
                nextEdges = nextEdges.map(edge => ({
                    ...edge,
                    className: edge.target === node.id ? 'hidden' : '',
                }))
                nextEdges.push({
                    ...closestEdge, 
                    className: 'temp',
                    type: 'editableEdge',
                    data: { label: '' },
                })
            } else {
                // 显示所有父连线
                nextEdges = nextEdges.map(e => ({...e, className: ''}))
            }
            setEdges(nextEdges)

            // 高亮
            const nextNodes = useFlowStore.getState().nodes.map((n: CustomNode) => ({
                ...n,
                className: n.id === closestNode?.node?.id ? 'highlight' : '',
                // 更新拖拽节点的位置
                position: n.id === node.id ? snappedPosition : n.position,
            }))
            setNodes(nextNodes)

        },
        [getClosestNode, setEdges, calculateHelperLines],
    );

    const onNodeDragStop = useCallback(
        async (_: ReactMouseEvent, node: CustomNode) => {
            // 清除辅助线
            clearHelperLines();
            
            const {closestEdge} = getClosestNode(node) || {};
            // 是否新增连线
            let isNewEdge = false
            let nextEdges = useFlowStore.getState().edges.filter((e) => !e.className?.includes('temp')).map(e => ({
                ...e,
                className: ""
            })) as Edge[];
            if (
                closestEdge &&
                !nextEdges.find(
                    (ne) =>
                        ne.source === closestEdge.source && ne.target === closestEdge.target,
                ) &&
                isValidConnection(closestEdge)
            ) {
                // 接口
                try {
                    await createLink({
                        nid: closestEdge.target,
                        pids: [closestEdge.source],
                        type: "default",
                        label: ''
                    })
                } catch (e) {
                    console.log(e)
                }
                // 删除所有父连线
                nextEdges = nextEdges.filter((e) => e.target !== node.id)
                nextEdges.push({
                    ...closestEdge,
                    type: 'editableEdge',
                    data: { label: '' },
                })
                isNewEdge = true
            }
            setEdges(nextEdges)

            // 保存节点位置
            try {
                if (isNewEdge) {
                    const {x, y} = calculateNodePosition([closestEdge!.source], closestEdge!.target)
                    node.position.x = x
                    node.position.y = y
                }
                await updateNodesPosition({positions: [{nid: node.id, x: node.position.x, y: node.position.y}]})
                const nextNodes = useFlowStore.getState().nodes.map((n: CustomNode) => ({
                    ...n,
                    className: '',
                    position: n.id === node.id ? {x: node.position.x, y: node.position.y} : n.position
                }))
                setNodes(nextNodes)
            } catch (e) {
                console.log(e)
            }
        },
        [getClosestNode, setEdges, clearHelperLines],
    );

    return {
        calculateDistance,
        onNodeDrag,
        onNodeDragStop,
        helperLines // 导出辅助线供组件使用
    }
};

export {useNodeDrag}
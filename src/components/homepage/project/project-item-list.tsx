import {useState} from "react";
import {useNavigate} from "react-router-dom";
import {Dropdown, type MenuProps, message} from "antd";
import {DeleteOutlined, EditOutlined, EllipsisOutlined,} from "@ant-design/icons";
import {CollapseIcon, ExpandIcon, HomeIcon, TagIcon,} from "@/components/icons";
import {ProjectType} from "../response-type";
import {useHomeStore} from "@/store/home-store";
import {deleteProject, updateProject} from "@/api/project";

type Props = {
  project: ProjectType;
  tagColors: { cid: string; color: string }[];
  getCurrentProject: (project: ProjectType) => void;
};
export const ProjectItemList = ({
  project,
  tagColors,
  getCurrentProject,
}: Props) => {
  const navigate = useNavigate();
  const [isExpand, setIsExpand] = useState(false);
  const {
    categories,
    categorySelectList,
    currentCategory,
    modifyProject,
    setCurrentCategory,
    toggleCategory,
    removeProjectByWId,
    removeProjectSyncCategory,
  } = useHomeStore();
  return (
    <div
      key={`workspace-${project.wid}-${crypto.randomUUID()}`}
      className="flex-shrink-0 w-[calc(100%-20px)] h-[65px] bg-[var(--bg-category-tabs)] rounded-md flex items-center justify-between cursor-pointer overflow-hidden px-2.5 hover:w-[calc(100%-20px)] hover:border-2 hover:border-[#EC692D] hover:px-[calc(0.625rem-2px)] transition-all ease-in-out group"
      onClick={() => {
        navigate(`/workerspace?wid=${project.wid}`);
      }}
    >
      <div className="flex gap-5 items-center flex-shrink-0">
        <div
            className="w-[70px] h-[44px] rounded-md flex-shrink-0 overflow-hidden"
          style={{
            backgroundImage: `${
              project.preview
                ? `url(${project.preview})`
                : "url(https://b0.bdstatic.com/30e1068eb5baee85ff39977076276655.jpg@h_1280)"
            }`,
            backgroundSize: "cover",
          }}
        >
          <div className="w-full h-full transform group-hover:scale-110 transition-transform duration-300" style={{
            backgroundImage: `${
                project.preview
                    ? `url(${project.preview})`
                    : "url(https://b0.bdstatic.com/30e1068eb5baee85ff39977076276655.jpg@h_1280)"
            }`,
            backgroundSize: "cover",
          }}/>
        </div>
        <div className="w-1/2 truncate transition-all duration-300">{project.title}</div>
      </div>
      <div className="flex gap-5 items-center justify-between flex-shrink-0">
        <Dropdown
          menu={{ items: categorySelectList as MenuProps["items"] }}
          placement="bottom"
          dropdownRender={() => {
            return (
              <div
                className={`relative bg-[var(--bg-category-tabs)] flex flex-col scrollbar-custom overflow-hidden rounded-md px-2.5 py-2.5 ${
                  isExpand
                    ? "w-[287px] max-h-[556px]"
                    : "w-[104px] max-h-[182px]"
                }`}
                onClick={(e) => e.stopPropagation()}
              >
                {categorySelectList.filter((item: any) => {
                  return item.key !== "-1"
                }).map((DropDownitem) => {
                  return (
                    <div
                      key={DropDownitem?.key}
                      title={(DropDownitem as any).label}
                      className={`flex items-center gap-2 mb-2 cursor-pointer  hover:bg-[var(--bg-category-tabs-hover)] rounded-md px-1 py-1 ${
                        DropDownitem?.key === project.cid
                          ? "bg-[var(--bg-category-tabs-hover)] cursor-default"
                          : ""
                      }`}
                      onClick={async (e) => {
                        if (
                          DropDownitem?.key === project.cid ||
                          DropDownitem?.key === "-1"
                        ) {
                          return;
                        }
                        const body = {
                          wid: project.wid,
                          cid:
                            DropDownitem?.key === "-1"
                              ? "0"
                              : DropDownitem?.key,
                          title: project.title
                        };
                        try {
                          const res = await updateProject(body);
                          if ((res as any)?.code === 0) {
                            modifyProject({
                              ...project,
                              cid: DropDownitem?.key,
                            });
                            toggleCategory(project.cid, DropDownitem?.key);
                            message.success("更新成功");
                            setIsExpand(false);
                          }
                        } catch (error) {
                          message.error((error as any)?.msg);
                        }
                      }}
                    >
                      {DropDownitem?.key === "-1" ? (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <HomeIcon color="#3856ce" size={12} />
                        </div>
                      ) : DropDownitem?.key === "0" ? (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                            background: "#8e9bd6",
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <TagIcon color="#ffffff" className="w-5 h-5" />
                        </div>
                      ) : (
                        <div
                          className="flex justify-center items-center rounded-[6px] "
                          style={{
                            width: "18px",
                            height: "18px",
                            background: tagColors.find(
                              (colorItem) => colorItem.cid === DropDownitem?.key
                            )?.color,
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <TagIcon color="#ffffff" className="w-5 h-5" />
                        </div>
                      )}
                      <div className="truncate">
                        {(DropDownitem as any).label}
                      </div>
                    </div>
                  );
                })}
                <div
                  className="absolute bottom-[2px] right-[2px]"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsExpand(!isExpand);
                  }}
                >
                  {isExpand ? (
                    <ExpandIcon color="var(--text-primary)" />
                  ) : (
                    <CollapseIcon color="var(--text-primary)" />
                  )}
                </div>
              </div>
            );
          }}
        >
          <div
            className="p-1 rounded-md"
            style={{
              backgroundColor:
                project.cid === "0"
                  ? "#8e9bd6"
                  : tagColors.find(
                      (colorItem) => colorItem.cid === project?.cid
                    )?.color,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <TagIcon color="#ffffff" className="w-5 h-5" />
          </div>
        </Dropdown>
        <div className="text-[12px] text-[#a0a0a1] tracking-[0.6px]">
          {new Date(project.last_at * 1000).toLocaleString()}更新
        </div>
        <Dropdown
          placement="bottom"
          dropdownRender={() => {
            const MenuItem = [
              {
                key: "rename",
                label: "重命名",
                icon: <EditOutlined />,
                onClick: () => {
                  getCurrentProject(project);
                },
              },
              {
                key: "delete",
                label: "删除",
                icon: <DeleteOutlined />,
                onClick: async () => {
                  try {
                    const res = await deleteProject({
                      wid: project.wid,
                    });
                    if ((res as any).code === 0) {
                      message.success("删除成功");
                      removeProjectByWId(project.wid);
                      const categoryItem = categories.find(
                        (category) => category.cid === project.cid
                      );
                      const allItem = categories.find(
                        (item) => item.cid === "-1"
                      );
                      if (categoryItem && allItem) {
                        removeProjectSyncCategory(project.cid);
                        if (currentCategory) {
                          setCurrentCategory({
                            ...currentCategory,
                            wnum: currentCategory.wnum - 1,
                          });
                        }
                      }
                    }
                  } catch (error) {
                    message.error("删除失败");
                  }
                },
              },
            ];
            return (
              <div className="bg-white rounded-md shadow-lg py-1">
                {MenuItem.map((menuItem) => (
                  <div
                    key={menuItem.key}
                    className="px-4 py-2 hover:bg-[var(--bg-category-tabs-hover)] flex items-center gap-2 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      menuItem.onClick?.();
                    }}
                  >
                    {menuItem.icon}
                    <span>{menuItem.label}</span>
                  </div>
                ))}
              </div>
            );
          }}
        >
          <EllipsisOutlined
            onClick={(e) => {
              e.stopPropagation();
            }}
          />
        </Dropdown>
      </div>
    </div>
  );
};

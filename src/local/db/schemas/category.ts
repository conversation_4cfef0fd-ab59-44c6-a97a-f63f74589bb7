// 扩展 RxJsonSchema 类型以支持 errorMessage
declare module 'rxdb' {
    interface TopLevelProperty {
        errorMessage?: {
            maxLength?: string; // 最大长度错误信息
            minLength?: string; // 最小长度错误信息
            minimum?: string;   // 最小值错误信息
            maximum?: string;   // 最大值错误信息
            pattern?: string;   // 正则表达式匹配错误信息
            required?: string;  // 必填项错误信息
            type?: string;      // 数据类型错误信息
            [key: string]: string | undefined; // 支持其他自定义键
        };
    }
}
import {RxJsonSchema} from 'rxdb';

export interface CategoryDocType {
    id: string; // 分类ID
    cate_name: string; // 分类名称
    ws_num: number; // 工作区数量
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const categorySchema: RxJsonSchema<CategoryDocType> = {
    title: 'category schema',
    version: 0,
    description: '分类模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100,
        },
        cate_name: {
            type: 'string',
            maxLength: 30,
            errorMessage: {
                maxLength: '分类名称不能超过30个字符'
            }
        },
        ws_num: {
            type: 'integer',
            minimum: 0,
            default: 0,
        },
        create_at: {
            type: 'integer',
            minimum: 0,
        },
        update_at: {
            type: 'integer',
            minimum: 0,
        }
    },
    required: ['id', 'cate_name'],
    indexes: ['cate_name']
};
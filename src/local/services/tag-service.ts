import { getDatabase } from '../db';
import { TagGroupDocType, TagDocType, NodeTagDocType, generateId } from '@/local';

// 定义事件总线，用于通知标签变更
export const TagEvents = {
    // 事件监听器集合
    listeners: new Map<string, Function[]>(),

    // 添加事件监听器
    on(event: string, callback: Function) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)?.push(callback);
    },

    // 移除事件监听器
    off(event: string, callback: Function) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event) || [];
        this.listeners.set(event, callbacks.filter(cb => cb !== callback));
    },

    // 触发事件
    emit(event: string, ...args: any[]) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event) || [];
        callbacks.forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`Error in ${event} event handler:`, error);
            }
        });
    }
};

// 标签组相关接口
export interface CreateTagGroupReq {
    title: string;
}

export interface UpdateTagGroupReq {
    id: string;
    title: string;
}

export interface DeleteTagGroupReq {
    id: string;
}

// 标签相关接口
export interface CreateTagReq {
    groupId: string;
    name: string;
}

export interface UpdateTagReq {
    id: string;
    name: string;
}

export interface DeleteTagReq {
    id: string;
}

export interface MoveTagReq {
    tagId: string;
    fromGroupId: string;
    toGroupId: string;
}

// 节点标签关联相关接口
export interface AddNodeTagReq {
    nodeId: string;
    tagId: string;
}

export interface RemoveNodeTagReq {
    nodeId: string;
    tagId: string;
}

export interface UpdateNodeTagsReq {
    nodeId: string;
    tagIds: string[];
}

// 排序相关接口
export interface ReorderGroupsReq {
    groupIds: string[];
}

export interface ReorderTagsReq {
    groupId: string;
    tagIds: string[];
}

export interface GetNodeTagsReq {
    nodeId: string;
}

// 响应接口
export interface TagGroupWithTags {
    id: string;
    title: string;
    tags: Array<{
        id: string;
        name: string;
    }>;
}

export interface ListTagGroupsResp {
    groups: TagGroupWithTags[];
}

export class TagService {
    /**
     * 创建标签组
     */
    async createGroup(req: CreateTagGroupReq): Promise<{ id: string }> {
        const db = await getDatabase();
        const id = generateId();
        try {
            // 获取当前最大的排序值，新项目排在最后
            const existingGroups = await db.tagGroups.find().exec();
            const maxSortOrder = existingGroups.reduce((max: number, group: TagGroupDocType) => {
                return Math.max(max, group.sort_order || 0);
            }, 0);

            const groupData: TagGroupDocType = {
                id,
                title: req.title,
                sort_order: maxSortOrder + 1,
                create_at: Math.floor(Date.now() / 1000),
                update_at: Math.floor(Date.now() / 1000)
            };

            await db.tagGroups.insert(groupData);
            console.log('标签组创建成功:', id, groupData);

            // 触发事件
            TagEvents.emit('groupCreated', groupData);

            return { id };
        } catch (error) {
            console.error('创建标签组失败:', error);
            throw error;
        }
    }

    /**
     * 更新标签组
     */
    async updateGroup(req: UpdateTagGroupReq): Promise<void> {
        const db = await getDatabase();
        
        // 重试机制，最多重试3次
        const maxRetries = 3;
        let attempt = 0;
        
        while (attempt < maxRetries) {
            try {
                const group = await db.tagGroups.findOne({
                    selector: { id: req.id }
                }).exec();

                if (!group) {
                    throw new Error('标签组不存在');
                }

                // 使用原子操作避免版本冲突
                await group.incrementalModify((docData: TagGroupDocType) => {
                    docData.title = req.title;
                    docData.update_at = Math.floor(Date.now() / 1000);
                    return docData;
                });

                console.log('标签组更新成功:', req.id);
                
                // 触发事件
                TagEvents.emit('groupUpdated', { id: req.id, title: req.title });
                return; // 成功则退出
                
            } catch (error: any) {
                attempt++;
                
                // 如果是冲突错误且还有重试次数，等待后重试
                if (error?.code === 'CONFLICT' && attempt < maxRetries) {
                    console.warn(`标签组更新冲突，正在重试 (${attempt}/${maxRetries})...`);
                    // 指数退避，避免立即重试
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
                    continue;
                }
                
                console.error('更新标签组失败:', error);
                throw error;
            }
        }
    }

    /**
     * 删除标签组
     */
    async deleteGroup(req: DeleteTagGroupReq): Promise<void> {
        const db = await getDatabase();
        
        try {
            // 先删除该组下的所有标签
            await db.tags.find({
                selector: { group_id: req.id }
            }).remove();

            // 删除标签组
            await db.tagGroups.find({
                selector: { id: req.id }
            }).remove();

            console.log('标签组删除成功:', req.id);
            
            // 触发事件
            TagEvents.emit('groupDeleted', req.id);
        } catch (error) {
            console.error('删除标签组失败:', error);
            throw error;
        }
    }

    /**
     * 创建标签
     */
    async createTag(req: CreateTagReq): Promise<{ id: string }> {
        const db = await getDatabase();
        const id = generateId();

        try {
            // 验证标签组是否存在
            const group = await db.tagGroups.findOne({
                selector: { id: req.groupId }
            }).exec();

            if (!group) {
                throw new Error('标签组不存在');
            }

            // 获取该组内当前最大的排序值，新标签排在最后
            const existingTags = await db.tags.find({
                selector: { group_id: req.groupId }
            }).exec();
            const maxSortOrder = existingTags.reduce((max: number, tag: TagDocType) => {
                return Math.max(max, tag.sort_order || 0);
            }, 0);

            const tagData: TagDocType = {
                id,
                group_id: req.groupId,
                name: req.name,
                sort_order: maxSortOrder + 1,
                create_at: Math.floor(Date.now() / 1000),
                update_at: Math.floor(Date.now() / 1000)
            };

            await db.tags.insert(tagData);
            console.log('标签创建成功:', id, tagData);

            // 触发事件
            TagEvents.emit('tagCreated', tagData);

            return { id };
        } catch (error) {
            console.error('创建标签失败:', error);
            throw error;
        }
    }

    /**
     * 更新标签
     */
    async updateTag(req: UpdateTagReq): Promise<void> {
        const db = await getDatabase();
        
        // 重试机制，最多重试3次
        const maxRetries = 3;
        let attempt = 0;
        
        while (attempt < maxRetries) {
            try {
                const tag = await db.tags.findOne({
                    selector: { id: req.id }
                }).exec();

                if (!tag) {
                    throw new Error('标签不存在');
                }

                // 使用原子操作避免版本冲突
                await tag.incrementalModify((docData: TagDocType) => {
                    docData.name = req.name;
                    docData.update_at = Math.floor(Date.now() / 1000);
                    return docData;
                });

                console.log('标签更新成功:', req.id);
                
                // 触发事件
                TagEvents.emit('tagUpdated', { id: req.id, name: req.name });
                return; // 成功则退出
                
            } catch (error: any) {
                attempt++;
                
                // 如果是冲突错误且还有重试次数，等待后重试
                if (error?.code === 'CONFLICT' && attempt < maxRetries) {
                    console.warn(`标签更新冲突，正在重试 (${attempt}/${maxRetries})...`);
                    // 指数退避，避免立即重试
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
                    continue;
                }
                
                console.error('更新标签失败:', error);
                throw error;
            }
        }
    }

    /**
     * 删除标签
     */
    async deleteTag(req: DeleteTagReq): Promise<void> {
        const db = await getDatabase();
        
        try {
            await db.tags.find({
                selector: { id: req.id }
            }).remove();

            console.log('标签删除成功:', req.id);
            
            // 触发事件
            TagEvents.emit('tagDeleted', req.id);
        } catch (error) {
            console.error('删除标签失败:', error);
            throw error;
        }
    }

    /**
     * 移动标签到其他组
     */
    async moveTag(req: MoveTagReq): Promise<void> {
        const db = await getDatabase();
        
        // 重试机制，最多重试3次
        const maxRetries = 3;
        let attempt = 0;
        
        while (attempt < maxRetries) {
            try {
                // 验证目标组是否存在
                const toGroup = await db.tagGroups.findOne({
                    selector: { id: req.toGroupId }
                }).exec();

                if (!toGroup) {
                    throw new Error('目标标签组不存在');
                }

                // 查找并更新标签
                const tag = await db.tags.findOne({
                    selector: { id: req.tagId }
                }).exec();

                if (!tag) {
                    throw new Error('标签不存在');
                }

                // 使用原子操作避免版本冲突
                await tag.incrementalModify((docData: TagDocType) => {
                    docData.group_id = req.toGroupId;
                    docData.update_at = Math.floor(Date.now() / 1000);
                    return docData;
                });

                console.log('标签移动成功:', req.tagId, 'from', req.fromGroupId, 'to', req.toGroupId);
                
                // 触发事件
                TagEvents.emit('tagMoved', req);
                return; // 成功则退出
                
            } catch (error: any) {
                attempt++;
                
                // 如果是冲突错误且还有重试次数，等待后重试
                if (error?.code === 'CONFLICT' && attempt < maxRetries) {
                    console.warn(`标签移动冲突，正在重试 (${attempt}/${maxRetries})...`);
                    // 指数退避，避免立即重试
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
                    continue;
                }
                
                console.error('移动标签失败:', error);
                throw error;
            }
        }
    }

    /**
     * 获取所有标签组和标签
     */
    async listGroups(): Promise<ListTagGroupsResp> {
        try {
            const db = await getDatabase();
            
            // 检查数据库是否正常初始化
            if (!db || !db.tagGroups || !db.tags) {
                console.warn('数据库未正确初始化，返回空结果');
                return { groups: [] };
            }

            console.log('获取标签组列表');

            // 查询所有标签组，按 sort_order 排序
            const groups = await db.tagGroups.find({
                sort: [{ sort_order: 'asc' }]
            }).exec();
            
            // 处理空结果情况
            if (!groups || groups.length === 0) {
                console.log('未找到任何标签组');
                return { groups: [] };
            }
            
            console.log(`找到${groups.length}个标签组`);

            // 查询所有标签，按 sort_order 排序
            const tags = await db.tags.find({
                sort: [{ group_id: 'asc' }, { sort_order: 'asc' }]
            }).exec();
            
            console.log(`找到${tags ? tags.length : 0}个标签`);

            // 组织数据结构
            const result: TagGroupWithTags[] = groups.map((group: TagGroupDocType) => {
                const groupTags = tags
                    ? tags
                        .filter((tag: TagDocType) => tag.group_id === group.id)
                        .sort((a: TagDocType, b: TagDocType) => (a.sort_order || 0) - (b.sort_order || 0)) // 额外确保排序
                        .map((tag: TagDocType) => ({
                            id: tag.id,
                            name: tag.name
                        }))
                    : []; // 如果 tags 为空，返回空数组

                return {
                    id: group.id,
                    title: group.title,
                    tags: groupTags
                };
            });

            console.log('返回标签组列表结果, 组数量:', result.length);
            return { groups: result };
        } catch (error) {
            console.error('获取标签组列表失败:', error);
            // 返回空结果而不是抛出错误，确保UI不会崩溃
            return { groups: [] };
        }
    }

    /**
     * 根据名称搜索标签
     */
    async searchTags(query: string): Promise<Array<{ groupId: string; groupTitle: string; tag: { id: string; name: string } }>> {
        const db = await getDatabase();
        
        try {
            
            // 查询匹配的标签
            const tags = await db.tags.find({
                selector: {
                    name: {
                        $regex: new RegExp(query, 'i')
                    }
                }
            }).exec();

            // 获取相关的标签组信息
            const results = [];
            for (const tag of tags) {
                const group = await db.tagGroups.findOne({
                    selector: { id: tag.group_id }
                }).exec();

                if (group) {
                    results.push({
                        groupId: group.id,
                        groupTitle: group.title,
                        tag: {
                            id: tag.id,
                            name: tag.name
                        }
                    });
                }
            }

            return results;
        } catch (error) {
            console.error('搜索标签失败:', error);
            throw error;
        }
    }

    /**
     * 为节点添加标签
     */
    async addNodeTag(req: AddNodeTagReq): Promise<{ id: string }> {
        const db = await getDatabase();
        const id = generateId();

        try {
            // 检查节点是否存在
            const node = await db.nodes.findOne({
                selector: { id: req.nodeId }
            }).exec();

            if (!node) {
                throw new Error('节点不存在');
            }

            // 检查标签是否存在
            const tag = await db.tags.findOne({
                selector: { id: req.tagId }
            }).exec();

            if (!tag) {
                throw new Error('标签不存在');
            }

            // 检查是否已经关联
            const existing = await db.nodeTags.findOne({
                selector: { 
                    node_id: req.nodeId,
                    tag_id: req.tagId
                }
            }).exec();

            if (existing) {
                throw new Error('标签已关联到该节点');
            }

            const nodeTagData: NodeTagDocType = {
                id,
                node_id: req.nodeId,
                tag_id: req.tagId,
                create_at: Math.floor(Date.now() / 1000),
                update_at: Math.floor(Date.now() / 1000)
            };

            await db.nodeTags.insert(nodeTagData);
            console.log('节点标签关联成功:', id, nodeTagData);

            // 触发事件
            TagEvents.emit('nodeTagAdded', { nodeId: req.nodeId, tagId: req.tagId });

            return { id };
        } catch (error) {
            console.error('添加节点标签失败:', error);
            throw error;
        }
    }

    /**
     * 移除节点标签
     */
    async removeNodeTag(req: RemoveNodeTagReq): Promise<void> {
        const db = await getDatabase();

        try {
            await db.nodeTags.find({
                selector: { 
                    node_id: req.nodeId,
                    tag_id: req.tagId
                }
            }).remove();

            console.log('节点标签移除成功:', req.nodeId, req.tagId);

            // 触发事件
            TagEvents.emit('nodeTagRemoved', { nodeId: req.nodeId, tagId: req.tagId });
        } catch (error) {
            console.error('移除节点标签失败:', error);
            throw error;
        }
    }

    /**
     * 获取节点的所有标签
     */
    async getNodeTags(req: GetNodeTagsReq): Promise<Array<{ id: string; name: string; groupId: string; groupTitle: string }>> {
        const db = await getDatabase();

        try {
            // 查询节点的标签关联
            const nodeTags = await db.nodeTags.find({
                selector: { node_id: req.nodeId }
            }).exec();

            const results = [];
            for (const nodeTag of nodeTags) {
                // 获取标签详情
                const tag = await db.tags.findOne({
                    selector: { id: nodeTag.tag_id }
                }).exec();

                if (tag) {
                    // 获取标签组详情
                    const group = await db.tagGroups.findOne({
                        selector: { id: tag.group_id }
                    }).exec();

                    if (group) {
                        results.push({
                            id: tag.id,
                            name: tag.name,
                            groupId: group.id,
                            groupTitle: group.title
                        });
                    }
                }
            }

            return results;
        } catch (error) {
            console.error('获取节点标签失败:', error);
            throw error;
        }
    }

    /**
     * 批量更新节点标签
     */
    async updateNodeTags(req: UpdateNodeTagsReq): Promise<void> {
        const db = await getDatabase();

        try {
            // 获取当前节点的所有标签
            const currentNodeTags = await db.nodeTags.find({
                selector: { node_id: req.nodeId }
            }).exec();

            const currentTagIds = currentNodeTags.map((nt: NodeTagDocType) => nt.tag_id);
            const newTagIds = req.tagIds;

            // 找出需要删除的标签
            const toRemove = currentTagIds.filter((tagId: string) => !newTagIds.includes(tagId));
            
            // 找出需要添加的标签
            const toAdd = newTagIds.filter(tagId => !currentTagIds.includes(tagId));

            // 删除不需要的标签关联
            for (const tagId of toRemove) {
                await this.removeNodeTag({ nodeId: req.nodeId, tagId });
            }

            // 添加新的标签关联
            for (const tagId of toAdd) {
                await this.addNodeTag({ nodeId: req.nodeId, tagId });
            }

            console.log('节点标签批量更新成功:', req.nodeId);

            // 触发事件
            TagEvents.emit('nodeTagsUpdated', { nodeId: req.nodeId, tagIds: req.tagIds });
        } catch (error) {
            console.error('批量更新节点标签失败:', error);
            throw error;
        }
    }

    /**
     * 根据标签搜索节点
     */
    async searchNodesByTag(tagId: string): Promise<Array<{ nodeId: string; tagId: string }>> {
        const db = await getDatabase();

        try {
            const nodeTags = await db.nodeTags.find({
                selector: { tag_id: tagId }
            }).exec();

            return nodeTags.map((nt: NodeTagDocType) => ({
                nodeId: nt.node_id,
                tagId: nt.tag_id
            }));
        } catch (error) {
            console.error('根据标签搜索节点失败:', error);
            throw error;
        }
    }

    /**
     * 移除节点的所有标签（当节点被删除时使用）
     */
    async removeAllNodeTags(nodeId: string): Promise<void> {
        const db = await getDatabase();

        try {
            await db.nodeTags.find({
                selector: { node_id: nodeId }
            }).remove();

            console.log('移除节点所有标签成功:', nodeId);

            // 触发事件
            TagEvents.emit('allNodeTagsRemoved', { nodeId });
        } catch (error) {
            console.error('移除节点所有标签失败:', error);
            throw error;
        }
    }

    /**
     * 重新排序标签组
     */
    async reorderGroups(req: ReorderGroupsReq): Promise<void> {
        const db = await getDatabase();

        try {
            console.log('开始重新排序标签组:', req.groupIds);

            // 使用事务确保原子性操作
            for (let i = 0; i < req.groupIds.length; i++) {
                const groupId = req.groupIds[i];
                const sortOrder = i + 1; // 从1开始编号

                const group = await db.tagGroups.findOne({
                    selector: { id: groupId }
                }).exec();

                if (group) {
                    await group.update({
                        $set: {
                            sort_order: sortOrder,
                            update_at: Math.floor(Date.now() / 1000)
                        }
                    });
                } else {
                    console.warn('标签组不存在:', groupId);
                }
            }

            console.log('标签组重新排序成功');

            // 触发事件
            TagEvents.emit('groupsReordered', req.groupIds);
        } catch (error) {
            console.error('重新排序标签组失败:', error);
            throw error;
        }
    }

    /**
     * 重新排序组内标签
     */
    async reorderTags(req: ReorderTagsReq): Promise<void> {
        const db = await getDatabase();

        try {
            console.log('开始重新排序标签:', req.groupId, req.tagIds);

            // 验证标签组是否存在
            const group = await db.tagGroups.findOne({
                selector: { id: req.groupId }
            }).exec();

            if (!group) {
                throw new Error('标签组不存在');
            }

            // 使用事务确保原子性操作
            for (let i = 0; i < req.tagIds.length; i++) {
                const tagId = req.tagIds[i];
                const sortOrder = i + 1; // 从1开始编号

                const tag = await db.tags.findOne({
                    selector: { 
                        id: tagId,
                        group_id: req.groupId 
                    }
                }).exec();

                if (tag) {
                    await tag.update({
                        $set: {
                            sort_order: sortOrder,
                            update_at: Math.floor(Date.now() / 1000)
                        }
                    });
                } else {
                    console.warn('标签不存在或不属于指定组:', tagId, req.groupId);
                }
            }

            console.log('标签重新排序成功');

            // 触发事件
            TagEvents.emit('tagsReordered', { groupId: req.groupId, tagIds: req.tagIds });
        } catch (error) {
            console.error('重新排序标签失败:', error);
            throw error;
        }
    }
}
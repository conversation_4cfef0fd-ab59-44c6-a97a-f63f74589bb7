# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 开发命令

**启动开发服务器：**

```bash
pnpm dev
```

**构建生产版本：**

```bash
pnpm build
```

**预览构建版本：**

```bash
pnpm preview
```

**部署到 GitHub Pages：**

```bash
pnpm deploy
```

## 项目架构

### 核心技术栈

- **前端框架**: React 18 + TypeScript
- **状态管理**: Zustand 全局状态管理
- **路由系统**: React Router 6
- **构建工具**: Vite 自定义配置
- **样式方案**: TailwindCSS + Styled Components + SCSS
- **UI 组件库**: shadcn/ui, Lucide React
- **本地数据库**: RxDB + Dexie 存储 (IndexedDB)

### 技术选型最佳实践

- 优先使用 tailwindcss + shadcn，使用 Context7 并遵循最佳规范，避免使用 antd 进行组件开发

### 应用结构

**主要页面：**

- `/` 和 `/home` - 项目首页，包含分类和项目管理
- `/workerspace` - 主工作区，多面板界面（PDF、流程图、笔记、聊天）
- `/login` 和 `/register` - 用户认证页面
- `/tag-management` - 标签管理界面

**核心组件架构：**

**工作区面板系统 (`src/pages/workspace/`)：**

- 多面板可调整大小界面，包含 PDF 查看器、流程图编辑器、笔记编辑器和聊天
- 动态面板定位，支持贴边吸附功能
- Z-index 层级管理
- 自动截图系统用于工作区预览

**流程图系统 (`src/components/flow/`)：**

- 基于 React Flow (@xyflow/react) 的节点式图表编辑
- 拖拽界面，自定义节点和连线
- 键盘快捷键和右键菜单
- 对齐辅助线
- 支持 Markdown 的节点编辑

**PDF 系统 (`src/components/pdf/`)：**

- 基于 pdfjs-dist 的自定义 PDF 高亮和标注系统
- 区域和文本高亮工具，支持笔记附件
- PDF 查看器，支持缩放和导航控制
- 高亮内容管理和持久化

**聊天系统 (`src/components/chat-panel/`)：**

- AI 聊天集成，支持流式响应
- 会话管理和对话历史
- 引用选择器，支持 PDF 高亮和流程图节点
- 聊天工具栏，支持模型选择

**笔记系统 (`src/components/note/`)：**

- 使用 Vditor 的富文本编辑器
- Markdown 支持，数学公式渲染 (KaTeX)
- 自动保存功能
- 与工作区上下文集成

### 状态管理模式

**Store 结构 (`src/store/`)：**

- `workerspace-store/` - 工作区专用状态管理
  - `store.ts` - 主工作区状态 (wid, refs, snapshots)
  - `flow-store.ts` - 流程图状态和节点管理
  - `chat-store.ts` - 聊天对话和会话
  - `highlighter-store.ts` - PDF 高亮状态
  - `edit-node-store.ts` - 节点编辑界面状态
- `global.ts` - 全局应用状态
- `panel-open-store.tsx` - 面板可见性和定位

### 数据库架构 (`src/local/db/`)

**数据集合：**

- `workspaces` - 工作区元数据
- `categories` - 项目分类
- `notes` - 笔记文档
- `nodes` - 流程图节点
- `edges` - 流程图连接
- `sessions` - 聊天会话
- `rounds` - 聊天消息轮次
- `attachments` - 文件附件
- `marks` - PDF 高亮和标注

**数据库特性：**

- 自动时间戳钩子 (create_at, update_at)
- AJV 模式验证
- RxDB + Dexie 存储，支持离线优先功能

### API 集成 (`src/api/`)

**模块化 API 结构：**

- `chat/` - AI 聊天完成接口，支持流式传输
- `pdf/` - PDF 上传和管理
- `image/` - 图片上传，OSS 集成
- `canvas/` - 画布/流程图操作
- `notebook/` - 笔记管理
- `project/` - 项目 CRUD 操作
- `category/` - 分类管理
- `user/` - 用户认证

### 关键开发模式

**组件组织：**

- 每个主要功能都有自己的目录，包含组件、钩子和类型
- 共享工具在 `src/tools/` 和 `src/lib/`
- 类型定义在 `src/types/`

**Vite 配置：**

- 自定义基础路径 `/pdf-build/` 用于 GitHub Pages 部署
- SSE 端点模拟用于 AI 聊天测试
- 外部 API 集成的代理配置
- vite-plugin-svgr 支持 SVG 组件

**样式方案：**

- 优先 TailwindCSS 用于实用类

除非有必要踩使用以下样式方案

- Styled Components 用于复杂组件样式
- SCSS 用于组件特定样式
- CSS-in-JS 用于动态样式

### 本地开发说明

- 使用 pnpm 作为包管理器
- 启用 TypeScript 严格模式，路径映射 (@/_ → src/_)
- Vite 开发服务器热重载
- 浏览器支持包括现代浏览器（Chrome、Firefox、Safari 最新版本）
- 工作区系统需要 `wid` 查询参数进行工作区识别

### 部署

- 为 GitHub Pages 部署构建，带基础路径配置
- 静态构建输出在 `build/` 目录
- 启用 Sourcemaps 用于调试

## 输出

始终使用中文输出

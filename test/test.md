
TOKENIZERS_PARALLELISM=false python -m vllm.entrypoints.openai.api_server --served-model-name ui-tars --model ./model/UI-TARS-7B-DPO --tensor-parallel-size 4 --dtype float16 --gpu-memory-utilization 0.8

vllm serve ./model/Qwen2.5-VL-32B-Instruct --port 8000 --host 0.0.0.0 --dtype float16 --limit-mm-per-prompt image=5,video=5 --tensor-parallel-size 4 --gpu-memory-utilization 0.8

TOKENIZERS_PARALLELISM=false vllm serve ./model/UI-TARS-7B-DPO --served-model-name ui-tars --port 8000 --host 0.0.0.0 --dtype float16 --limit-mm-per-prompt image=5,video=5 --tensor-parallel-size 4 
--gpu-memory-utilization 0.8

vllm serve ./model/UI-TARS-7B-DPO --served-model-name ui-tars --port 8000 --host 0.0.0.0 --limit-mm-per-prompt image=5

vllm serve ./model/Qwen2.5-VL-32B-Instruct --port 8000 --host 0.0.0.0 --dtype bfloat16 --max-model-len 8192 --limit-mm-per-prompt image=5,video=5
vllm serve ./model/Qwen3-32B --port 8100 --host 0.0.0.0 --enable-auto-tool-choice --tool-call-parser hermes --dtype float16 --gpu-memory-utilization 0.8 --tensor-parallel-size 8 --chat-template ./model/Qwen3-32B/qwen3_nonthinking.jinja
vllm serve ./model/Qwen3-32B --port 8100 --host 0.0.0.0 --enable-auto-tool-choice --tool-call-parser hermes --dtype bfloat16 --chat-template ./model/Qwen3-32B/qwen3_nonthinking.jinja --max_model_len 32768 --gpu-memory-utilization 0.95

vllm serve ./model/Qwen2.5-VL-72B-Instruct-AWQ --port 8000 --host 0.0.0.0 --max-model-len 8192 --limit-mm-per-prompt image=5,video=5

ssh -N -L 0.0.0.0:8000:localhost:8000 ssh <EMAIL>


export VLLM_COMMIT=f49777ba62b4926d0f8c100ab06edb03c5c10098 
pip install https://wheels.vllm.ai/${VLLM_COMMIT}/vllm-1.0.0.dev-cp38-abi3-manylinux1_x86_64.whl

export VLLM_COMMIT=f49777ba62b4926d0f8c100ab06edb03c5c10098 # use full commit hash from the main branch
uv pip install vllm --extra-index-url https://wheels.vllm.ai/${VLLM_COMMIT}


## 输入
1. 一段领域描述代表这个Agent生成哪个领域的问题，可以用{topic}引用该参数
2. 用户问题（主张来自于对该问题的分析），可以用{question}引用该参数

根据输入的两个参数，生成一段系统prompt，用于输出一系列的正向主张以及对应的验证逻辑。 要求从因果延伸的角度思考，现象的根本原因是否是潜在原因。



## 角色与目标
你是一个专业的问题分析专家，擅长针对{topic}领域的问题进行深度分析。你的任务是根据用户提出的"{question}"，生成一系列正向主张(支持该问题的论点)以及对应的严谨验证逻辑。

## 输出要求
1. 针对每个正向主张，提供2-3层因果延伸分析，探究根本原因
2. 每个主张必须附带可操作的验证方法
3. 验证逻辑需要包含：
   - 验证步骤
   - 所需数据/证据类型
   - 可能的验证结果及其含义

## 输出格式
### 正向主张1：[简明扼要的陈述]
#### 因果链分析：
1. 直接原因：[最表层的原因]
2. 潜在原因：[更深层次的因素]
3. 根本原因：[最底层的驱动因素]
#### 验证逻辑：
    - 验证方法：[具体操作步骤]
    - 证据要求：[需要收集的数据类型]
    - 结果解读：[可能的结果如何支持/反驳该主张]

### 正向主张2：[...]
(同上结构)

## 思考要求
1. 每个主张必须经过至少两层因果推理
2. 验证方法要具体可行，避免空泛
3. 考虑反事实验证的可能性
4. 注意区分相关性和因果性


curl 'https://www.315jiage.cn/graphql' \
-H 'accept: application/graphql+json, application/json' \
-H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7,zh-TW;q=0.6' \
-H 'content-type: application/json' \
-b 'Hm_lvt_4cce664ec5d8326cc457ab09053c15b2=**********; HMACCOUNT=6AFA7FD115A88945; Hm_lpvt_4cce664ec5d8326cc457ab09053c15b2=**********' \
-H 'origin: https://www.315jiage.cn' \
-H 'priority: u=1, i' \
-H 'referer: https://www.315jiage.cn/search/certification?keyword=%E5%9B%BD%E8%8D%AF%E5%87%86%E5%AD%97H20067965' \
-H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'sec-fetch-dest: empty' \
-H 'sec-fetch-mode: cors' \
-H 'sec-fetch-site: same-origin' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36' \
-H 'x-search-platform-env: ppe_flush_dy' \
--data-raw $'{"operationName":"search_article","query":"query search_article($req: ArticlePaginationInput\u0021) {\\n  articles(request: $req) {\\n    nodes {\\n      contentType\\n      ...MobileListItemInfo\\n      ...PCSearchListItemInfo\\n      id\\n    }\\n    pageInfo {\\n      hasNextPage\\n      hasPreviousPage\\n    }\\n  }\\n}\\n\\nfragment MobileListItemInfo on Article {\\n  id\\n  contentType\\n  ...MobileArticleCacheInfo\\n  ...MobileListItemVideoInfo\\n  ...ListItemMedicinePriceInfo\\n  ...ListItemServiceInfo\\n  ...MobileListItemConsultInfo\\n  ...MobileListItemArticleInfo\\n  __typename\\n}\\n\\nfragment PCSearchListItemInfo on Article {\\n  id\\n  contentType\\n  title\\n  summary\\n  date\\n  titleImg\\n  category {\\n    name\\n    id\\n  }\\n  latestComment {\\n    body\\n    id\\n  }\\n  updateDate\\n  ...ListItemMedicinePriceInfo\\n  ...ListItemServiceInfo\\n  ...PCSearchListItemVideoInfo\\n  __typename\\n}\\n\\nfragment MobileArticleCacheInfo on Article {\\n  id\\n  contentType\\n  __typename\\n}\\n\\nfragment MobileListItemVideoInfo on Article {\\n  id\\n  title\\n  titleImg\\n  date\\n  contentTypeContentId\\n  video {\\n    outVideoId\\n    duration\\n    id\\n  }\\n  user {\\n    photo\\n    name\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment ListItemMedicinePriceInfo on Article {\\n  id\\n  ...ListItemMedicineInfo\\n  medicine {\\n    limited\\n    retailPrice\\n    guidedRetailPrice\\n    instockState {\\n      minPrice\\n      supplierNum\\n    }\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment ListItemServiceInfo on Article {\\n  id\\n  title\\n  titleImg\\n  summary\\n  updateDate\\n  contentType\\n  contentTypeContentId\\n  medicine {\\n    name\\n    limited\\n    retailPrice\\n    guidedRetailPrice\\n    instockState {\\n      minPrice\\n      supplierNum\\n    }\\n    id\\n  }\\n  latestComment {\\n    body\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment MobileListItemConsultInfo on Article {\\n  id\\n  title\\n  titleImg\\n  summary\\n  date\\n  updateDate\\n  latestComment {\\n    body\\n    id\\n  }\\n  consult {\\n    siteReplyTime\\n    replies {\\n      replyTime\\n      id\\n    }\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment MobileListItemArticleInfo on Article {\\n  id\\n  title\\n  titleImg\\n  summary\\n  date\\n  updateDate\\n  latestComment {\\n    body\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment PCSearchListItemVideoInfo on Article {\\n  id\\n  title\\n  titleImg\\n  summary\\n  date\\n  updateDate\\n  video {\\n    description\\n    id\\n  }\\n  latestComment {\\n    body\\n    id\\n  }\\n  __typename\\n}\\n\\nfragment ListItemMedicineInfo on Article {\\n  id\\n  title\\n  date\\n  titleImg\\n  updateDate\\n  contentType\\n  contentTypeContentId\\n  categoryId\\n  disease {\\n    name\\n    id\\n  }\\n  latestComment {\\n    body\\n    date\\n    id\\n  }\\n  summary\\n  medicine {\\n    productName\\n    productType\\n    name\\n    norm\\n    unit\\n    rx\\n    herb\\n    isMedicine\\n    limited\\n    model\\n    approvalNumbers(first: 2) {\\n      number\\n      id\\n    }\\n    factory {\\n      name\\n      id\\n    }\\n    standard\\n    licenseNumber\\n    id\\n  }\\n  __typename\\n}\\n","variables":{"req":{"first":15,"after":0,"sort":"RECOMMEND","filter":{"keyword":"国药准字*********","keywordType":"CERTIFICATION","contentType":null}}}}'



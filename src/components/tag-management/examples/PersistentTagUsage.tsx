import React, { useEffect } from 'react';
import { useTagStore, useTagGroups, useTagActions } from '../stores/useTagStore';

/**
 * 持久化标签管理使用示例
 * 展示如何使用新的持久化TagStore
 */
export const PersistentTagUsage: React.FC = () => {
  const { loadTagGroups, isLoading, error } = useTagStore();
  const tagGroups = useTagGroups();
  const { addGroup, addTag, updateGroup, deleteGroup } = useTagActions();

  // 组件挂载时自动加载数据
  useEffect(() => {
    loadTagGroups();
  }, [loadTagGroups]);

  const handleCreateGroup = async () => {
    try {
      const id = await addGroup({ title: '新标签组', tags: [] });
      console.log('创建标签组成功，ID:', id);
    } catch (error) {
      console.error('创建标签组失败:', error);
    }
  };

  const handleCreateTag = async (groupId: string) => {
    try {
      const id = await addTag(groupId, { name: '新标签' });
      console.log('创建标签成功，ID:', id);
    } catch (error) {
      console.error('创建标签失败:', error);
    }
  };

  const handleUpdateGroup = async (groupId: string) => {
    try {
      await updateGroup(groupId, { title: '更新后的标签组名称' });
      console.log('更新标签组成功');
    } catch (error) {
      console.error('更新标签组失败:', error);
    }
  };

  const handleDeleteGroup = async (groupId: string) => {
    try {
      await deleteGroup(groupId);
      console.log('删除标签组成功');
    } catch (error) {
      console.error('删除标签组失败:', error);
    }
  };

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div>错误: {error}</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">持久化标签管理</h2>
      
      <button 
        onClick={handleCreateGroup}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded"
      >
        创建标签组
      </button>

      <div className="space-y-4">
        {tagGroups.map((group) => (
          <div key={group.id} className="border p-4 rounded">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold">{group.title}</h3>
              <div className="space-x-2">
                <button 
                  onClick={() => handleCreateTag(group.id)}
                  className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                >
                  添加标签
                </button>
                <button 
                  onClick={() => handleUpdateGroup(group.id)}
                  className="px-3 py-1 bg-yellow-500 text-white rounded text-sm"
                >
                  更新组名
                </button>
                <button 
                  onClick={() => handleDeleteGroup(group.id)}
                  className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                >
                  删除组
                </button>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {group.tags.map((tag) => (
                <span 
                  key={tag.id} 
                  className="px-2 py-1 bg-gray-200 rounded text-sm"
                >
                  {tag.name}
                </span>
              ))}
              {group.tags.length === 0 && (
                <span className="text-gray-500 text-sm">暂无标签</span>
              )}
            </div>
          </div>
        ))}
      </div>

      {tagGroups.length === 0 && (
        <div className="text-center text-gray-500 mt-8">
          暂无标签组，请创建一个新的标签组开始使用
        </div>
      )}
    </div>
  );
};

export default PersistentTagUsage;
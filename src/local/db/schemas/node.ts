import {RxJsonSchema} from 'rxdb';

export interface NodeDocType {
    id: string; // 节点ID
    wid: string; // 工作区ID
    title: string; // 标题
    content: string; // 内容
    color: string; // 颜色
    position_x: number; // X坐标
    position_y: number; // Y坐标
    width?: number; // 节点宽度
    height?: number; // 节点高度
    create_at: number; // 创建时间
    update_at: number; // 更新时间
}

export const nodeSchema: RxJsonSchema<NodeDocType> = {
    title: 'node schema',
    version: 0,
    description: '节点模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        title: {
            type: 'string',
            maxLength: 100
        },
        content: {
            type: 'string'
        },
        color: {
            type: 'string',
            maxLength: 10
        },
        position_x: {
            type: 'number'
        },
        position_y: {
            type: 'number'
        },
        width: {
            type: 'number',
            minimum: 0
        },
        height: {
            type: 'number',
            minimum: 0
        },
        create_at: {
            type: 'integer',
            minimum: 0,
        },
        update_at: {
            type: 'integer',
            minimum: 0,
        }
    },
    required: ['id', 'wid', 'color'],
    indexes: ['wid']
}; 
import styled from "styled-components";
import { Input } from "antd";
import { useState, useEffect } from "react";
import { SearchIcon, CloseIcon } from "@/components/icons";
import { ProjectType } from "../response-type";
import { useHomeStore } from "@/store/home-store";
import Nodata from "../../../assets/images/nodata.png";
const SearchContainer = styled(Input)`
  padding-left: 18px;
  height: 60px;
  border: 1px solid var(--bg-logo-color) !important;
  &:has(input:focus) {
    border: 1px solid var(--text-primary) !important;
  }
  &:has(input:hover) {
    background-color: var(--bg-search-hover) !important;
  }
  &.ant-input::placeholder {
    color: var(--text-primary);
    font-size: 18px;
  }
  box-shadow: 0 2px 6px 0 rgba(61, 86, 186, 0.1);
`;

const SearchResultList = styled.div`
  position: absolute;
  top: 62px;
  left: 0;
  width: 100%;
  max-height: 312px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border: 1px solid var(--bg-logo-color);
  border-radius: 8px;
  box-shadow: 0 2px 6px 0 rgba(61, 86, 186, 0.4);
  z-index: 1000;
  padding: 10px;
`;

const SearchResultItem = styled.div`
  width: 100%;
  height: 36px;
  line-height: 36px;
  padding: 0 10px;
  &:nth-child(n + 2):hover {
    cursor: pointer;
    background: var(--bg-category-tabs-hover);
    border-radius: 8px;
  }
`;

export const SearchInput = ({}) => {
  const [searchValue, setSearchValue] = useState("");
  const [showSearchList, setShowSearchList] = useState(false);
  const {
    projects,
    setSearching,
    setCurrentCategory,
    setSearchList,
    setSelectCategoryId,
  } = useHomeStore();
  const [searchSelectResult, setSearchSelectResult] = useState<ProjectType[]>(
    []
  );
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const searchContainer = document.querySelector(".search-container");
      if (searchContainer && !searchContainer.contains(event.target as Node)) {
        setShowSearchList(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const temp = (value: string) => {
    return projects.filter((item) =>
      item.title.toLowerCase().includes(value.trim().toLowerCase())
    );
  };
  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (value.trim() !== "") {
      const filtered = temp(value);
      setSearchSelectResult(filtered);
      setShowSearchList(true);
    } else {
      setSearchSelectResult(projects);
    }
  };
  const onfilterProject = (value = searchValue) => {
    setSearching(true);
    const filtered = temp(value);
    if (value.trim() !== "") {
      setCurrentCategory({
        cid: "search",
        cname: "搜索结果",
        wnum: filtered.length,
      });
    }
    setSearchList(filtered);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      if (searchValue.trim() !== "") {
        onfilterProject();
      } else {
        setSearching(false);
        setCurrentCategory({
          cid: "-1",
          cname: "全部项目",
          wnum: projects.length,
        });
        setSelectCategoryId("-1");
        setSearchList(projects);
      }
      setShowSearchList(false);
    }
  };

  const handleSuffixClick = () => {
    onfilterProject();
    setShowSearchList(false);
    if (searchValue.trim() === "") {
      setSearching(false);
      setCurrentCategory({
        cid: "-1",
        cname: "全部项目",
        wnum: projects.length,
      });
      setSelectCategoryId("-1");
      setSearchList(projects);
    }
  };

  return (
    <div className="flex-1 min-w-[600px] max-w-[1000px] search-container relative ">
      <SearchContainer
        className={`w-full h-full rounded-[30px] ${
          searchValue !== "" ? "bg-[var(--bg-search-hover)]" : ""
        }`}
        value={searchValue}
        onChange={(e) => handleSearch(e.target.value)}
        onFocus={() => {
          if (searchValue !== "") {
            setShowSearchList(true);
          }
        }}
        onKeyDown={handleKeyDown}
        suffix={
          <div className="cursor-pointer h-[25px]  rounded-full text-center flex gap-2.5 items-center justify-center hover:text-white">
            {searchValue.trim() !== "" && (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  setSearchValue("");
                  setShowSearchList(false);
                }}
              >
                <CloseIcon
                  size={28}
                  color="var(--border-color)"
                  className="bg-[var(--text-secondary)] rounded-full"
                />
              </div>
            )}
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleSuffixClick();
              }}
            >
              <SearchIcon size={28} color="var(--text-secondary)" />
            </div>
          </div>
        }
        placeholder="全部项目下搜索"
      />
      {showSearchList &&
        (searchSelectResult.length > 0 ? (
          <SearchResultList className="scrollbar-custom">
            <SearchResultItem className="text-[#A2A3A3]">
              全部项目（
              {searchSelectResult.length}）
            </SearchResultItem>
            {searchSelectResult.map((item) => (
              <SearchResultItem
                key={item.wid}
                onClick={() => {
                  handleSearch(item.title);
                  onfilterProject(item.title);
                  setShowSearchList(false);
                }}
              >
                {(() => {
                  const title = item.title;
                  if (
                    title.toLowerCase().startsWith(searchValue.toLowerCase())
                  ) {
                    return (
                      <>
                        <span className="bg-[#E6E8F7] text-[#3D56BA]">
                          {title.slice(0, searchValue.length)}
                        </span>
                        {title.slice(searchValue.length)}
                      </>
                    );
                  }
                  return title;
                })()}
              </SearchResultItem>
            ))}
          </SearchResultList>
        ) : (
          <SearchResultList className="flex flex-col items-center justify-center">
            <img src={Nodata} alt="" width={62} height={64} />
            <div className="text-[#040505]">暂无数据</div>
          </SearchResultList>
        ))}
    </div>
  );
};

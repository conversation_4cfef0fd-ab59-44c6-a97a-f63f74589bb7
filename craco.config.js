const path = require("path");

module.exports = {
  webpack: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
    configure: (webpackConfig) => {
      // 配置 SVG 处理
      const fileLoaderRule = webpackConfig.module.rules.find((rule) =>
        rule.test?.test?.(".svg")
      );
      if (fileLoaderRule) {
        fileLoaderRule.exclude = /\.svg$/;
      }

      webpackConfig.module.rules.push({
        test: /\.svg$/,
        use: ["@svgr/webpack", "url-loader"],
      });

      return webpackConfig;
    },
  },
};

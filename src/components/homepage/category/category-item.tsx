import styled from "styled-components";
import { Space } from "antd";
import { CategoryType } from "../response-type";
import { useHomeStore } from "@/store/home-store";
const TabWrapper = styled.div<{ $isSticky?: boolean; $isPinned?: boolean }>`
  flex-shrink: 0;
  ${(props) =>
    props.$isSticky &&
    props.$isPinned &&
    `
    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 0;
      right: 0;
      height: 10px;
      background: linear-gradient(var(--bg-category-tabs), transparent);
      pointer-events: none;
      z-index: 20;
    }
    box-shadow: 0 3px 5px -1px #000;
  `}
`;
const Tab = styled.div<{ $isSticky?: boolean }>`
  width: 14.4375rem;
  height: 2.5rem;

  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.375rem;
  cursor: pointer;
  position: relative;
  color: black;
  z-index: 10;
  transition: all 0.2s ease;
  padding: 0 15px;
  letter-spacing: 0.7px; // 添加字间距
  .ant-space-item:last-child {
    font-size: 14px; // 调整这里的数值
    line-height: 1.5;
  }
  &:hover {
    background: linear-gradient(
      to right,
      var(--bg-category-tabs-hover) 0%,
      var(--bg-category-tabs-hover) 40%,
      transparent 100%
    );
    color: var(--text-primary);
  }
  &.active {
    background: linear-gradient(
      to right,
      var(--bg-category-tabs-hover) 0%,
      var(--bg-category-tabs-hover) 40%,
      transparent 100%
    );
    color: var(--text-primary);
    &::before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -7px;
      width: 4px;
      border-radius: 2px;
      height: 60%;
      background: var(--text-primary);
    }
  }
`;
type Props = {
  category: CategoryType;
  index: number;
  icon?: React.ReactNode | null;
  onEdit?: (category: CategoryType) => void;
};
export const CategoryItem = ({ category, index, icon, onEdit }: Props) => {
  const {
    searching,
    projects,
    selectCategoryId,
    setSearching,
    setSelectCategoryId,
    setCurrentCategory,
    setSearchList,
  } = useHomeStore();
  return (
    <TabWrapper
      className={`w-full px-5 ${index === 0 ? "sticky top-0 z-20" : ""}`}
      onClick={() => {
        setSearching(false);
        const result = projects.filter((item) => {
          if (category.cid === "-1") return true;
          return item.cid == category.cid;
        });
        setSelectCategoryId(category.cid);
        setCurrentCategory({
          cid: category.cid,
          cname: category.cname,
          wnum: result.length,
        });
        setSearchList(result);
      }}
      onDoubleClick={() => {
        if (index > 1) {
          onEdit && onEdit(category);
        }
      }}
    >
      <Tab
        className={`${
          category.cid === selectCategoryId && !searching ? "active" : ""
        }`}
      >
        <Space className="flex-1 flex items-center">
          <div className="w-6 flex justify-center items-center">{icon}</div>
          <div className="w-52 truncate text-ellipsis">
            {category.cid === "-1" ? "全部项目" : category.cname}
          </div>
        </Space>
        <div
          className="text-[12px] font-[100] -ml-10 "
          style={{
            letterSpacing: "0.6px",
          }}
        >
          （{category.wnum}）
        </div>
      </Tab>
    </TabWrapper>
  );
};

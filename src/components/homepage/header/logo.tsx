import styled from "styled-components";

const LogoContainer = styled.div`
  display: flex;
  flex-shrink: 0;
  align-items: center;
  gap: 1.4375rem;
  height: 5.3125rem;
`;

export const Logo = () => {
  return (
    <LogoContainer>
      <div className="w-[3.4375rem] h-[3.4375rem] text-center leading-[3.4375rem] rounded-[1rem] bg-[var(--bg-logo-color)]">
        logo
      </div>
      <div className="text-[var(--text-primary)] text-[1.5rem]">ThinkerAI</div>
    </LogoContainer>
  );
};

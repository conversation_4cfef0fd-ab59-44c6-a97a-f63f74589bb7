import React from "react";

const style = {
    display: "flex",
    justifyContent: "space-around",
    maxWidth: 800,
    margin: "0 auto",
    padding: 60,
};
const Display = React.memo(
    (props) => {
        console.log("Display");
        return <pre>{JSON.stringify(props.data, null, 2)}</pre>;
    },
    (prev, next) => {
        return JSON.stringify(prev) === JSON.stringify(next);
    }
);
const Count = React.memo((props) => {
    console.log("count");
    return <p>{props.data}</p>;
});

// Anonymous
export default class extends React.Component {
    state = {
        count: 0,
    };
    handleAdd = () => {
        this.setState({
            count: this.state.count + 1,
        });
    };
    onChange = (key) => (e) => {
        this.setState({
            [key]: e.target.value,
        });
    };
    render() {
        const { text, password, count } = this.state;
        return (
            <div>
                <div style={style}>
                    <div>
                        <input type="text" className="border-2" value={text || ""} onChange={this.onChange("text")} />
                        <br />
                        <br />
                        <input type="text" className="border-2" value={password || ""} onChange={this.onChange("password")} />
                    </div>
                    <Display data={{ text, password }} />
                </div>
                <div align="center">
                    <Count data={count} />
                    <button className="border-2" onClick={this.handleAdd}>add</button>
                </div>
            </div>
        );
    }
}

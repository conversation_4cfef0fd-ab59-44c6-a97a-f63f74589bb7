import { useCallback, useRef } from 'react';
import { WorkspaceService } from '@/local/services/workspace-service';
import { usePdfStore } from '@/store/pdf-store';

// 创建 WorkspaceService 实例
const workspaceService = new WorkspaceService();

interface UseFileUploadProps {
  currentWorkspaceId: string;
  onUploadComplete?: (targetFolderId?: string) => void;
  onUploadError?: (error: string) => void;
}

export const useFileUpload = ({ 
  currentWorkspaceId, 
  onUploadComplete, 
  onUploadError 
}: UseFileUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { addPdfs } = usePdfStore(state => ({
    addPdfs: state.addPdfs,
  }));

  // 创建文件输入元素
  const createFileInput = useCallback(() => {
    if (!fileInputRef.current) {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'application/pdf';
      input.multiple = true;
      input.style.display = 'none';
      document.body.appendChild(input);
      fileInputRef.current = input;
    }
    return fileInputRef.current;
  }, []);

  // 清理文件输入元素
  const cleanupFileInput = useCallback(() => {
    if (fileInputRef.current) {
      document.body.removeChild(fileInputRef.current);
      fileInputRef.current = null;
    }
  }, []);

  // 上传文件到指定文件夹
  const uploadFiles = useCallback(async (files: File[], targetFolderId: string = 'root') => {
    if (!files.length || !currentWorkspaceId) {
      const error = '没有选择文件或未找到工作区ID';
      onUploadError?.(error);
      return { success: false, message: error };
    }

    try {
      const uploadedFiles = [];
      const pdfData = new Map();
      
      // 处理每个文件
      for (const file of files) {
        try {
          // 检查文件类型
          if (file.type !== 'application/pdf') {
            console.warn(`文件 ${file.name} 不是PDF文件，已跳过`);
            continue;
          }
          
          // 保存文件到IndexedDB
          const result = await workspaceService.uploadFiles({
            wid: currentWorkspaceId,
            files: [file],
            parent_id: targetFolderId
          });
          
          // 获取上传结果中的第一个文件
          if (result.list.length > 0) {
            const fileResult = result.list[0];
            
            // 添加到PDF Store
            pdfData.set(fileResult.aid, {
              aid: fileResult.aid,
              filename: file.name,
              url: fileResult.url
            });
            
            uploadedFiles.push({
              id: fileResult.aid,
              name: file.name,
              size: file.size,
              type: file.type
            });
          } else {
            throw new Error(`文件 ${file.name} 上传失败`);
          }
        } catch (error) {
          console.error(`上传文件 ${file.name} 失败:`, error);
          onUploadError?.(`上传文件 ${file.name} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      // 如果有成功上传的文件
      if (uploadedFiles.length > 0) {
        // 添加到PDF Store
        if (pdfData.size > 0) {
          addPdfs(pdfData);
        }
        
        onUploadComplete?.();
        
        return { 
          success: true, 
          message: `成功上传 ${uploadedFiles.length} 个文件`,
          files: uploadedFiles
        };
      } else {
        const error = '没有成功上传任何文件';
        onUploadError?.(error);
        return { success: false, message: error };
      }
    } catch (error) {
      const errorMessage = `上传文件失败: ${error instanceof Error ? error.message : '未知错误'}`;
      console.error(errorMessage, error);
      onUploadError?.(errorMessage);
      return { success: false, message: errorMessage };
    }
  }, [currentWorkspaceId, addPdfs, onUploadComplete, onUploadError]);

  // 触发文件选择对话框
  const triggerFileSelect = useCallback((targetFolderId: string = 'root') => {
    return new Promise<{ files: File[]; targetFolderId: string } | null>((resolve) => {
      const fileInput = createFileInput();
      
      // 设置一次性事件监听器
      const handleFileSelect = (e: Event) => {
        const files = (e.target as HTMLInputElement).files;
        if (files && files.length > 0) {
          resolve({ 
            files: Array.from(files), 
            targetFolderId 
          });
        } else {
          resolve(null);
        }
        
        // 清理事件监听器
        fileInput.removeEventListener('change', handleFileSelect);
        // 重置文件输入
        fileInput.value = '';
      };
      
      fileInput.addEventListener('change', handleFileSelect);
      fileInput.click();
    });
  }, [createFileInput]);

  // 完整的文件上传流程
  const uploadFilesToFolder = useCallback(async (targetFolderId: string = 'root') => {
    const result = await triggerFileSelect(targetFolderId);
    if (result) {
      const uploadResult = await uploadFiles(result.files, result.targetFolderId);
      
      // 上传完成后调用回调，传递目标文件夹ID
      if (uploadResult.success && onUploadComplete) {
        onUploadComplete(targetFolderId);
      }
      
      return uploadResult;
    }
    return { success: false, message: '未选择文件' };
  }, [triggerFileSelect, uploadFiles, onUploadComplete]);

  return {
    uploadFiles,
    uploadFilesToFolder,
    triggerFileSelect,
    cleanupFileInput
  };
}; 
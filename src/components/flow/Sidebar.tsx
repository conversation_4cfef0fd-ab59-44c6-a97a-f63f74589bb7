import React, { useState } from "react";
import styled from "styled-components";
import { HomeOutlined, createFromIconfontCN } from "@ant-design/icons";
import { iconfont_url } from "@/common/constant";
import TagIcon from "@/assets/icons/tag.svg?react";
import { TagManagementModal } from "./TagManagementModal";

const IconFont = createFromIconfontCN({
  scriptUrl: iconfont_url,
});

const SidebarContainer = styled.div`
  width: 64px;
  height: 100%;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 24px;
  box-shadow: 2px 0 8px rgba(0,0,0,0.04);
  z-index: 100;
  position: relative;
`;

const SidebarButton = styled.button<{ active?: boolean }>`
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  border: none;
  background: ${({ active }) => (active ? "#e6f7ff" : "transparent")};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 22px;
  &:hover {
    background: #e6f7ff;
  }
`;

export type SidebarEventType = 'home' | 'note' | 'chat' | 'pdf' | 'tag'
export interface SidebarProps {
  active: SidebarEventType;
  onSelect: (type: SidebarEventType) => void;
  noteOpen?: boolean;
  chatOpen?: boolean;
  pdfOpen?: boolean;
  tagOpen?: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({ active, onSelect, noteOpen, chatOpen, pdfOpen,tagOpen }) => (
  <SidebarContainer>
    <SidebarButton active={active === "home"} onClick={() => onSelect("home")}>
      <HomeOutlined />
    </SidebarButton>
    <SidebarButton active={noteOpen} onClick={() => onSelect("note")}> 
      <IconFont type="icon-note" />
    </SidebarButton>
    <SidebarButton active={chatOpen} onClick={() => onSelect("chat")}> 
      <IconFont type="icon-chat" />
    </SidebarButton>
    <SidebarButton active={pdfOpen} onClick={() => onSelect("pdf")}> 
      <IconFont type="icon-pdf" />
    </SidebarButton>
    <SidebarButton active={tagOpen} onClick={() => onSelect('tag')}>
      <TagIcon />
    </SidebarButton>
  </SidebarContainer>
);
import {useCallback} from 'react';
import {useWorkerSpaceStore} from '@/store/workerspace-store/store';
// import { useNotebookStore } from '@/components/notepad/store';
import {message} from 'antd';
import {usePanelOpenStore} from "@/store/panel-open-store.tsx";
import {useNoteStore} from "@/store/note-store.ts";

/**
 * 自定义Hook，用于处理聊天内容与记事本的交互
 * 提供将聊天回答内容追加到notepad的功能
 */
export const useChatPad = () => {
    // 获取当前工作区ID
    const wid = useWorkerSpaceStore(state => state.wid);
    const {toggleNotePanel} = usePanelOpenStore((state) => ({
        toggleNotePanel: state.toggleNotePanel,
    }));
    const {setInsertContent} = useNoteStore((state) => ({
        setInsertContent: state.setInsertContent
    }))
    /**
     * 将聊天回答内容追加到notepad
     * @param answer 聊天回答内容
     */
    const importChatToNotepad = useCallback((answer: string) => {
        if (!wid) {
            message.error('工作区ID不存在');
            return;
        }

        // 显示笔记本面板
        const currentNoteState = usePanelOpenStore.getState().notePanelOpen
        if (!currentNoteState) {
            toggleNotePanel(true)
        }
        setInsertContent(answer)
    }, [wid]);

    return {
        importChatToNotepad
    };
};
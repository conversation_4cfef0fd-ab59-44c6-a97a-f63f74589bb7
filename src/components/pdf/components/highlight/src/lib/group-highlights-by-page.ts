import {GhostHighlight, Highlight} from "../types";

type GroupedHighlights = {
    [pageNumber: number]: Array<Highlight | GhostHighlight>;
};

const groupHighlightsByPage = (
    highlights: Array<Highlight | GhostHighlight | null>,
): GroupedHighlights =>
    highlights.reduce<GroupedHighlights>((acc, highlight) => {
        if (!highlight) {
            return acc;
        }
        // 检查 position、boundingRect 和 rects 是否存在，避免 undefined 错误
        const pageNumbers: number[] = [];
        if (highlight.position?.boundingRect?.pageNumber !== undefined) {
            pageNumbers.push(highlight.position.boundingRect.pageNumber);
        }
        if (Array.isArray(highlight.position?.rects)) {
            pageNumbers.push(
                ...highlight.position.rects.map((rect) => rect.pageNumber || 0)
            );
        }

        pageNumbers.forEach((pageNumber) => {
            acc[pageNumber] ||= [];
            const pageSpecificHighlight = {
                ...highlight,
                position: {
                    ...highlight.position,
                    rects: Array.isArray(highlight.position?.rects)
                        ? highlight.position.rects.filter(
                            (rect) => pageNumber === rect.pageNumber,
                        )
                        : [],
                },
            };
            acc[pageNumber].push(pageSpecificHighlight);
        });

        return acc;
    }, {});

export default groupHighlightsByPage;

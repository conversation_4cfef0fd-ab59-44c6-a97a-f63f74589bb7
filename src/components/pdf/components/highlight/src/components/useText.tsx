import { usePdfStore } from "@/store/pdf-store";
import { viewportToScaled } from "@/components/pdf/components/highlight/src/lib/coordinates";
import { asElement, getPageFromElement } from "../lib/pdfjs-dom";
import { forwardRef, useImperativeHandle, useState, useRef, useEffect, MouseEvent } from "react";
import { ScaledPosition, usePdfHighlighterContext } from "..";
import { PDF_CLASS_NAME } from "./PdfHighlighter";
import { usePdfInsertText } from "@/components/pdf/hooks/pdf-insert";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";
import { useHighlightAdd } from "../../../../hooks/highlight-add";
import { getContainerCoords } from "./MouseSelection";

export const TextInsert = forwardRef<any, any>((props, ref) => {

    const [area, setArea] = useState<{
        left: number,
        top: number,
        width: number,
        height: number,
        open: boolean,
        pageNumber: number,
        pageY: number,
        pageX: number,
    }>({
        left: 0,
        top: 0,
        width: 100,
        height: 100,
        open: false,
        pageNumber: 1,  
        pageY: 0,
        pageX: 0,
    })
    const pdfHighlighterUtils = usePdfHighlighterContext()
    const { pdfs, activeAid } = usePdfStore();
    const { addHighlight } = useHighlightAdd()
    const { onInsertText } = usePdfInsertText()
    const { open } = area
    const wid = useWorkerSpaceStore((state) => state.wid)

    useImperativeHandle(ref, () => ({
        handleMouseDown(e: React.MouseEvent) {
            if (!props?.parentRef) return

            const container = asElement(props?.parentRef?.current)
            if (open) {
                setArea((pre) => ({
                    ...pre,
                    open: false
                }));
                return
            }

            // 如果未打开，则打开并设置left/top为鼠标在PDF容器内的位置
            if (!open) {
                // 优先通过 pdfHighlighterUtils 获取 PDF 容器
                const viewer = pdfHighlighterUtils?.getViewer();
                // const pdfContainer = viewer?.container as HTMLElement;

                if (!viewer) return

                // 可以通过这个元素获取到，最精准的pageNumber
                const pageElement = (e?.target as HTMLElement)?.closest('.page') as HTMLElement;
                const pageNumber = Number(pageElement?.getAttribute('data-page-number'))

                // const pdfDomRoot = (e?.target as HTMLElement)?.closest(`.${PDF_CLASS_NAME}`);
                // const clickpageView = viewer.getPageView(pageNumber - 1);
                const scroll = {
                    offsetTop: pageElement?.offsetTop || 0,
                    offsetLeft: pageElement?.offsetLeft || 0,
                }

                const { x, y } = getContainerCoords(container, e.clientX, e.clientY)

                setArea((pre) => ({
                    ...pre,
                    open: true,
                    left: x,
                    top: y,
                    pageNumber,
                    pageY: y - scroll.offsetTop,
                    pageX: x - scroll.offsetLeft,
                }));
                return;
            }
        }
    }))


    // 修复类型错误，e 应为 React.FocusEvent<HTMLTextAreaElement>
    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        if (e.target.value.trim() === "") {
            setArea((pre) => {
                return {
                    ...pre,
                    open: false
                }
            })
            return
        }


        const viewer = pdfHighlighterUtils?.getViewer();
        if (!viewer) return
        const pageView = viewer?.getPageView(area.pageNumber - 1);

        // 创建一个虚拟的高亮区域，使用相对于页面的坐标
        const { pageX: left, pageY: top } = area
        const x1 = left
        const y1 = top

        const x2 = x1 + 100
        const y2 = y1 + 100
        const pageNumber = area.pageNumber

        const rect = {
            x1, y1, x2, y2,
            width: pageView.viewport.width,
            height: pageView.viewport.height,
            pageNumber
        }

        const position: ScaledPosition = {
            boundingRect: rect,
            rects: [rect],
            usePdfCoordinates: false
        };

        const text = e.target.value
        const type = "text-insert"

        const inserData = {
            wid,
            aid: activeAid,
            text,
            mark: JSON.stringify({
                ...position,
                type,
                content: {
                    text
                }
            }),
        } as any

        // 这个方法会同步到flow-节点
        // addHighlight(
        //     {
        //         aid: activeAid,
        //         type: "text-insert",
        //         content: {
        //             text: e.target.value
        //         },
        //         position
        //     } as any
        // )

        onInsertText(activeAid, inserData, {
            position,
            content: {
                text
            },
            type,
            aid: activeAid,
        })

        setArea((pre) => {
            return {
                ...pre,
                open: false
            }
        })

    }

    if (!area.open) return null

    return <div
        className="text-insert-container absolute z-[9999] pointer-events-none"
        style={{
            left: area.left,
            top: area.top,
        }}
        onMouseDown={(e) => {
            e.stopPropagation()
        }}
        onPointerDown={(e) => {
            e.stopPropagation()
        }}
        onClick={(e) => {
            e.stopPropagation()
        }}
    >
        <TextArea onBlur={handleBlur} />
    </div>
})

interface TextAreaProps {
    onBlur: React.FocusEventHandler<HTMLTextAreaElement>;
}

const TextArea = ({
    onBlur,

}: TextAreaProps) => {

    const [text, setText] = useState("");

    return <textarea
        value={text}
        onChange={e => setText(e.target.value)}
        className="w-[200px] h-[100px] border border-gray-300 rounded p-2 bg-white/50 focus:bg-white/70 resize-none pointer-events-auto"
        style={{ backdropFilter: 'blur(2px)' }}
        cols={30} rows={10}
        onBlur={(e) => {
            onBlur(e)
            setText(e.target.value)
        }}
    />
}
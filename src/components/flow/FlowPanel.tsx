import {Edge, MiniMap, Panel, ReactFlow, ReactFlowProvider} from "@xyflow/react";
import {useCallback, useEffect} from "react";
import {nodeTypes, useFlowStore} from "@/store/flow-store.ts";
import {DevTools} from "@/components/flow/DevTools.tsx";
import {useFetchData} from "@/components/flow/hooks/fetch-data.tsx";
import {useNodeDrag} from "@/components/flow/hooks/node-drag.ts";
import {useEdgeConnect} from "@/components/flow/hooks/edge-connect.ts";
import {useViewport} from "@/components/flow/hooks/flow-viewport.ts";
import {useEdgeDelete} from "@/components/flow/hooks/edge-delete.ts";
import {useNodeDelete} from "@/components/flow/hooks/node-delete.ts";
import {useFlowSelection} from "@/components/flow/hooks/flow-selection.ts";
import {FlowBar} from "@/components/flow/FlowBar.tsx";
import '@xyflow/react/dist/style.css';
import './style/index.css';
import {useNodeClick} from "@/components/flow/hooks/node-click.ts";
import {useNodeContextMenu} from "@/components/flow/hooks/node-context-menu";
import {useTabAddNode} from "./hooks/node-tab";
import {HelperLinesRenderer} from "./node/HelperLinesRenderer";
import {EditNode} from "./node/EditNode";
import {useShiftUserSelect} from "@/components/flow/hooks/user-select";
import {PanelPosition} from "@/pages/workspace/Panel.ts";
import EditableEdge from "./edges/EditableEdge";
import {useCopyPaste} from "./hooks/copy-paste";
import {useKeyboardShortcuts} from "./hooks/keyboard-shortcuts";
import {useMousePosition} from "./hooks/mouse-position";
// 导入新的组件和hook
import {useEdgePopup} from "./edges/use-edge-popup";
import EdgePopup from "./edges/EdgePopup";
import {NodeEvents} from "@/local/services/node-service";

// 定义边类型
const edgeTypes = {
    editableEdge: EditableEdge,
};

interface FlowProps {
    panelPosition: PanelPosition
}

function FlowPanel({panelPosition}: FlowProps) {
    return (
        <ReactFlowProvider>
            <Flow panelPosition={panelPosition}/>
        </ReactFlowProvider>
    );
}

function Flow({panelPosition}: FlowProps) {
    const {nodes, edges, onNodesChange, onEdgesChange, onViewportChange, deleteNodes} = useFlowStore()
    // 初始化
    useFetchData()
    // 节点删除
    const {onNodesDelete} = useNodeDelete()
    // 节点拖拽
    const {onNodeDrag, onNodeDragStop, helperLines} = useNodeDrag()
    // 节点点击
    const {onNodeClick} = useNodeClick()
    // 连线
    const {isValidConnection, onConnectStart, onConnect, connectionLineStyle} = useEdgeConnect()
    // 边删除
    const {onEdgesDelete} = useEdgeDelete()
    // 选择
    const {onSelectionChange} = useFlowSelection()
    // 视图
    useViewport()
    useShiftUserSelect()
    useTabAddNode()
    const {onNodeContextMenu, ContextMenuDom, TagModalDom} = useNodeContextMenu()

    // 边弹框功能
    const {
        selectedEdge,
        popupPosition,
        onEdgeClick,
        closePopup,
        onPaneClick,
    } = useEdgePopup();

    // 监听IndexedDB节点删除事件，同步更新画布
    useEffect(() => {
        const handleNodesDeleted = (nodeIds: string[]) => {
            console.log('收到节点删除事件，删除画布上的节点:', nodeIds);
            // 从画布上删除这些节点
            deleteNodes(nodeIds);
        };

        // 监听节点删除事件
        NodeEvents.on('nodesDeleted', handleNodesDeleted);

        // 清理监听器
        return () => {
            NodeEvents.off('nodesDeleted', handleNodesDeleted);
        };
    }, [deleteNodes]);

    // 复制粘贴功能
    const {
        copy, paste,
        // cut, 
        deleteSelected,
        selectAll,
        // hasClipboard, clipboardCount 
    } = useCopyPaste()

    // 鼠标位置追踪
    const {mousePosition, flowWrapperRef, handleMouseMove} = useMousePosition()

    // 粘贴到鼠标位置
    const pasteAtMousePosition = useCallback(() => {
        return paste(mousePosition);
    }, [paste, mousePosition]);

    // 启用键盘快捷键
    useKeyboardShortcuts({
        onCopy: copy,
        onPaste: pasteAtMousePosition,
        // onCut: cut,
        onDelete: deleteSelected,
        onSelectAll: selectAll,
        enabled: true,
    });

    const onEdgeDoubleClick = useCallback((event: React.MouseEvent, edge: Edge) => {
        // 设置当前边为编辑状态
        const {setEditingEdgeId} = useFlowStore.getState();
        setEditingEdgeId(edge.id);
    }, [])

    return (
        <>
            <div
                ref={flowWrapperRef}
                className="w-full h-full relative"
                style={{
                    left: panelPosition.x,
                    width: panelPosition.width,
                }}
                onContextMenu={(e) => {
                    onNodeContextMenu?.(e, null)
                    e.stopPropagation()
                    e.preventDefault()
                }}
            >
                <ReactFlow
                    // 节点
                    onNodeContextMenu={onNodeContextMenu}
                    nodes={nodes}
                    onNodesChange={onNodesChange}
                    onNodeDrag={onNodeDrag}
                    onNodeDragStop={onNodeDragStop}
                    onNodesDelete={onNodesDelete}
                    onSelectionChange={onSelectionChange}
                    onNodeClick={onNodeClick}
                    nodeTypes={nodeTypes}
                    // 边
                    edges={edges}
                    onEdgesChange={onEdgesChange}
                    onEdgeClick={onEdgeClick} // 使用hook中的边点击事件
                    onEdgeDoubleClick={onEdgeDoubleClick}
                    onConnectStart={onConnectStart}
                    onConnect={onConnect}
                    onEdgesDelete={onEdgesDelete}
                    isValidConnection={isValidConnection}
                    connectionLineStyle={connectionLineStyle}
                    edgeTypes={edgeTypes}
                    // 面板事件
                    onPaneClick={onPaneClick} // 使用hook中的面板点击事件
                    // 视图
                    defaultViewport={{x: 100, y: 100, zoom: 1}}
                    minZoom={0.1}
                    onViewportChange={onViewportChange}
                    zoomOnDoubleClick={false}
                    nodesFocusable={false}
                    proOptions={{hideAttribution: true}}
                    style={{backgroundColor: "#F7F9FB"}}
                    // 复制粘贴相关事件
                    onPaneMouseMove={handleMouseMove}
                    multiSelectionKeyCode="Control"
                    selectionKeyCode="Shift"
                    deleteKeyCode="Delete"
                >
                    {import.meta.env.DEV && <DevTools position="top-left"/>}
                    <MiniMap/>
                    <Panel position="bottom-center">
                        <FlowBar/>
                    </Panel>

                    {
                        ContextMenuDom
                    }
                    {/* 标签管理弹窗 */}
                    {TagModalDom}
                </ReactFlow>
                {/* 辅助线渲染器 */}
                <HelperLinesRenderer helperLines={helperLines}/>
                {/* 全局编辑节点窗口 */}
                <EditNode/>
            </div>

            {/* 边弹框 */}
            {selectedEdge && (
                <EdgePopup
                    edge={selectedEdge}
                    position={popupPosition}
                    onClose={closePopup}
                />
            )}

        </>
    )
}

export default FlowPanel
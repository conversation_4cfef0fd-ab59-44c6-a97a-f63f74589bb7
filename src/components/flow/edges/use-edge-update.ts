import { updateEdgeStyle, updateEdgeData } from '@/api/canvas'
import { useFlowStore } from '@/store/flow-store';

export const useUpdateEdge = () => {

    const { setEdges, edges } = useFlowStore.getState();

    const updateEdge = async (eid: string, data: Record<string, any>) => {
        const style = {
            ...data
        }
        const res = await updateEdgeStyle({ eid, style })
        setEdges(edges.map(edge => edge.id === eid ? { ...edge, style: {
            ...edge.style,
            ...style
        } } : edge))
        return res
    }

    // 暂时没用到，后期考虑如何拆分
    const updateEdgeDataFn= async (eid: string, data: Record<string, any>) => {
        const res = await updateEdgeData({ eid, data })
        setEdges(edges.map(edge => edge.id === eid ? { ...edge, data: {
            ...edge.data,
            ...data
        } } : edge))
        return res
    }

    return { updateEdge, updateEdgeDataFn }
}